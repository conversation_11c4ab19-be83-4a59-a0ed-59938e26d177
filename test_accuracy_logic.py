#!/usr/bin/env python3
"""
Test script to verify the new accuracy calculation logic for single field strength batches
"""

import torch
import torch.nn.functional as F

def test_accuracy_logic():
    """Test the new accuracy calculation logic"""
    
    print("Testing accuracy calculation logic for single field strength batches...")
    
    # Test 1: 1.5T batch (label = 0.0)
    print("\n=== Test 1: 1.5T batch ===")
    field_strength_labels = torch.zeros(4, 1)  # 4 samples of 1.5T
    
    # Case 1a: Good discriminator (outputs negative logits for 1.5T)
    pred_field_strength_good = torch.tensor([[-2.0], [-1.5], [-1.0], [-0.5]])  # Negative logits
    probs_good = torch.sigmoid(pred_field_strength_good)
    accuracy_good = (1.0 - probs_good.mean()).item()
    print(f"Good discriminator (negative logits):")
    print(f"  Logits: {pred_field_strength_good.flatten().tolist()}")
    print(f"  Probabilities: {probs_good.flatten().tolist()}")
    print(f"  Accuracy: {accuracy_good:.3f}")
    
    # Case 1b: Bad discriminator (outputs positive logits for 1.5T)
    pred_field_strength_bad = torch.tensor([[0.5], [1.0], [1.5], [2.0]])  # Positive logits
    probs_bad = torch.sigmoid(pred_field_strength_bad)
    accuracy_bad = (1.0 - probs_bad.mean()).item()
    print(f"Bad discriminator (positive logits):")
    print(f"  Logits: {pred_field_strength_bad.flatten().tolist()}")
    print(f"  Probabilities: {probs_bad.flatten().tolist()}")
    print(f"  Accuracy: {accuracy_bad:.3f}")
    
    # Test 2: 3.0T batch (label = 1.0)
    print("\n=== Test 2: 3.0T batch ===")
    field_strength_labels = torch.ones(4, 1)  # 4 samples of 3.0T
    
    # Case 2a: Good discriminator (outputs positive logits for 3.0T)
    pred_field_strength_good = torch.tensor([[0.5], [1.0], [1.5], [2.0]])  # Positive logits
    probs_good = torch.sigmoid(pred_field_strength_good)
    accuracy_good = probs_good.mean().item()
    print(f"Good discriminator (positive logits):")
    print(f"  Logits: {pred_field_strength_good.flatten().tolist()}")
    print(f"  Probabilities: {probs_good.flatten().tolist()}")
    print(f"  Accuracy: {accuracy_good:.3f}")
    
    # Case 2b: Bad discriminator (outputs negative logits for 3.0T)
    pred_field_strength_bad = torch.tensor([[-2.0], [-1.5], [-1.0], [-0.5]])  # Negative logits
    probs_bad = torch.sigmoid(pred_field_strength_bad)
    accuracy_bad = probs_bad.mean().item()
    print(f"Bad discriminator (negative logits):")
    print(f"  Logits: {pred_field_strength_bad.flatten().tolist()}")
    print(f"  Probabilities: {probs_bad.flatten().tolist()}")
    print(f"  Accuracy: {accuracy_bad:.3f}")
    
    # Test 3: Mixed batch (for completeness)
    print("\n=== Test 3: Mixed batch ===")
    field_strength_labels = torch.tensor([[0.0], [1.0], [0.0], [1.0]])  # Mixed 1.5T and 3.0T
    
    pred_field_strength = torch.tensor([[-1.0], [1.0], [-0.5], [1.5]])  # Correct predictions
    pred_binary = (pred_field_strength > 0.0).float()
    accuracy = (pred_binary == field_strength_labels).float().mean().item()
    print(f"Mixed batch (correct predictions):")
    print(f"  Logits: {pred_field_strength.flatten().tolist()}")
    print(f"  Binary predictions: {pred_binary.flatten().tolist()}")
    print(f"  Labels: {field_strength_labels.flatten().tolist()}")
    print(f"  Accuracy: {accuracy:.3f}")
    
    print("\n✅ All accuracy logic tests completed!")

if __name__ == "__main__":
    test_accuracy_logic() 