#!/usr/bin/env python3
"""
分析discriminator的学习趋势
"""

import re
import matplotlib.pyplot as plt
import numpy as np

def extract_metrics_from_log(log_file):
    """从日志文件中提取discriminator指标"""
    steps = []
    discriminator_losses = []
    discriminator_accuracies = []
    stable_accuracies = []
    
    with open(log_file, 'r') as f:
        for line in f:
            # 提取步骤数
            step_match = re.search(r'(\d+)/24289', line)
            if step_match:
                step = int(step_match.group(1))
                steps.append(step)
            
            # 提取discriminator loss
            loss_match = re.search(r'train_metrics/discriminator_loss=([\d.]+)', line)
            if loss_match:
                loss = float(loss_match.group(1))
                discriminator_losses.append(loss)
            
            # 提取discriminator accuracy
            acc_match = re.search(r'train_metrics/discriminator_accuracy=([\d.]+)', line)
            if acc_match:
                acc = float(acc_match.group(1))
                discriminator_accuracies.append(acc)
            
            # 提取stable accuracy
            stable_match = re.search(r'Stable accuracy: ([\d.]+)', line)
            if stable_match:
                stable_acc = float(stable_match.group(1))
                stable_accuracies.append(stable_acc)
    
    return steps, discriminator_losses, discriminator_accuracies, stable_accuracies

def analyze_trends(steps, losses, accuracies, stable_accuracies):
    """分析学习趋势"""
    if not steps:
        print("没有找到数据")
        return
    
    print(f"总数据点: {len(steps)}")
    print(f"步骤范围: {min(steps)} - {max(steps)}")
    
    # 分析loss趋势
    if losses:
        print(f"\n=== Discriminator Loss 分析 ===")
        print(f"初始loss: {losses[0]:.4f}")
        print(f"最终loss: {losses[-1]:.4f}")
        print(f"Loss变化: {losses[-1] - losses[0]:.4f}")
        
        # 计算移动平均
        window = min(50, len(losses) // 10)
        if window > 1:
            moving_avg = np.convolve(losses, np.ones(window)/window, mode='valid')
            print(f"Loss移动平均趋势: {'下降' if moving_avg[-1] < moving_avg[0] else '上升'}")
    
    # 分析accuracy趋势
    if accuracies:
        print(f"\n=== Discriminator Accuracy 分析 ===")
        print(f"初始accuracy: {accuracies[0]:.4f}")
        print(f"最终accuracy: {accuracies[-1]:.4f}")
        print(f"Accuracy变化: {accuracies[-1] - accuracies[0]:.4f}")
        
        # 计算移动平均
        window = min(50, len(accuracies) // 10)
        if window > 1:
            moving_avg = np.convolve(accuracies, np.ones(window)/window, mode='valid')
            print(f"Accuracy移动平均趋势: {'上升' if moving_avg[-1] > moving_avg[0] else '下降'}")
    
    # 分析stable accuracy趋势
    if stable_accuracies:
        print(f"\n=== Stable Accuracy 分析 ===")
        print(f"初始stable accuracy: {stable_accuracies[0]:.4f}")
        print(f"最终stable accuracy: {stable_accuracies[-1]:.4f}")
        print(f"Stable accuracy变化: {stable_accuracies[-1] - stable_accuracies[0]:.4f}")
        
        # 计算移动平均
        window = min(50, len(stable_accuracies) // 10)
        if window > 1:
            moving_avg = np.convolve(stable_accuracies, np.ones(window)/window, mode='valid')
            print(f"Stable accuracy移动平均趋势: {'上升' if moving_avg[-1] > moving_avg[0] else '下降'}")
    
    # 学习效果评估
    print(f"\n=== 学习效果评估 ===")
    if accuracies and accuracies[-1] > 0.6:
        print("✅ 判别器学习效果良好 (accuracy > 0.6)")
    elif accuracies and accuracies[-1] > 0.55:
        print("⚠️ 判别器学习效果一般 (accuracy > 0.55)")
    else:
        print("❌ 判别器学习效果较差 (accuracy < 0.55)")
    
    if losses and losses[-1] < 0.5:
        print("✅ 判别器损失较低 (loss < 0.5)")
    elif losses and losses[-1] < 1.0:
        print("⚠️ 判别器损失中等 (loss < 1.0)")
    else:
        print("❌ 判别器损失较高 (loss > 1.0)")

def plot_trends(steps, losses, accuracies, stable_accuracies):
    """绘制趋势图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制loss
    if losses and steps:
        ax1.plot(steps[:len(losses)], losses, 'b-', alpha=0.7, label='Discriminator Loss')
        ax1.set_ylabel('Loss')
        ax1.set_title('Discriminator Loss Trend')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
    
    # 绘制accuracy
    if accuracies and steps:
        ax2.plot(steps[:len(accuracies)], accuracies, 'r-', alpha=0.7, label='Discriminator Accuracy')
    if stable_accuracies and steps:
        ax2.plot(steps[:len(stable_accuracies)], stable_accuracies, 'g-', alpha=0.7, label='Stable Accuracy')
    
    ax2.set_xlabel('Training Steps')
    ax2.set_ylabel('Accuracy')
    ax2.set_title('Discriminator Accuracy Trend')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('discriminator_trends.png', dpi=300, bbox_inches='tight')
    print("趋势图已保存为 discriminator_trends.png")

if __name__ == "__main__":
    log_file = "logs/cmr_fixed_domain_adapt_1811278.out"
    
    print("分析discriminator学习趋势...")
    steps, losses, accuracies, stable_accuracies = extract_metrics_from_log(log_file)
    
    analyze_trends(steps, losses, accuracies, stable_accuracies)
    plot_trends(steps, losses, accuracies, stable_accuracies) 