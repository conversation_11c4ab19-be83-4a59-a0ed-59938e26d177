#!/usr/bin/env python3
"""
Analyze discriminator loss trends and statistics from training logs.
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import re

def moving_average(data, window_size):
    """Calculate moving average without pandas."""
    weights = np.ones(window_size) / window_size
    return np.convolve(data, weights, mode='valid')

def extract_discriminator_data(log_file):
    """Extract discriminator loss and accuracy from log file."""
    losses = []
    accuracies = []
    epochs = []
    steps = []
    
    with open(log_file, 'r') as f:
        for line in f:
            # Look for discriminator loss lines
            if "DEBUG: Discriminator loss:" in line:
                # Extract loss and accuracy
                loss_match = re.search(r'Discriminator loss: ([\d.]+)', line)
                acc_match = re.search(r'Accuracy: ([\d.]+)', line)
                
                if loss_match and acc_match:
                    losses.append(float(loss_match.group(1)))
                    accuracies.append(float(acc_match.group(1)))
                    
                    # Try to extract epoch and step info
                    epoch_match = re.search(r'Epoch (\d+)', line)
                    if epoch_match:
                        epochs.append(int(epoch_match.group(1)))
                    else:
                        epochs.append(None)
                    
                    # Extract step info if available
                    step_match = re.search(r'(\d+)/\d+', line)
                    if step_match:
                        steps.append(int(step_match.group(1)))
                    else:
                        steps.append(None)
    
    return np.array(losses), np.array(accuracies), epochs, steps

def analyze_loss_trend(losses, accuracies, epochs=None, steps=None):
    """Analyze the trend of discriminator loss."""
    print("=== Discriminator Loss Analysis ===")
    print(f"Total samples: {len(losses)}")
    
    # Basic statistics
    print(f"Loss - Mean: {np.mean(losses):.4f}, Std: {np.std(losses):.4f}")
    print(f"Loss - Min: {np.min(losses):.4f}, Max: {np.max(losses):.4f}")
    print(f"Accuracy - Mean: {np.mean(accuracies):.4f}, Std: {np.std(accuracies):.4f}")
    
    # Check if loss is actually decreasing
    if len(losses) > 100:
        # Use moving average to smooth the trend
        window_size = 100
        moving_avg = moving_average(losses, window_size)
        
        # Calculate trend using linear regression on moving average
        if len(moving_avg) > 10:
            x = np.arange(len(moving_avg))
            y = moving_avg
            
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
            
            print(f"\nTrend Analysis (using {window_size}-step moving average):")
            print(f"Slope: {slope:.6f} (negative = decreasing)")
            print(f"R-squared: {r_value**2:.4f}")
            print(f"P-value: {p_value:.4f}")
            
            if p_value < 0.05:
                if slope < 0:
                    print("✅ Statistically significant DECREASING trend")
                else:
                    print("❌ Statistically significant INCREASING trend")
            else:
                print("⚠️ No statistically significant trend")
    
    # Analyze accuracy stability
    accuracy_changes = np.diff(accuracies)
    stability_score = 1 - np.mean(np.abs(accuracy_changes))
    print(f"\nAccuracy Stability Score: {stability_score:.4f} (1.0 = perfectly stable)")
    
    # Count accuracy transitions
    transitions_0_to_1 = np.sum((accuracies[:-1] == 0) & (accuracies[1:] == 1))
    transitions_1_to_0 = np.sum((accuracies[:-1] == 1) & (accuracies[1:] == 0))
    print(f"Accuracy transitions 0→1: {transitions_0_to_1}")
    print(f"Accuracy transitions 1→0: {transitions_1_to_0}")
    
    return {
        'loss_mean': np.mean(losses),
        'loss_std': np.std(losses),
        'accuracy_mean': np.mean(accuracies),
        'accuracy_std': np.std(accuracies),
        'stability_score': stability_score,
        'transitions': transitions_0_to_1 + transitions_1_to_0
    }

def plot_loss_trend(losses, accuracies, save_path="discriminator_analysis.png"):
    """Plot discriminator loss and accuracy trends."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # Plot 1: Loss trend
    x = np.arange(len(losses))
    ax1.plot(x, losses, alpha=0.6, label='Raw Loss', linewidth=0.5)
    
    # Add moving average
    if len(losses) > 100:
        window_size = 100
        moving_avg = moving_average(losses, window_size)
        x_avg = np.arange(window_size-1, len(losses))
        ax1.plot(x_avg, moving_avg, 'r-', linewidth=2, label=f'{window_size}-step Moving Average')
    
    ax1.set_ylabel('Discriminator Loss')
    ax1.set_title('Discriminator Loss Trend')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Accuracy trend
    ax2.plot(x, accuracies, alpha=0.6, label='Raw Accuracy', linewidth=0.5)
    
    # Add moving average for accuracy
    if len(accuracies) > 100:
        moving_avg_acc = moving_average(accuracies, window_size)
        ax2.plot(x_avg, moving_avg_acc, 'g-', linewidth=2, label=f'{window_size}-step Moving Average')
    
    ax2.set_xlabel('Training Steps')
    ax2.set_ylabel('Discriminator Accuracy')
    ax2.set_title('Discriminator Accuracy Trend')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Saved analysis plot to {save_path}")

def main():
    """Main analysis function."""
    log_file = "logs/cmr_extended_1802344.out"
    
    print("Analyzing discriminator performance from training logs...")
    
    # Extract data
    losses, accuracies, epochs, steps = extract_discriminator_data(log_file)
    
    if len(losses) == 0:
        print("No discriminator data found in log file!")
        return
    
    # Analyze trends
    stats = analyze_loss_trend(losses, accuracies, epochs, steps)
    
    # Create visualization
    plot_loss_trend(losses, accuracies)
    
    # Summary
    print("\n=== SUMMARY ===")
    print(f"Discriminator Loss Trend: {'Decreasing' if stats['loss_mean'] < 0.7 else 'Stable/Increasing'}")
    print(f"Accuracy Stability: {'Good' if stats['stability_score'] > 0.8 else 'Poor'}")
    print(f"Training Stability: {'Good' if stats['transitions'] < len(losses)*0.1 else 'Poor'}")

if __name__ == "__main__":
    main() 