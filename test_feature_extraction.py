#!/usr/bin/env python3
"""
Test script to verify the new feature extraction mechanism with hierarchical processing
"""

import torch
import sys
import os

# Add the PromptMR-plus-Task3 directory to the path
sys.path.append('PromptMR-plus-Task3')

from models.promptmr import PromptMR
from models.domain_adapt import DomainAdaptationModule

def test_hierarchical_feature_extraction():
    """Test the new hierarchical feature extraction mechanism"""
    
    print("Testing hierarchical feature extraction...")
    
    # Test domain adaptation module with hierarchical processing
    try:
        domain_adapt = DomainAdaptationModule(
            cascade_channels=48,
            feature_extract_layers=[0, 1, 2],
            feature_dim=64
        )
        
        # Create dummy features (simulating the new format)
        dummy_features = []
        for i in range(3):  # 3 cascades
            cascade_features = []
            for j in range(3):  # 3 layers per cascade
                # Create dummy feature with different shapes for different layers
                if j == 0:  # bottleneck - 全局信息
                    feat = torch.randn(1, 48, 32, 32)
                elif j == 1:  # dec_level3 - 中等语义
                    feat = torch.randn(1, 48, 64, 64)
                else:  # dec_level2 - 局部细节
                    feat = torch.randn(1, 48, 128, 128)
                cascade_features.append(feat)
            dummy_features.append(cascade_features)
        
        print(f"✅ Created dummy features with {len(dummy_features)} cascades")
        for i, cascade_feat in enumerate(dummy_features):
            print(f"  Cascade {i}: {len(cascade_feat)} layers")
            for j, layer_feat in enumerate(cascade_feat):
                layer_names = ["bottleneck (global)", "dec_level3 (mid)", "dec_level2 (local)"]
                print(f"    Layer {j} ({layer_names[j]}): {layer_feat.shape}")
        
        # Test domain adaptation forward pass
        pred = domain_adapt(dummy_features, alpha=0.5)
        print(f"✅ Hierarchical domain adaptation successful, output shape: {pred.shape}")
        print(f"Output range: [{pred.min().item():.4f}, {pred.max().item():.4f}]")
        
        # Test the hierarchical processors
        print("\nTesting hierarchical processors:")
        
        # Test global processor (bottleneck)
        global_feat = torch.randn(1, 64, 64, 64)
        global_processed = domain_adapt.global_processor(global_feat)
        print(f"  Global processor: {global_feat.shape} -> {global_processed.shape}")
        
        # Test mid processor (dec_level3)
        mid_feat = torch.randn(1, 64, 64, 64)
        mid_processed = domain_adapt.mid_processor(mid_feat)
        print(f"  Mid processor: {mid_feat.shape} -> {mid_processed.shape}")
        
        # Test local processor (dec_level2)
        local_feat = torch.randn(1, 64, 64, 64)
        local_processed = domain_adapt.local_processor(local_feat)
        print(f"  Local processor: {local_feat.shape} -> {local_processed.shape}")
        
        # Test feature fusion
        print(f"\nFeature fusion input channels: {domain_adapt.feature_fusion[0].in_channels}")
        print(f"Feature fusion output channels: {domain_adapt.feature_fusion[0].out_channels}")
        
    except Exception as e:
        print(f"❌ Hierarchical domain adaptation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("✅ All hierarchical tests passed!")
    return True

if __name__ == "__main__":
    test_hierarchical_feature_extraction() 