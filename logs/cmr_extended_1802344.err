wandb: Appending key for api.wandb.ai to your netrc file: /home/<USER>/.netrc
wandb: W&B API key is configured. Use `wandb login --relogin` to force relogin
[rank: 0] Seed set to 42
GPU available: True (cuda), used: True
TPU available: False, using: 0 TPU cores
HPU available: False, using: 0 HPUs
You are using a CUDA device ('NVIDIA L40S') that has Tensor Cores. To properly utilize them, you should set `torch.set_float32_matmul_precision('medium' | 'high')` which will trade-off precision for performance. For more details, read https://pytorch.org/docs/stable/generated/torch.set_float32_matmul_precision.html#torch.set_float32_matmul_precision
[rank: 0] Seed set to 42
Initializing distributed: GLOBAL_RANK: 0, MEMBER: 1/1
----------------------------------------------------------------------------------------------------
distributed_backend=nccl
All distributed processes registered. Starting with 1 processes
----------------------------------------------------------------------------------------------------

wandb: Using wandb-core as the SDK backend.  Please refer to https://wandb.me/wandb-core for more information.
wandb: Currently logged in as: lisha-zeng (lisha-zeng-cedars-sinai) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.19.9
wandb: Run data is saved locally in logs/wandb/run-20250620_115438-9ip30l87
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run extended_pretrain_1148
wandb: ⭐️ View project at https://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_extended
wandb: 🚀 View run at https://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_extended/runs/9ip30l87
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                   | Params | Mode 
--------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum   | 0      | train
1 | SSIM             | DistributedMetricSum   | 0      | train
2 | PSNR             | DistributedMetricSum   | 0      | train
3 | ValLoss          | DistributedMetricSum   | 0      | train
4 | TotExamples      | DistributedMetricSum   | 0      | train
5 | TotSliceExamples | DistributedMetricSum   | 0      | train
6 | promptmr         | PromptMR               | 42.1 M | train
7 | loss             | SSIMLoss               | 0      | train
8 | domain_adapt     | DomainAdaptationModule | 2.2 M  | train
9 | domain_criterion | MSELoss                | 0      | train
--------------------------------------------------------------------
38.9 M    Trainable params
5.4 M     Non-trainable params
44.2 M    Total params
176.996   Total estimated model params size (MB)
SLURM auto-requeueing enabled. Setting signal handlers.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/data.py:78: Trying to infer the `batch_size` from an ambiguous collection. The batch size we found is 1. To avoid any miscalculations, use `self.log(..., batch_size=batch_size)`.
/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/pl_modules/domain_adapt_module.py:984: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  'std': pred_field_strength.std().item()
/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/pl_modules/domain_adapt_module.py:1010: UserWarning: std(): degrees of freedom is <= 0. Correction should be strictly less than the reduction factor (input numel divided by output numel). (Triggered internally at ../aten/src/ATen/native/ReduceOps.cpp:1808.)
  self.log("val_metrics/pred_prob_std", pred_field_strength.std(), on_step=False, on_epoch=True)
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_loss', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/logit_min', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/logit_max', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/logit_mean', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/logit_std', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/domain_accuracy', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/field_strength_error', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/pred_prob_mean', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_metrics/pred_prob_std', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
wandb: WARNING Tried to log to step 0 that is less than the current step 3. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 3. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 7. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 7. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24290. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24294. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24294. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24298. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24298. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24302. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24302. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24306. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 24289 that is less than the current step 24306. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory
  warn(f"Failed to load image Python extension: {e}")
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('train_metrics/grl_alpha', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('train_loss', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('train_metrics/recon_loss', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
Epoch 0, global step 24289: 'validation_loss' reached 0.03965 (best 0.03965), saving model to 'logs/cmr2025_extended/9ip30l87/checkpoints/best-epochepoch=00-valvalidation_loss=0.0396.ckpt' as top 3
wandb: WARNING Tried to log to step 48578 that is less than the current step 48579. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48583. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48583. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48587. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48587. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48591. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48591. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48595. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 48578 that is less than the current step 48595. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
Epoch 1, global step 48578: 'validation_loss' reached 0.03397 (best 0.03397), saving model to 'logs/cmr2025_extended/9ip30l87/checkpoints/best-epochepoch=01-valvalidation_loss=0.0340.ckpt' as top 3
srun: Job step aborted: Waiting up to 32 seconds for job step to finish.
slurmstepd: error: *** JOB 1802344 ON esplhpc-cp076 CANCELLED AT 2025-06-20T23:48:43 DUE TO TIME LIMIT ***
slurmstepd: error: *** STEP 1802344.0 ON esplhpc-cp076 CANCELLED AT 2025-06-20T23:48:43 DUE TO TIME LIMIT ***
[rank: 0] Received SIGTERM: 15
Bypassing SIGTERM: 15
[rank: 0] Received SIGTERM: 15
Bypassing SIGTERM: 15
