Python path: /home/<USER>/.conda/envs/promptmr/bin/python
CUDA available: True
=== IMPROVED DISCRIMINATOR TRAINING ===
Config: cmr25-cardiac-domain-adapt-improved.yaml
Total epochs: 10
Batch size per GPU: 2 (effective: 8)
Model: 6 cascades, 48 channels (full capacity)
Domain: 4 feature layers, 5 epochs pretrain, 0.85 accuracy threshold
Expected time: ~6-8 hours
Key improvements:
  - Better discriminator architecture
  - More feature layers [1,2,3,4]
  - Higher accuracy threshold (0.85)
  - Longer pretraining (5 epochs)
  - Improved loss function
==========================
Starting improved discriminator training...
Train from scratch, no pretrain weights loaded
Configuration saved to logs/cmr2025_improved/71qrhcxl/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s][1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mimproved_discriminator_1459[0m at: [34mhttps://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_improved/runs/71qrhcxl[0m
Improved discriminator training complete!
Check wandb project: cmr2025_improved
Key metrics to monitor:
  - discriminator_accuracy (should reach >0.85 in pretrain phase)
  - training_phase (should switch to adversarial after pretrain)
  - domain_loss (should be stable)
  - field_strength_error (should decrease)
  - logit statistics (should be reasonable)
