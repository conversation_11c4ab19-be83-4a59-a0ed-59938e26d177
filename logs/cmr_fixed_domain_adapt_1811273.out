Python path: /home/<USER>/.conda/envs/promptmr/bin/python
CUDA available: True
=== FIXED DOMAIN ADAPTATION TRAINING ===
Config: cmr25-cardiac-domain-adapt-fixed.yaml
Total epochs: 50
Batch size per GPU: 1 (effective: 4)
Model: 6 cascades, 48 channels
Domain: Hierarchical feature processing (global/mid/local)
Discriminator: BCE_with_logits for mixed precision compatibility
Accuracy: Fixed calculation for single field strength batches
Loss function: binary_cross_entropy_with_logits (stable)
Learning rate: 0.0003
Layer analysis: ENABLED (real-time feature analysis)
Discriminator checkpoint: Will save when accuracy >= 0.65
Training time: 24 hours
Fixed issues:
  - Mixed precision compatibility (BCE_with_logits)
  - Correct accuracy calculation for single field strength batches
  - Hierarchical feature processing
  - Stable accuracy with sliding average
==========================
Starting fixed domain adaptation training...
Train from scratch, no pretrain weights loaded
Configuration saved to logs/cmr2025_task3/rq0vp7n8/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3753653.2500, mean: 10442.4580
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_0.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_0.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_0.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_0.png
Cascade features length: 6
Feature 0 type: <class 'list'>, shape: no shape
Feature 1 type: <class 'list'>, shape: no shape
Feature 2 type: <class 'list'>, shape: no shape
Feature 3 type: <class 'list'>, shape: no shape
Feature 4 type: <class 'list'>, shape: no shape
Feature 5 type: <class 'list'>, shape: no shape
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5

Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:01<00:01,  0.54it/s]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3418105.5000, mean: 12657.9951
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_1.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_1.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_1.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_1.png
Cascade features length: 6
Feature 0 type: <class 'list'>, shape: no shape
Feature 1 type: <class 'list'>, shape: no shape
Feature 2 type: <class 'list'>, shape: no shape
Feature 3 type: <class 'list'>, shape: no shape
Feature 4 type: <class 'list'>, shape: no shape
Feature 5 type: <class 'list'>, shape: no shape
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5

Sanity Checking DataLoader 0: 100%|██████████| 2/2 [00:02<00:00,  0.67it/s]
                                                                           

Training: |          | 0/? [00:00<?, ?it/s]
Training:   0%|          | 0/24289 [00:00<?, ?it/s]
Epoch 0:   0%|          | 0/24289 [00:00<?, ?it/s] 🔄 Epoch 0 starting - Phase: discriminator_pretrain, Alpha: 0.0000
DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
Performing one-time verification of domain adaptation setup...

--- Domain Adaptation Verification ---
Feature extract layers: [1, 3, 5]
Number of cascade features available: 6

Verifying feature extraction layers:
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mfixed_domain_adapt_1735[0m at: [34mhttps://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_task3/runs/rq0vp7n8[0m
Fixed domain adaptation training complete!
Check wandb project: cmr2025_task3
Check discriminator checkpoints: discriminator_checkpoints/
Key metrics to monitor:
  - discriminator_accuracy (should be stable, not 0/1 jumping)
  - training_phase (should switch to adversarial after epoch 3)
  - discriminator_loss (should decrease during pretrain)
  - domain_loss (should be stable, no NaN)
  - layer_analysis (real-time feature analysis every 500 steps)
  - discriminator_checkpoint/* (saved when threshold reached)
