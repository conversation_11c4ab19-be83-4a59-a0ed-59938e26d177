Python path: /home/<USER>/.conda/envs/promptmr/bin/python
CUDA available: True
=== FIXED DOMAIN ADAPTATION TRAINING ===
Config: cmr25-cardiac-domain-adapt-fixed.yaml
Total epochs: 50
Batch size per GPU: 1 (effective: 4)
Model: 6 cascades, 48 channels
Domain: Hierarchical feature processing (global/mid/local)
Discriminator: BCE_with_logits for mixed precision compatibility
Accuracy: Fixed calculation for single field strength batches
Loss function: binary_cross_entropy_with_logits (stable)
Learning rate: 0.0003
Layer analysis: ENABLED (real-time feature analysis)
Discriminator checkpoint: Will save when accuracy >= 0.65
Training time: 24 hours
Fixed issues:
  - Mixed precision compatibility (BCE_with_logits)
  - Correct accuracy calculation for single field strength batches
  - Hierarchical feature processing
  - Stable accuracy with sliding average
==========================
Starting fixed domain adaptation training...
Train from scratch, no pretrain weights loaded
Configuration saved to logs/cmr2025_task3/6zbgzwmv/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3753653.2500, mean: 10442.4580
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_0.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_0.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_0.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_0.png

Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:01<00:01,  0.55it/s]KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3418105.5000, mean: 12657.9951
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_1.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_1.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_1.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_1.png

Sanity Checking DataLoader 0: 100%|██████████| 2/2 [00:02<00:00,  0.69it/s]
                                                                           

Training: |          | 0/? [00:00<?, ?it/s]
Training:   0%|          | 0/24289 [00:00<?, ?it/s]
Epoch 0:   0%|          | 0/24289 [00:00<?, ?it/s] 🔄 Epoch 0 starting - Phase: discriminator_pretrain, Alpha: 0.0000
Performing one-time verification of domain adaptation setup...

--- Domain Adaptation Verification ---
Feature extract layers: [1, 3, 5]
Number of cascade features available: 0

Verifying feature extraction layers:
  - Layer 1: Index out of range for available features (0)!
  - Layer 3: Index out of range for available features (0)!
  - Layer 5: Index out of range for available features (0)!

Verifying compressors:
  - Compressor 0: in_channels=96
  - Compressor 1: in_channels=384
  - Compressor 2: in_channels=384
--- End Verification ---


🔍 LAYER ANALYSIS at step 0

Epoch 0:   0%|          | 1/24289 [00:00<5:30:59,  1.22it/s]
Epoch 0:   0%|          | 1/24289 [00:00<5:31:21,  1.22it/s, v_num=zwmv, train_loss_step=0.221, train_metrics/recon_loss_step=0.221]
Epoch 0:   0%|          | 2/24289 [00:01<3:48:37,  1.77it/s, v_num=zwmv, train_loss_step=0.221, train_metrics/recon_loss_step=0.221]
Epoch 0:   0%|          | 2/24289 [00:01<3:48:49,  1.77it/s, v_num=zwmv, train_loss_step=0.357, train_metrics/recon_loss_step=0.357]
Epoch 0:   0%|          | 3/24289 [00:01<3:04:08,  2.20it/s, v_num=zwmv, train_loss_step=0.357, train_metrics/recon_loss_step=0.357]
Epoch 0:   0%|          | 3/24289 [00:01<3:04:14,  2.20it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   0%|          | 4/24289 [00:01<2:49:46,  2.38it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   0%|          | 4/24289 [00:01<2:49:50,  2.38it/s, v_num=zwmv, train_loss_step=0.213, train_metrics/recon_loss_step=0.213]
Epoch 0:   0%|          | 5/24289 [00:01<2:36:23,  2.59it/s, v_num=zwmv, train_loss_step=0.213, train_metrics/recon_loss_step=0.213]
Epoch 0:   0%|          | 5/24289 [00:01<2:36:27,  2.59it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   0%|          | 6/24289 [00:02<2:33:59,  2.63it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   0%|          | 6/24289 [00:02<2:34:02,  2.63it/s, v_num=zwmv, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]
Epoch 0:   0%|          | 7/24289 [00:02<2:34:59,  2.61it/s, v_num=zwmv, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]
Epoch 0:   0%|          | 7/24289 [00:02<2:35:01,  2.61it/s, v_num=zwmv, train_loss_step=0.519, train_metrics/recon_loss_step=0.519]
Epoch 0:   0%|          | 8/24289 [00:02<2:26:57,  2.75it/s, v_num=zwmv, train_loss_step=0.519, train_metrics/recon_loss_step=0.519]
Epoch 0:   0%|          | 8/24289 [00:02<2:26:59,  2.75it/s, v_num=zwmv, train_loss_step=0.455, train_metrics/recon_loss_step=0.455]
Epoch 0:   0%|          | 9/24289 [00:03<2:21:51,  2.85it/s, v_num=zwmv, train_loss_step=0.455, train_metrics/recon_loss_step=0.455]
Epoch 0:   0%|          | 9/24289 [00:03<2:21:53,  2.85it/s, v_num=zwmv, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]
Epoch 0:   0%|          | 10/24289 [00:03<2:24:40,  2.80it/s, v_num=zwmv, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]
Epoch 0:   0%|          | 10/24289 [00:03<2:24:42,  2.80it/s, v_num=zwmv, train_loss_step=0.0935, train_metrics/recon_loss_step=0.0935]
Epoch 0:   0%|          | 11/24289 [00:03<2:20:54,  2.87it/s, v_num=zwmv, train_loss_step=0.0935, train_metrics/recon_loss_step=0.0935]
Epoch 0:   0%|          | 11/24289 [00:03<2:20:56,  2.87it/s, v_num=zwmv, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]  
Epoch 0:   0%|          | 12/24289 [00:04<2:23:57,  2.81it/s, v_num=zwmv, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]
Epoch 0:   0%|          | 12/24289 [00:04<2:23:59,  2.81it/s, v_num=zwmv, train_loss_step=0.402, train_metrics/recon_loss_step=0.402]
Epoch 0:   0%|          | 13/24289 [00:04<2:21:25,  2.86it/s, v_num=zwmv, train_loss_step=0.402, train_metrics/recon_loss_step=0.402]
Epoch 0:   0%|          | 13/24289 [00:04<2:21:27,  2.86it/s, v_num=zwmv, train_loss_step=0.261, train_metrics/recon_loss_step=0.261]
Epoch 0:   0%|          | 14/24289 [00:05<2:24:39,  2.80it/s, v_num=zwmv, train_loss_step=0.261, train_metrics/recon_loss_step=0.261]
Epoch 0:   0%|          | 14/24289 [00:05<2:24:41,  2.80it/s, v_num=zwmv, train_loss_step=0.237, train_metrics/recon_loss_step=0.237]
Epoch 0:   0%|          | 15/24289 [00:05<2:23:40,  2.82it/s, v_num=zwmv, train_loss_step=0.237, train_metrics/recon_loss_step=0.237]
Epoch 0:   0%|          | 15/24289 [00:05<2:23:42,  2.82it/s, v_num=zwmv, train_loss_step=0.233, train_metrics/recon_loss_step=0.233]
Epoch 0:   0%|          | 16/24289 [00:05<2:20:34,  2.88it/s, v_num=zwmv, train_loss_step=0.233, train_metrics/recon_loss_step=0.233]
Epoch 0:   0%|          | 16/24289 [00:05<2:20:35,  2.88it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   0%|          | 17/24289 [00:05<2:19:31,  2.90it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   0%|          | 17/24289 [00:05<2:19:33,  2.90it/s, v_num=zwmv, train_loss_step=0.243, train_metrics/recon_loss_step=0.243]
Epoch 0:   0%|          | 18/24289 [00:06<2:19:24,  2.90it/s, v_num=zwmv, train_loss_step=0.243, train_metrics/recon_loss_step=0.243]
Epoch 0:   0%|          | 18/24289 [00:06<2:19:25,  2.90it/s, v_num=zwmv, train_loss_step=0.0151, train_metrics/recon_loss_step=0.0151]
Epoch 0:   0%|          | 19/24289 [00:06<2:19:29,  2.90it/s, v_num=zwmv, train_loss_step=0.0151, train_metrics/recon_loss_step=0.0151]
Epoch 0:   0%|          | 19/24289 [00:06<2:19:30,  2.90it/s, v_num=zwmv, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]  
Epoch 0:   0%|          | 20/24289 [00:06<2:19:08,  2.91it/s, v_num=zwmv, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]
Epoch 0:   0%|          | 20/24289 [00:06<2:19:09,  2.91it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   0%|          | 21/24289 [00:07<2:18:52,  2.91it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   0%|          | 21/24289 [00:07<2:18:53,  2.91it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   0%|          | 22/24289 [00:07<2:19:26,  2.90it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   0%|          | 22/24289 [00:07<2:19:27,  2.90it/s, v_num=zwmv, train_loss_step=0.040, train_metrics/recon_loss_step=0.040]
Epoch 0:   0%|          | 23/24289 [00:07<2:18:57,  2.91it/s, v_num=zwmv, train_loss_step=0.040, train_metrics/recon_loss_step=0.040]
Epoch 0:   0%|          | 23/24289 [00:07<2:18:58,  2.91it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   0%|          | 24/24289 [00:08<2:18:54,  2.91it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   0%|          | 24/24289 [00:08<2:18:55,  2.91it/s, v_num=zwmv, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]
Epoch 0:   0%|          | 25/24289 [00:08<2:19:05,  2.91it/s, v_num=zwmv, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]
Epoch 0:   0%|          | 25/24289 [00:08<2:19:06,  2.91it/s, v_num=zwmv, train_loss_step=0.0735, train_metrics/recon_loss_step=0.0735]
Epoch 0:   0%|          | 26/24289 [00:08<2:17:31,  2.94it/s, v_num=zwmv, train_loss_step=0.0735, train_metrics/recon_loss_step=0.0735]
Epoch 0:   0%|          | 26/24289 [00:08<2:17:32,  2.94it/s, v_num=zwmv, train_loss_step=0.317, train_metrics/recon_loss_step=0.317]  
Epoch 0:   0%|          | 27/24289 [00:09<2:18:04,  2.93it/s, v_num=zwmv, train_loss_step=0.317, train_metrics/recon_loss_step=0.317]
Epoch 0:   0%|          | 27/24289 [00:09<2:18:05,  2.93it/s, v_num=zwmv, train_loss_step=0.0768, train_metrics/recon_loss_step=0.0768]
Epoch 0:   0%|          | 28/24289 [00:09<2:18:00,  2.93it/s, v_num=zwmv, train_loss_step=0.0768, train_metrics/recon_loss_step=0.0768]
Epoch 0:   0%|          | 28/24289 [00:09<2:18:01,  2.93it/s, v_num=zwmv, train_loss_step=0.315, train_metrics/recon_loss_step=0.315]  
Epoch 0:   0%|          | 29/24289 [00:09<2:17:17,  2.94it/s, v_num=zwmv, train_loss_step=0.315, train_metrics/recon_loss_step=0.315]
Epoch 0:   0%|          | 29/24289 [00:09<2:17:18,  2.94it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   0%|          | 30/24289 [00:10<2:17:12,  2.95it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   0%|          | 30/24289 [00:10<2:17:13,  2.95it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   0%|          | 31/24289 [00:10<2:15:37,  2.98it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   0%|          | 31/24289 [00:10<2:15:37,  2.98it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   0%|          | 32/24289 [00:10<2:15:51,  2.98it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   0%|          | 32/24289 [00:10<2:15:51,  2.98it/s, v_num=zwmv, train_loss_step=0.205, train_metrics/recon_loss_step=0.205]
Epoch 0:   0%|          | 33/24289 [00:10<2:14:35,  3.00it/s, v_num=zwmv, train_loss_step=0.205, train_metrics/recon_loss_step=0.205]
Epoch 0:   0%|          | 33/24289 [00:10<2:14:35,  3.00it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   0%|          | 34/24289 [00:11<2:13:23,  3.03it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   0%|          | 34/24289 [00:11<2:13:24,  3.03it/s, v_num=zwmv, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]
Epoch 0:   0%|          | 35/24289 [00:11<2:12:29,  3.05it/s, v_num=zwmv, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]
Epoch 0:   0%|          | 35/24289 [00:11<2:12:30,  3.05it/s, v_num=zwmv, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]
Epoch 0:   0%|          | 36/24289 [00:11<2:11:36,  3.07it/s, v_num=zwmv, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]
Epoch 0:   0%|          | 36/24289 [00:11<2:11:37,  3.07it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   0%|          | 37/24289 [00:11<2:10:38,  3.09it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   0%|          | 37/24289 [00:11<2:10:38,  3.09it/s, v_num=zwmv, train_loss_step=0.230, train_metrics/recon_loss_step=0.230]
Epoch 0:   0%|          | 38/24289 [00:12<2:09:59,  3.11it/s, v_num=zwmv, train_loss_step=0.230, train_metrics/recon_loss_step=0.230]
Epoch 0:   0%|          | 38/24289 [00:12<2:10:00,  3.11it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   0%|          | 39/24289 [00:12<2:10:16,  3.10it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   0%|          | 39/24289 [00:12<2:10:17,  3.10it/s, v_num=zwmv, train_loss_step=0.380, train_metrics/recon_loss_step=0.380]
Epoch 0:   0%|          | 40/24289 [00:12<2:10:17,  3.10it/s, v_num=zwmv, train_loss_step=0.380, train_metrics/recon_loss_step=0.380]
Epoch 0:   0%|          | 40/24289 [00:12<2:10:17,  3.10it/s, v_num=zwmv, train_loss_step=0.069, train_metrics/recon_loss_step=0.069]
Epoch 0:   0%|          | 41/24289 [00:13<2:09:54,  3.11it/s, v_num=zwmv, train_loss_step=0.069, train_metrics/recon_loss_step=0.069]
Epoch 0:   0%|          | 41/24289 [00:13<2:09:55,  3.11it/s, v_num=zwmv, train_loss_step=0.289, train_metrics/recon_loss_step=0.289]
Epoch 0:   0%|          | 42/24289 [00:13<2:09:10,  3.13it/s, v_num=zwmv, train_loss_step=0.289, train_metrics/recon_loss_step=0.289]
Epoch 0:   0%|          | 42/24289 [00:13<2:09:10,  3.13it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   0%|          | 43/24289 [00:13<2:09:53,  3.11it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   0%|          | 43/24289 [00:13<2:09:54,  3.11it/s, v_num=zwmv, train_loss_step=0.307, train_metrics/recon_loss_step=0.307]
Epoch 0:   0%|          | 44/24289 [00:14<2:09:51,  3.11it/s, v_num=zwmv, train_loss_step=0.307, train_metrics/recon_loss_step=0.307]
Epoch 0:   0%|          | 44/24289 [00:14<2:09:51,  3.11it/s, v_num=zwmv, train_loss_step=0.0404, train_metrics/recon_loss_step=0.0404]
Epoch 0:   0%|          | 45/24289 [00:14<2:09:09,  3.13it/s, v_num=zwmv, train_loss_step=0.0404, train_metrics/recon_loss_step=0.0404]
Epoch 0:   0%|          | 45/24289 [00:14<2:09:09,  3.13it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]  
Epoch 0:   0%|          | 46/24289 [00:14<2:08:24,  3.15it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   0%|          | 46/24289 [00:14<2:08:25,  3.15it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   0%|          | 47/24289 [00:14<2:07:55,  3.16it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   0%|          | 47/24289 [00:14<2:07:56,  3.16it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   0%|          | 48/24289 [00:15<2:08:03,  3.16it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   0%|          | 48/24289 [00:15<2:08:03,  3.15it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   0%|          | 49/24289 [00:15<2:07:32,  3.17it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   0%|          | 49/24289 [00:15<2:07:32,  3.17it/s, v_num=zwmv, train_loss_step=0.263, train_metrics/recon_loss_step=0.263]
Epoch 0:   0%|          | 50/24289 [00:15<2:08:32,  3.14it/s, v_num=zwmv, train_loss_step=0.263, train_metrics/recon_loss_step=0.263]
Epoch 0:   0%|          | 50/24289 [00:15<2:08:32,  3.14it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   0%|          | 51/24289 [00:16<2:08:39,  3.14it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   0%|          | 51/24289 [00:16<2:08:39,  3.14it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   0%|          | 52/24289 [00:16<2:09:19,  3.12it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   0%|          | 52/24289 [00:16<2:09:19,  3.12it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   0%|          | 53/24289 [00:17<2:09:48,  3.11it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   0%|          | 53/24289 [00:17<2:09:48,  3.11it/s, v_num=zwmv, train_loss_step=0.0261, train_metrics/recon_loss_step=0.0261]
Epoch 0:   0%|          | 54/24289 [00:17<2:09:24,  3.12it/s, v_num=zwmv, train_loss_step=0.0261, train_metrics/recon_loss_step=0.0261]
Epoch 0:   0%|          | 54/24289 [00:17<2:09:24,  3.12it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]  
Epoch 0:   0%|          | 55/24289 [00:17<2:08:38,  3.14it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 55/24289 [00:17<2:08:39,  3.14it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   0%|          | 56/24289 [00:17<2:08:03,  3.15it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   0%|          | 56/24289 [00:17<2:08:03,  3.15it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   0%|          | 57/24289 [00:18<2:08:29,  3.14it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   0%|          | 57/24289 [00:18<2:08:29,  3.14it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   0%|          | 58/24289 [00:18<2:08:06,  3.15it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   0%|          | 58/24289 [00:18<2:08:06,  3.15it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   0%|          | 59/24289 [00:18<2:08:24,  3.15it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   0%|          | 59/24289 [00:18<2:08:24,  3.14it/s, v_num=zwmv, train_loss_step=0.00403, train_metrics/recon_loss_step=0.00403]
Epoch 0:   0%|          | 60/24289 [00:19<2:07:59,  3.15it/s, v_num=zwmv, train_loss_step=0.00403, train_metrics/recon_loss_step=0.00403]
Epoch 0:   0%|          | 60/24289 [00:19<2:07:59,  3.15it/s, v_num=zwmv, train_loss_step=0.271, train_metrics/recon_loss_step=0.271]    
Epoch 0:   0%|          | 61/24289 [00:19<2:07:31,  3.17it/s, v_num=zwmv, train_loss_step=0.271, train_metrics/recon_loss_step=0.271]
Epoch 0:   0%|          | 61/24289 [00:19<2:07:32,  3.17it/s, v_num=zwmv, train_loss_step=0.237, train_metrics/recon_loss_step=0.237]
Epoch 0:   0%|          | 62/24289 [00:19<2:06:58,  3.18it/s, v_num=zwmv, train_loss_step=0.237, train_metrics/recon_loss_step=0.237]
Epoch 0:   0%|          | 62/24289 [00:19<2:06:59,  3.18it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   0%|          | 63/24289 [00:19<2:07:18,  3.17it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   0%|          | 63/24289 [00:19<2:07:18,  3.17it/s, v_num=zwmv, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]
Epoch 0:   0%|          | 64/24289 [00:20<2:07:21,  3.17it/s, v_num=zwmv, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]
Epoch 0:   0%|          | 64/24289 [00:20<2:07:21,  3.17it/s, v_num=zwmv, train_loss_step=0.219, train_metrics/recon_loss_step=0.219]
Epoch 0:   0%|          | 65/24289 [00:20<2:07:43,  3.16it/s, v_num=zwmv, train_loss_step=0.219, train_metrics/recon_loss_step=0.219]
Epoch 0:   0%|          | 65/24289 [00:20<2:07:43,  3.16it/s, v_num=zwmv, train_loss_step=0.0606, train_metrics/recon_loss_step=0.0606]
Epoch 0:   0%|          | 66/24289 [00:20<2:07:13,  3.17it/s, v_num=zwmv, train_loss_step=0.0606, train_metrics/recon_loss_step=0.0606]
Epoch 0:   0%|          | 66/24289 [00:20<2:07:14,  3.17it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]  
Epoch 0:   0%|          | 67/24289 [00:21<2:06:46,  3.18it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   0%|          | 67/24289 [00:21<2:06:47,  3.18it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   0%|          | 68/24289 [00:21<2:06:55,  3.18it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   0%|          | 68/24289 [00:21<2:06:56,  3.18it/s, v_num=zwmv, train_loss_step=0.246, train_metrics/recon_loss_step=0.246]
Epoch 0:   0%|          | 69/24289 [00:21<2:06:25,  3.19it/s, v_num=zwmv, train_loss_step=0.246, train_metrics/recon_loss_step=0.246]
Epoch 0:   0%|          | 69/24289 [00:21<2:06:26,  3.19it/s, v_num=zwmv, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]
Epoch 0:   0%|          | 70/24289 [00:21<2:06:13,  3.20it/s, v_num=zwmv, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]
Epoch 0:   0%|          | 70/24289 [00:21<2:06:13,  3.20it/s, v_num=zwmv, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]
Epoch 0:   0%|          | 71/24289 [00:22<2:05:43,  3.21it/s, v_num=zwmv, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]
Epoch 0:   0%|          | 71/24289 [00:22<2:05:44,  3.21it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]
Epoch 0:   0%|          | 72/24289 [00:22<2:05:29,  3.22it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]
Epoch 0:   0%|          | 72/24289 [00:22<2:05:30,  3.22it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   0%|          | 73/24289 [00:22<2:05:15,  3.22it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   0%|          | 73/24289 [00:22<2:05:15,  3.22it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   0%|          | 74/24289 [00:22<2:04:57,  3.23it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   0%|          | 74/24289 [00:22<2:04:57,  3.23it/s, v_num=zwmv, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]
Epoch 0:   0%|          | 75/24289 [00:23<2:05:20,  3.22it/s, v_num=zwmv, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]
Epoch 0:   0%|          | 75/24289 [00:23<2:05:20,  3.22it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 76/24289 [00:23<2:04:54,  3.23it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 76/24289 [00:23<2:04:54,  3.23it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   0%|          | 77/24289 [00:23<2:04:37,  3.24it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   0%|          | 77/24289 [00:23<2:04:38,  3.24it/s, v_num=zwmv, train_loss_step=0.0828, train_metrics/recon_loss_step=0.0828]
Epoch 0:   0%|          | 78/24289 [00:24<2:04:21,  3.24it/s, v_num=zwmv, train_loss_step=0.0828, train_metrics/recon_loss_step=0.0828]
Epoch 0:   0%|          | 78/24289 [00:24<2:04:21,  3.24it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]  
Epoch 0:   0%|          | 79/24289 [00:24<2:04:43,  3.24it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   0%|          | 79/24289 [00:24<2:04:43,  3.24it/s, v_num=zwmv, train_loss_step=0.230, train_metrics/recon_loss_step=0.230]
Epoch 0:   0%|          | 80/24289 [00:24<2:04:47,  3.23it/s, v_num=zwmv, train_loss_step=0.230, train_metrics/recon_loss_step=0.230]
Epoch 0:   0%|          | 80/24289 [00:24<2:04:48,  3.23it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   0%|          | 81/24289 [00:25<2:05:00,  3.23it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   0%|          | 81/24289 [00:25<2:05:01,  3.23it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   0%|          | 82/24289 [00:25<2:05:03,  3.23it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   0%|          | 82/24289 [00:25<2:05:03,  3.23it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   0%|          | 83/24289 [00:25<2:05:23,  3.22it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   0%|          | 83/24289 [00:25<2:05:24,  3.22it/s, v_num=zwmv, train_loss_step=0.0883, train_metrics/recon_loss_step=0.0883]
Epoch 0:   0%|          | 84/24289 [00:26<2:05:25,  3.22it/s, v_num=zwmv, train_loss_step=0.0883, train_metrics/recon_loss_step=0.0883]
Epoch 0:   0%|          | 84/24289 [00:26<2:05:25,  3.22it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]  
Epoch 0:   0%|          | 85/24289 [00:26<2:05:06,  3.22it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   0%|          | 85/24289 [00:26<2:05:07,  3.22it/s, v_num=zwmv, train_loss_step=0.303, train_metrics/recon_loss_step=0.303]
Epoch 0:   0%|          | 86/24289 [00:26<2:05:09,  3.22it/s, v_num=zwmv, train_loss_step=0.303, train_metrics/recon_loss_step=0.303]
Epoch 0:   0%|          | 86/24289 [00:26<2:05:09,  3.22it/s, v_num=zwmv, train_loss_step=0.269, train_metrics/recon_loss_step=0.269]
Epoch 0:   0%|          | 87/24289 [00:27<2:05:40,  3.21it/s, v_num=zwmv, train_loss_step=0.269, train_metrics/recon_loss_step=0.269]
Epoch 0:   0%|          | 87/24289 [00:27<2:05:40,  3.21it/s, v_num=zwmv, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   0%|          | 88/24289 [00:27<2:05:32,  3.21it/s, v_num=zwmv, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   0%|          | 88/24289 [00:27<2:05:32,  3.21it/s, v_num=zwmv, train_loss_step=0.0451, train_metrics/recon_loss_step=0.0451]
Epoch 0:   0%|          | 89/24289 [00:27<2:05:55,  3.20it/s, v_num=zwmv, train_loss_step=0.0451, train_metrics/recon_loss_step=0.0451]
Epoch 0:   0%|          | 89/24289 [00:27<2:05:55,  3.20it/s, v_num=zwmv, train_loss_step=0.265, train_metrics/recon_loss_step=0.265]  
Epoch 0:   0%|          | 90/24289 [00:28<2:05:42,  3.21it/s, v_num=zwmv, train_loss_step=0.265, train_metrics/recon_loss_step=0.265]
Epoch 0:   0%|          | 90/24289 [00:28<2:05:43,  3.21it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   0%|          | 91/24289 [00:28<2:05:58,  3.20it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   0%|          | 91/24289 [00:28<2:05:58,  3.20it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   0%|          | 92/24289 [00:28<2:06:10,  3.20it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   0%|          | 92/24289 [00:28<2:06:10,  3.20it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   0%|          | 93/24289 [00:29<2:05:52,  3.20it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   0%|          | 93/24289 [00:29<2:05:52,  3.20it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   0%|          | 94/24289 [00:29<2:05:32,  3.21it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   0%|          | 94/24289 [00:29<2:05:32,  3.21it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]  
Epoch 0:   0%|          | 95/24289 [00:29<2:05:33,  3.21it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   0%|          | 95/24289 [00:29<2:05:33,  3.21it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   0%|          | 96/24289 [00:29<2:05:25,  3.21it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   0%|          | 96/24289 [00:29<2:05:26,  3.21it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   0%|          | 97/24289 [00:30<2:05:07,  3.22it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   0%|          | 97/24289 [00:30<2:05:08,  3.22it/s, v_num=zwmv, train_loss_step=0.231, train_metrics/recon_loss_step=0.231]
Epoch 0:   0%|          | 98/24289 [00:30<2:05:00,  3.23it/s, v_num=zwmv, train_loss_step=0.231, train_metrics/recon_loss_step=0.231]
Epoch 0:   0%|          | 98/24289 [00:30<2:05:00,  3.23it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   0%|          | 99/24289 [00:30<2:04:40,  3.23it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   0%|          | 99/24289 [00:30<2:04:40,  3.23it/s, v_num=zwmv, train_loss_step=0.0818, train_metrics/recon_loss_step=0.0818]
Epoch 0:   0%|          | 100/24289 [00:30<2:04:27,  3.24it/s, v_num=zwmv, train_loss_step=0.0818, train_metrics/recon_loss_step=0.0818]
Epoch 0:   0%|          | 100/24289 [00:30<2:04:27,  3.24it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]  
Epoch 0:   0%|          | 101/24289 [00:31<2:04:36,  3.24it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   0%|          | 101/24289 [00:31<2:04:36,  3.24it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   0%|          | 102/24289 [00:31<2:04:29,  3.24it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   0%|          | 102/24289 [00:31<2:04:30,  3.24it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   0%|          | 103/24289 [00:31<2:04:28,  3.24it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   0%|          | 103/24289 [00:31<2:04:28,  3.24it/s, v_num=zwmv, train_loss_step=0.0741, train_metrics/recon_loss_step=0.0741]
Epoch 0:   0%|          | 104/24289 [00:32<2:04:08,  3.25it/s, v_num=zwmv, train_loss_step=0.0741, train_metrics/recon_loss_step=0.0741]
Epoch 0:   0%|          | 104/24289 [00:32<2:04:09,  3.25it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   0%|          | 105/24289 [00:32<2:03:57,  3.25it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   0%|          | 105/24289 [00:32<2:03:57,  3.25it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 106/24289 [00:32<2:03:40,  3.26it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 106/24289 [00:32<2:03:40,  3.26it/s, v_num=zwmv, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]
Epoch 0:   0%|          | 107/24289 [00:32<2:03:49,  3.25it/s, v_num=zwmv, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]
Epoch 0:   0%|          | 107/24289 [00:32<2:03:50,  3.25it/s, v_num=zwmv, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   0%|          | 108/24289 [00:33<2:03:40,  3.26it/s, v_num=zwmv, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   0%|          | 108/24289 [00:33<2:03:40,  3.26it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   0%|          | 109/24289 [00:33<2:03:31,  3.26it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   0%|          | 109/24289 [00:33<2:03:31,  3.26it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   0%|          | 110/24289 [00:33<2:03:43,  3.26it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   0%|          | 110/24289 [00:33<2:03:43,  3.26it/s, v_num=zwmv, train_loss_step=0.0913, train_metrics/recon_loss_step=0.0913]
Epoch 0:   0%|          | 111/24289 [00:34<2:03:26,  3.26it/s, v_num=zwmv, train_loss_step=0.0913, train_metrics/recon_loss_step=0.0913]
Epoch 0:   0%|          | 111/24289 [00:34<2:03:26,  3.26it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]  
Epoch 0:   0%|          | 112/24289 [00:34<2:03:14,  3.27it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   0%|          | 112/24289 [00:34<2:03:14,  3.27it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   0%|          | 113/24289 [00:34<2:03:01,  3.28it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   0%|          | 113/24289 [00:34<2:03:01,  3.28it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   0%|          | 114/24289 [00:34<2:02:59,  3.28it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   0%|          | 114/24289 [00:34<2:02:59,  3.28it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   0%|          | 115/24289 [00:35<2:02:58,  3.28it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   0%|          | 115/24289 [00:35<2:02:58,  3.28it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   0%|          | 116/24289 [00:35<2:02:54,  3.28it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   0%|          | 116/24289 [00:35<2:02:55,  3.28it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 117/24289 [00:35<2:03:01,  3.27it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   0%|          | 117/24289 [00:35<2:03:02,  3.27it/s, v_num=zwmv, train_loss_step=0.0917, train_metrics/recon_loss_step=0.0917]
Epoch 0:   0%|          | 118/24289 [00:36<2:02:58,  3.28it/s, v_num=zwmv, train_loss_step=0.0917, train_metrics/recon_loss_step=0.0917]
Epoch 0:   0%|          | 118/24289 [00:36<2:02:59,  3.28it/s, v_num=zwmv, train_loss_step=0.00537, train_metrics/recon_loss_step=0.00537]
Epoch 0:   0%|          | 119/24289 [00:36<2:03:11,  3.27it/s, v_num=zwmv, train_loss_step=0.00537, train_metrics/recon_loss_step=0.00537]
Epoch 0:   0%|          | 119/24289 [00:36<2:03:11,  3.27it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]    
Epoch 0:   0%|          | 120/24289 [00:36<2:02:54,  3.28it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   0%|          | 120/24289 [00:36<2:02:54,  3.28it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   0%|          | 121/24289 [00:36<2:02:40,  3.28it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   0%|          | 121/24289 [00:36<2:02:40,  3.28it/s, v_num=zwmv, train_loss_step=0.073, train_metrics/recon_loss_step=0.073]
Epoch 0:   1%|          | 122/24289 [00:37<2:02:39,  3.28it/s, v_num=zwmv, train_loss_step=0.073, train_metrics/recon_loss_step=0.073]
Epoch 0:   1%|          | 122/24289 [00:37<2:02:39,  3.28it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   1%|          | 123/24289 [00:37<2:02:23,  3.29it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   1%|          | 123/24289 [00:37<2:02:23,  3.29it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   1%|          | 124/24289 [00:37<2:02:19,  3.29it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   1%|          | 124/24289 [00:37<2:02:19,  3.29it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   1%|          | 125/24289 [00:37<2:02:09,  3.30it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   1%|          | 125/24289 [00:37<2:02:09,  3.30it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 126/24289 [00:38<2:02:09,  3.30it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 126/24289 [00:38<2:02:09,  3.30it/s, v_num=zwmv, train_loss_step=0.0999, train_metrics/recon_loss_step=0.0999]
Epoch 0:   1%|          | 127/24289 [00:38<2:01:57,  3.30it/s, v_num=zwmv, train_loss_step=0.0999, train_metrics/recon_loss_step=0.0999]
Epoch 0:   1%|          | 127/24289 [00:38<2:01:57,  3.30it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   1%|          | 128/24289 [00:38<2:01:47,  3.31it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 128/24289 [00:38<2:01:47,  3.31it/s, v_num=zwmv, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]
Epoch 0:   1%|          | 129/24289 [00:39<2:01:44,  3.31it/s, v_num=zwmv, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]
Epoch 0:   1%|          | 129/24289 [00:39<2:01:44,  3.31it/s, v_num=zwmv, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   1%|          | 130/24289 [00:39<2:01:31,  3.31it/s, v_num=zwmv, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   1%|          | 130/24289 [00:39<2:01:31,  3.31it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 131/24289 [00:39<2:01:17,  3.32it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 131/24289 [00:39<2:01:17,  3.32it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   1%|          | 132/24289 [00:39<2:01:16,  3.32it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   1%|          | 132/24289 [00:39<2:01:16,  3.32it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 133/24289 [00:39<2:01:04,  3.33it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 133/24289 [00:39<2:01:04,  3.33it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   1%|          | 134/24289 [00:40<2:01:09,  3.32it/s, v_num=zwmv, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   1%|          | 134/24289 [00:40<2:01:09,  3.32it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   1%|          | 135/24289 [00:40<2:01:00,  3.33it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   1%|          | 135/24289 [00:40<2:01:00,  3.33it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 136/24289 [00:40<2:00:48,  3.33it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 136/24289 [00:40<2:00:48,  3.33it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|          | 137/24289 [00:41<2:00:40,  3.34it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|          | 137/24289 [00:41<2:00:41,  3.34it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 138/24289 [00:41<2:00:52,  3.33it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 138/24289 [00:41<2:00:52,  3.33it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   1%|          | 139/24289 [00:41<2:00:47,  3.33it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   1%|          | 139/24289 [00:41<2:00:47,  3.33it/s, v_num=zwmv, train_loss_step=0.0179, train_metrics/recon_loss_step=0.0179]
Epoch 0:   1%|          | 140/24289 [00:41<2:00:37,  3.34it/s, v_num=zwmv, train_loss_step=0.0179, train_metrics/recon_loss_step=0.0179]
Epoch 0:   1%|          | 140/24289 [00:41<2:00:37,  3.34it/s, v_num=zwmv, train_loss_step=0.0987, train_metrics/recon_loss_step=0.0987]
Epoch 0:   1%|          | 141/24289 [00:42<2:00:29,  3.34it/s, v_num=zwmv, train_loss_step=0.0987, train_metrics/recon_loss_step=0.0987]
Epoch 0:   1%|          | 141/24289 [00:42<2:00:29,  3.34it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]  
Epoch 0:   1%|          | 142/24289 [00:42<2:00:17,  3.35it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   1%|          | 142/24289 [00:42<2:00:17,  3.35it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|          | 143/24289 [00:42<2:00:02,  3.35it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|          | 143/24289 [00:42<2:00:03,  3.35it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   1%|          | 144/24289 [00:42<1:59:57,  3.35it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   1%|          | 144/24289 [00:42<1:59:57,  3.35it/s, v_num=zwmv, train_loss_step=0.253, train_metrics/recon_loss_step=0.253]
Epoch 0:   1%|          | 145/24289 [00:43<1:59:56,  3.35it/s, v_num=zwmv, train_loss_step=0.253, train_metrics/recon_loss_step=0.253]
Epoch 0:   1%|          | 145/24289 [00:43<1:59:57,  3.35it/s, v_num=zwmv, train_loss_step=0.169, train_metrics/recon_loss_step=0.169]
Epoch 0:   1%|          | 146/24289 [00:43<1:59:54,  3.36it/s, v_num=zwmv, train_loss_step=0.169, train_metrics/recon_loss_step=0.169]
Epoch 0:   1%|          | 146/24289 [00:43<1:59:54,  3.36it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   1%|          | 147/24289 [00:43<2:00:07,  3.35it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   1%|          | 147/24289 [00:43<2:00:07,  3.35it/s, v_num=zwmv, train_loss_step=0.0264, train_metrics/recon_loss_step=0.0264]
Epoch 0:   1%|          | 148/24289 [00:44<2:00:08,  3.35it/s, v_num=zwmv, train_loss_step=0.0264, train_metrics/recon_loss_step=0.0264]
Epoch 0:   1%|          | 148/24289 [00:44<2:00:09,  3.35it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   1%|          | 149/24289 [00:44<1:59:56,  3.35it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 149/24289 [00:44<1:59:57,  3.35it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 150/24289 [00:44<2:00:05,  3.35it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 150/24289 [00:44<2:00:05,  3.35it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 151/24289 [00:45<2:00:00,  3.35it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 151/24289 [00:45<2:00:01,  3.35it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|          | 152/24289 [00:45<1:59:59,  3.35it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|          | 152/24289 [00:45<1:59:59,  3.35it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 153/24289 [00:45<2:00:01,  3.35it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 153/24289 [00:45<2:00:01,  3.35it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   1%|          | 154/24289 [00:45<1:59:53,  3.36it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   1%|          | 154/24289 [00:45<1:59:53,  3.36it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 155/24289 [00:46<1:59:44,  3.36it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 155/24289 [00:46<1:59:44,  3.36it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 156/24289 [00:46<1:59:45,  3.36it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 156/24289 [00:46<1:59:45,  3.36it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   1%|          | 157/24289 [00:46<1:59:38,  3.36it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   1%|          | 157/24289 [00:46<1:59:38,  3.36it/s, v_num=zwmv, train_loss_step=0.269, train_metrics/recon_loss_step=0.269]  
Epoch 0:   1%|          | 158/24289 [00:46<1:59:31,  3.36it/s, v_num=zwmv, train_loss_step=0.269, train_metrics/recon_loss_step=0.269]
Epoch 0:   1%|          | 158/24289 [00:46<1:59:31,  3.36it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   1%|          | 159/24289 [00:47<1:59:20,  3.37it/s, v_num=zwmv, train_loss_step=0.217, train_metrics/recon_loss_step=0.217]
Epoch 0:   1%|          | 159/24289 [00:47<1:59:20,  3.37it/s, v_num=zwmv, train_loss_step=0.0866, train_metrics/recon_loss_step=0.0866]
Epoch 0:   1%|          | 160/24289 [00:47<1:59:12,  3.37it/s, v_num=zwmv, train_loss_step=0.0866, train_metrics/recon_loss_step=0.0866]
Epoch 0:   1%|          | 160/24289 [00:47<1:59:12,  3.37it/s, v_num=zwmv, train_loss_step=0.212, train_metrics/recon_loss_step=0.212]  
Epoch 0:   1%|          | 161/24289 [00:47<1:59:04,  3.38it/s, v_num=zwmv, train_loss_step=0.212, train_metrics/recon_loss_step=0.212]
Epoch 0:   1%|          | 161/24289 [00:47<1:59:04,  3.38it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|          | 162/24289 [00:47<1:58:58,  3.38it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|          | 162/24289 [00:47<1:58:58,  3.38it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   1%|          | 163/24289 [00:48<1:58:56,  3.38it/s, v_num=zwmv, train_loss_step=0.220, train_metrics/recon_loss_step=0.220]
Epoch 0:   1%|          | 163/24289 [00:48<1:58:56,  3.38it/s, v_num=zwmv, train_loss_step=0.354, train_metrics/recon_loss_step=0.354]
Epoch 0:   1%|          | 164/24289 [00:48<1:58:54,  3.38it/s, v_num=zwmv, train_loss_step=0.354, train_metrics/recon_loss_step=0.354]
Epoch 0:   1%|          | 164/24289 [00:48<1:58:55,  3.38it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 165/24289 [00:48<1:58:51,  3.38it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 165/24289 [00:48<1:58:52,  3.38it/s, v_num=zwmv, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]
Epoch 0:   1%|          | 166/24289 [00:49<1:58:48,  3.38it/s, v_num=zwmv, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]
Epoch 0:   1%|          | 166/24289 [00:49<1:58:48,  3.38it/s, v_num=zwmv, train_loss_step=0.191, train_metrics/recon_loss_step=0.191]
Epoch 0:   1%|          | 167/24289 [00:49<1:58:37,  3.39it/s, v_num=zwmv, train_loss_step=0.191, train_metrics/recon_loss_step=0.191]
Epoch 0:   1%|          | 167/24289 [00:49<1:58:38,  3.39it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   1%|          | 168/24289 [00:49<1:58:29,  3.39it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   1%|          | 168/24289 [00:49<1:58:29,  3.39it/s, v_num=zwmv, train_loss_step=0.0836, train_metrics/recon_loss_step=0.0836]
Epoch 0:   1%|          | 169/24289 [00:49<1:58:18,  3.40it/s, v_num=zwmv, train_loss_step=0.0836, train_metrics/recon_loss_step=0.0836]
Epoch 0:   1%|          | 169/24289 [00:49<1:58:18,  3.40it/s, v_num=zwmv, train_loss_step=0.099, train_metrics/recon_loss_step=0.099]  
Epoch 0:   1%|          | 170/24289 [00:49<1:58:08,  3.40it/s, v_num=zwmv, train_loss_step=0.099, train_metrics/recon_loss_step=0.099]
Epoch 0:   1%|          | 170/24289 [00:49<1:58:08,  3.40it/s, v_num=zwmv, train_loss_step=0.262, train_metrics/recon_loss_step=0.262]
Epoch 0:   1%|          | 171/24289 [00:50<1:58:00,  3.41it/s, v_num=zwmv, train_loss_step=0.262, train_metrics/recon_loss_step=0.262]
Epoch 0:   1%|          | 171/24289 [00:50<1:58:00,  3.41it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   1%|          | 172/24289 [00:50<1:57:51,  3.41it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   1%|          | 172/24289 [00:50<1:57:51,  3.41it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|          | 173/24289 [00:50<1:57:45,  3.41it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|          | 173/24289 [00:50<1:57:45,  3.41it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   1%|          | 174/24289 [00:50<1:57:37,  3.42it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   1%|          | 174/24289 [00:50<1:57:37,  3.42it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   1%|          | 175/24289 [00:51<1:57:32,  3.42it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   1%|          | 175/24289 [00:51<1:57:32,  3.42it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 176/24289 [00:51<1:57:27,  3.42it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 176/24289 [00:51<1:57:27,  3.42it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   1%|          | 177/24289 [00:51<1:57:19,  3.43it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   1%|          | 177/24289 [00:51<1:57:20,  3.42it/s, v_num=zwmv, train_loss_step=0.0794, train_metrics/recon_loss_step=0.0794]
Epoch 0:   1%|          | 178/24289 [00:51<1:57:20,  3.42it/s, v_num=zwmv, train_loss_step=0.0794, train_metrics/recon_loss_step=0.0794]
Epoch 0:   1%|          | 178/24289 [00:51<1:57:20,  3.42it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]  
Epoch 0:   1%|          | 179/24289 [00:52<1:57:24,  3.42it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]
Epoch 0:   1%|          | 179/24289 [00:52<1:57:24,  3.42it/s, v_num=zwmv, train_loss_step=0.0724, train_metrics/recon_loss_step=0.0724]
Epoch 0:   1%|          | 180/24289 [00:52<1:57:15,  3.43it/s, v_num=zwmv, train_loss_step=0.0724, train_metrics/recon_loss_step=0.0724]
Epoch 0:   1%|          | 180/24289 [00:52<1:57:15,  3.43it/s, v_num=zwmv, train_loss_step=0.077, train_metrics/recon_loss_step=0.077]  
Epoch 0:   1%|          | 181/24289 [00:52<1:57:13,  3.43it/s, v_num=zwmv, train_loss_step=0.077, train_metrics/recon_loss_step=0.077]
Epoch 0:   1%|          | 181/24289 [00:52<1:57:13,  3.43it/s, v_num=zwmv, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]
Epoch 0:   1%|          | 182/24289 [00:53<1:57:11,  3.43it/s, v_num=zwmv, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]
Epoch 0:   1%|          | 182/24289 [00:53<1:57:11,  3.43it/s, v_num=zwmv, train_loss_step=0.00348, train_metrics/recon_loss_step=0.00348]
Epoch 0:   1%|          | 183/24289 [00:53<1:57:04,  3.43it/s, v_num=zwmv, train_loss_step=0.00348, train_metrics/recon_loss_step=0.00348]
Epoch 0:   1%|          | 183/24289 [00:53<1:57:04,  3.43it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]    
Epoch 0:   1%|          | 184/24289 [00:53<1:57:07,  3.43it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   1%|          | 184/24289 [00:53<1:57:08,  3.43it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 185/24289 [00:53<1:57:08,  3.43it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 185/24289 [00:53<1:57:08,  3.43it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 186/24289 [00:54<1:57:00,  3.43it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 186/24289 [00:54<1:57:00,  3.43it/s, v_num=zwmv, train_loss_step=0.0919, train_metrics/recon_loss_step=0.0919]
Epoch 0:   1%|          | 187/24289 [00:54<1:57:00,  3.43it/s, v_num=zwmv, train_loss_step=0.0919, train_metrics/recon_loss_step=0.0919]
Epoch 0:   1%|          | 187/24289 [00:54<1:57:01,  3.43it/s, v_num=zwmv, train_loss_step=0.0185, train_metrics/recon_loss_step=0.0185]
Epoch 0:   1%|          | 188/24289 [00:54<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.0185, train_metrics/recon_loss_step=0.0185]
Epoch 0:   1%|          | 188/24289 [00:54<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]  
Epoch 0:   1%|          | 189/24289 [00:55<1:57:01,  3.43it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]
Epoch 0:   1%|          | 189/24289 [00:55<1:57:01,  3.43it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 190/24289 [00:55<1:56:52,  3.44it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 190/24289 [00:55<1:56:52,  3.44it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 191/24289 [00:55<1:56:56,  3.43it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 191/24289 [00:55<1:56:56,  3.43it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 192/24289 [00:55<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 192/24289 [00:55<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]
Epoch 0:   1%|          | 193/24289 [00:56<1:57:02,  3.43it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]
Epoch 0:   1%|          | 193/24289 [00:56<1:57:02,  3.43it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 194/24289 [00:56<1:57:12,  3.43it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 194/24289 [00:56<1:57:12,  3.43it/s, v_num=zwmv, train_loss_step=0.275, train_metrics/recon_loss_step=0.275]
Epoch 0:   1%|          | 195/24289 [00:57<1:57:23,  3.42it/s, v_num=zwmv, train_loss_step=0.275, train_metrics/recon_loss_step=0.275]
Epoch 0:   1%|          | 195/24289 [00:57<1:57:23,  3.42it/s, v_num=zwmv, train_loss_step=0.028, train_metrics/recon_loss_step=0.028]
Epoch 0:   1%|          | 196/24289 [00:57<1:57:27,  3.42it/s, v_num=zwmv, train_loss_step=0.028, train_metrics/recon_loss_step=0.028]
Epoch 0:   1%|          | 196/24289 [00:57<1:57:27,  3.42it/s, v_num=zwmv, train_loss_step=0.0717, train_metrics/recon_loss_step=0.0717]
Epoch 0:   1%|          | 197/24289 [00:57<1:57:25,  3.42it/s, v_num=zwmv, train_loss_step=0.0717, train_metrics/recon_loss_step=0.0717]
Epoch 0:   1%|          | 197/24289 [00:57<1:57:25,  3.42it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]  
Epoch 0:   1%|          | 198/24289 [00:57<1:57:20,  3.42it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   1%|          | 198/24289 [00:57<1:57:20,  3.42it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   1%|          | 199/24289 [00:58<1:57:30,  3.42it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   1%|          | 199/24289 [00:58<1:57:30,  3.42it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]  
Epoch 0:   1%|          | 200/24289 [00:58<1:57:21,  3.42it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   1%|          | 200/24289 [00:58<1:57:21,  3.42it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 201/24289 [00:58<1:57:13,  3.42it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 201/24289 [00:58<1:57:13,  3.42it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   1%|          | 202/24289 [00:58<1:57:08,  3.43it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   1%|          | 202/24289 [00:58<1:57:08,  3.43it/s, v_num=zwmv, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]
Epoch 0:   1%|          | 203/24289 [00:59<1:57:07,  3.43it/s, v_num=zwmv, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]
Epoch 0:   1%|          | 203/24289 [00:59<1:57:07,  3.43it/s, v_num=zwmv, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]
Epoch 0:   1%|          | 204/24289 [00:59<1:57:05,  3.43it/s, v_num=zwmv, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]
Epoch 0:   1%|          | 204/24289 [00:59<1:57:05,  3.43it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   1%|          | 205/24289 [00:59<1:56:59,  3.43it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   1%|          | 205/24289 [00:59<1:56:59,  3.43it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 206/24289 [01:00<1:56:54,  3.43it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 206/24289 [01:00<1:56:54,  3.43it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|          | 207/24289 [01:00<1:56:47,  3.44it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|          | 207/24289 [01:00<1:56:47,  3.44it/s, v_num=zwmv, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|          | 208/24289 [01:00<1:56:40,  3.44it/s, v_num=zwmv, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|          | 208/24289 [01:00<1:56:40,  3.44it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]  
Epoch 0:   1%|          | 209/24289 [01:00<1:56:52,  3.43it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   1%|          | 209/24289 [01:00<1:56:52,  3.43it/s, v_num=zwmv, train_loss_step=0.0942, train_metrics/recon_loss_step=0.0942]
Epoch 0:   1%|          | 210/24289 [01:01<1:56:47,  3.44it/s, v_num=zwmv, train_loss_step=0.0942, train_metrics/recon_loss_step=0.0942]
Epoch 0:   1%|          | 210/24289 [01:01<1:56:47,  3.44it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]  
Epoch 0:   1%|          | 211/24289 [01:01<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 211/24289 [01:01<1:56:58,  3.43it/s, v_num=zwmv, train_loss_step=0.0309, train_metrics/recon_loss_step=0.0309]
Epoch 0:   1%|          | 212/24289 [01:01<1:56:53,  3.43it/s, v_num=zwmv, train_loss_step=0.0309, train_metrics/recon_loss_step=0.0309]
Epoch 0:   1%|          | 212/24289 [01:01<1:56:53,  3.43it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]  
Epoch 0:   1%|          | 213/24289 [01:02<1:56:55,  3.43it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   1%|          | 213/24289 [01:02<1:56:55,  3.43it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   1%|          | 214/24289 [01:02<1:56:51,  3.43it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   1%|          | 214/24289 [01:02<1:56:51,  3.43it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]
Epoch 0:   1%|          | 215/24289 [01:02<1:56:52,  3.43it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]
Epoch 0:   1%|          | 215/24289 [01:02<1:56:52,  3.43it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   1%|          | 216/24289 [01:02<1:56:45,  3.44it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   1%|          | 216/24289 [01:02<1:56:45,  3.44it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 217/24289 [01:03<1:56:43,  3.44it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 217/24289 [01:03<1:56:43,  3.44it/s, v_num=zwmv, train_loss_step=0.024, train_metrics/recon_loss_step=0.024]
Epoch 0:   1%|          | 218/24289 [01:03<1:56:37,  3.44it/s, v_num=zwmv, train_loss_step=0.024, train_metrics/recon_loss_step=0.024]
Epoch 0:   1%|          | 218/24289 [01:03<1:56:37,  3.44it/s, v_num=zwmv, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]
Epoch 0:   1%|          | 219/24289 [01:03<1:56:36,  3.44it/s, v_num=zwmv, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]
Epoch 0:   1%|          | 219/24289 [01:03<1:56:36,  3.44it/s, v_num=zwmv, train_loss_step=0.238, train_metrics/recon_loss_step=0.238]  
Epoch 0:   1%|          | 220/24289 [01:03<1:56:33,  3.44it/s, v_num=zwmv, train_loss_step=0.238, train_metrics/recon_loss_step=0.238]
Epoch 0:   1%|          | 220/24289 [01:03<1:56:33,  3.44it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   1%|          | 221/24289 [01:04<1:56:27,  3.44it/s, v_num=zwmv, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   1%|          | 221/24289 [01:04<1:56:27,  3.44it/s, v_num=zwmv, train_loss_step=0.0928, train_metrics/recon_loss_step=0.0928]
Epoch 0:   1%|          | 222/24289 [01:04<1:56:25,  3.45it/s, v_num=zwmv, train_loss_step=0.0928, train_metrics/recon_loss_step=0.0928]
Epoch 0:   1%|          | 222/24289 [01:04<1:56:25,  3.45it/s, v_num=zwmv, train_loss_step=0.0102, train_metrics/recon_loss_step=0.0102]
Epoch 0:   1%|          | 223/24289 [01:04<1:56:23,  3.45it/s, v_num=zwmv, train_loss_step=0.0102, train_metrics/recon_loss_step=0.0102]
Epoch 0:   1%|          | 223/24289 [01:04<1:56:23,  3.45it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]  
Epoch 0:   1%|          | 224/24289 [01:04<1:56:17,  3.45it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   1%|          | 224/24289 [01:04<1:56:18,  3.45it/s, v_num=zwmv, train_loss_step=0.0893, train_metrics/recon_loss_step=0.0893]
Epoch 0:   1%|          | 225/24289 [01:05<1:56:14,  3.45it/s, v_num=zwmv, train_loss_step=0.0893, train_metrics/recon_loss_step=0.0893]
Epoch 0:   1%|          | 225/24289 [01:05<1:56:14,  3.45it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]  
Epoch 0:   1%|          | 226/24289 [01:05<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   1%|          | 226/24289 [01:05<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.0563, train_metrics/recon_loss_step=0.0563]
Epoch 0:   1%|          | 227/24289 [01:05<1:56:11,  3.45it/s, v_num=zwmv, train_loss_step=0.0563, train_metrics/recon_loss_step=0.0563]
Epoch 0:   1%|          | 227/24289 [01:05<1:56:12,  3.45it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]  
Epoch 0:   1%|          | 228/24289 [01:06<1:56:12,  3.45it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 228/24289 [01:06<1:56:12,  3.45it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   1%|          | 229/24289 [01:06<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   1%|          | 229/24289 [01:06<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   1%|          | 230/24289 [01:06<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   1%|          | 230/24289 [01:06<1:56:07,  3.45it/s, v_num=zwmv, train_loss_step=0.244, train_metrics/recon_loss_step=0.244]
Epoch 0:   1%|          | 231/24289 [01:06<1:56:16,  3.45it/s, v_num=zwmv, train_loss_step=0.244, train_metrics/recon_loss_step=0.244]
Epoch 0:   1%|          | 231/24289 [01:06<1:56:16,  3.45it/s, v_num=zwmv, train_loss_step=0.069, train_metrics/recon_loss_step=0.069]
Epoch 0:   1%|          | 232/24289 [01:07<1:56:25,  3.44it/s, v_num=zwmv, train_loss_step=0.069, train_metrics/recon_loss_step=0.069]
Epoch 0:   1%|          | 232/24289 [01:07<1:56:25,  3.44it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|          | 233/24289 [01:07<1:56:20,  3.45it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|          | 233/24289 [01:07<1:56:20,  3.45it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 234/24289 [01:07<1:56:14,  3.45it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 234/24289 [01:07<1:56:14,  3.45it/s, v_num=zwmv, train_loss_step=0.0999, train_metrics/recon_loss_step=0.0999]
Epoch 0:   1%|          | 235/24289 [01:08<1:56:08,  3.45it/s, v_num=zwmv, train_loss_step=0.0999, train_metrics/recon_loss_step=0.0999]
Epoch 0:   1%|          | 235/24289 [01:08<1:56:08,  3.45it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]  
Epoch 0:   1%|          | 236/24289 [01:08<1:56:04,  3.45it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|          | 236/24289 [01:08<1:56:04,  3.45it/s, v_num=zwmv, train_loss_step=0.0719, train_metrics/recon_loss_step=0.0719]
Epoch 0:   1%|          | 237/24289 [01:08<1:56:09,  3.45it/s, v_num=zwmv, train_loss_step=0.0719, train_metrics/recon_loss_step=0.0719]
Epoch 0:   1%|          | 237/24289 [01:08<1:56:09,  3.45it/s, v_num=zwmv, train_loss_step=0.0835, train_metrics/recon_loss_step=0.0835]
Epoch 0:   1%|          | 238/24289 [01:08<1:56:05,  3.45it/s, v_num=zwmv, train_loss_step=0.0835, train_metrics/recon_loss_step=0.0835]
Epoch 0:   1%|          | 238/24289 [01:08<1:56:05,  3.45it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]  
Epoch 0:   1%|          | 239/24289 [01:09<1:56:02,  3.45it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 239/24289 [01:09<1:56:02,  3.45it/s, v_num=zwmv, train_loss_step=0.236, train_metrics/recon_loss_step=0.236]
Epoch 0:   1%|          | 240/24289 [01:09<1:56:00,  3.46it/s, v_num=zwmv, train_loss_step=0.236, train_metrics/recon_loss_step=0.236]
Epoch 0:   1%|          | 240/24289 [01:09<1:56:00,  3.46it/s, v_num=zwmv, train_loss_step=0.0582, train_metrics/recon_loss_step=0.0582]
Epoch 0:   1%|          | 241/24289 [01:09<1:55:53,  3.46it/s, v_num=zwmv, train_loss_step=0.0582, train_metrics/recon_loss_step=0.0582]
Epoch 0:   1%|          | 241/24289 [01:09<1:55:53,  3.46it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]  
Epoch 0:   1%|          | 242/24289 [01:09<1:55:48,  3.46it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 242/24289 [01:09<1:55:49,  3.46it/s, v_num=zwmv, train_loss_step=0.0623, train_metrics/recon_loss_step=0.0623]
Epoch 0:   1%|          | 243/24289 [01:10<1:55:55,  3.46it/s, v_num=zwmv, train_loss_step=0.0623, train_metrics/recon_loss_step=0.0623]
Epoch 0:   1%|          | 243/24289 [01:10<1:55:55,  3.46it/s, v_num=zwmv, train_loss_step=0.295, train_metrics/recon_loss_step=0.295]  
Epoch 0:   1%|          | 244/24289 [01:10<1:55:56,  3.46it/s, v_num=zwmv, train_loss_step=0.295, train_metrics/recon_loss_step=0.295]
Epoch 0:   1%|          | 244/24289 [01:10<1:55:56,  3.46it/s, v_num=zwmv, train_loss_step=0.0812, train_metrics/recon_loss_step=0.0812]
Epoch 0:   1%|          | 245/24289 [01:10<1:55:48,  3.46it/s, v_num=zwmv, train_loss_step=0.0812, train_metrics/recon_loss_step=0.0812]
Epoch 0:   1%|          | 245/24289 [01:10<1:55:48,  3.46it/s, v_num=zwmv, train_loss_step=0.0837, train_metrics/recon_loss_step=0.0837]
Epoch 0:   1%|          | 246/24289 [01:11<1:55:43,  3.46it/s, v_num=zwmv, train_loss_step=0.0837, train_metrics/recon_loss_step=0.0837]
Epoch 0:   1%|          | 246/24289 [01:11<1:55:43,  3.46it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]  
Epoch 0:   1%|          | 247/24289 [01:11<1:55:40,  3.46it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 247/24289 [01:11<1:55:40,  3.46it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   1%|          | 248/24289 [01:11<1:55:37,  3.47it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   1%|          | 248/24289 [01:11<1:55:37,  3.47it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 249/24289 [01:11<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 249/24289 [01:11<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.0514, train_metrics/recon_loss_step=0.0514]
Epoch 0:   1%|          | 250/24289 [01:12<1:55:35,  3.47it/s, v_num=zwmv, train_loss_step=0.0514, train_metrics/recon_loss_step=0.0514]
Epoch 0:   1%|          | 250/24289 [01:12<1:55:36,  3.47it/s, v_num=zwmv, train_loss_step=0.0681, train_metrics/recon_loss_step=0.0681]
Epoch 0:   1%|          | 251/24289 [01:12<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.0681, train_metrics/recon_loss_step=0.0681]
Epoch 0:   1%|          | 251/24289 [01:12<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.0875, train_metrics/recon_loss_step=0.0875]
Epoch 0:   1%|          | 252/24289 [01:12<1:55:39,  3.46it/s, v_num=zwmv, train_loss_step=0.0875, train_metrics/recon_loss_step=0.0875]
Epoch 0:   1%|          | 252/24289 [01:12<1:55:39,  3.46it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  
Epoch 0:   1%|          | 253/24289 [01:13<1:55:39,  3.46it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   1%|          | 253/24289 [01:13<1:55:39,  3.46it/s, v_num=zwmv, train_loss_step=0.265, train_metrics/recon_loss_step=0.265]
Epoch 0:   1%|          | 254/24289 [01:13<1:55:35,  3.47it/s, v_num=zwmv, train_loss_step=0.265, train_metrics/recon_loss_step=0.265]
Epoch 0:   1%|          | 254/24289 [01:13<1:55:35,  3.47it/s, v_num=zwmv, train_loss_step=0.0783, train_metrics/recon_loss_step=0.0783]
Epoch 0:   1%|          | 255/24289 [01:13<1:55:35,  3.47it/s, v_num=zwmv, train_loss_step=0.0783, train_metrics/recon_loss_step=0.0783]
Epoch 0:   1%|          | 255/24289 [01:13<1:55:35,  3.47it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]  
Epoch 0:   1%|          | 256/24289 [01:13<1:55:33,  3.47it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   1%|          | 256/24289 [01:13<1:55:33,  3.47it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 257/24289 [01:14<1:55:48,  3.46it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 257/24289 [01:14<1:55:48,  3.46it/s, v_num=zwmv, train_loss_step=0.0824, train_metrics/recon_loss_step=0.0824]
Epoch 0:   1%|          | 258/24289 [01:14<1:55:55,  3.46it/s, v_num=zwmv, train_loss_step=0.0824, train_metrics/recon_loss_step=0.0824]
Epoch 0:   1%|          | 258/24289 [01:14<1:55:55,  3.46it/s, v_num=zwmv, train_loss_step=0.0918, train_metrics/recon_loss_step=0.0918]
Epoch 0:   1%|          | 259/24289 [01:14<1:55:50,  3.46it/s, v_num=zwmv, train_loss_step=0.0918, train_metrics/recon_loss_step=0.0918]
Epoch 0:   1%|          | 259/24289 [01:14<1:55:50,  3.46it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   1%|          | 260/24289 [01:15<1:55:58,  3.45it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 260/24289 [01:15<1:55:58,  3.45it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   1%|          | 261/24289 [01:15<1:55:51,  3.46it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   1%|          | 261/24289 [01:15<1:55:52,  3.46it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   1%|          | 262/24289 [01:15<1:55:45,  3.46it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   1%|          | 262/24289 [01:15<1:55:46,  3.46it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   1%|          | 263/24289 [01:15<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   1%|          | 263/24289 [01:15<1:55:41,  3.46it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|          | 264/24289 [01:16<1:55:38,  3.46it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|          | 264/24289 [01:16<1:55:38,  3.46it/s, v_num=zwmv, train_loss_step=0.0652, train_metrics/recon_loss_step=0.0652]
Epoch 0:   1%|          | 265/24289 [01:16<1:55:34,  3.46it/s, v_num=zwmv, train_loss_step=0.0652, train_metrics/recon_loss_step=0.0652]
Epoch 0:   1%|          | 265/24289 [01:16<1:55:34,  3.46it/s, v_num=zwmv, train_loss_step=0.0649, train_metrics/recon_loss_step=0.0649]
Epoch 0:   1%|          | 266/24289 [01:16<1:55:31,  3.47it/s, v_num=zwmv, train_loss_step=0.0649, train_metrics/recon_loss_step=0.0649]
Epoch 0:   1%|          | 266/24289 [01:16<1:55:31,  3.47it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]  
Epoch 0:   1%|          | 267/24289 [01:16<1:55:26,  3.47it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 267/24289 [01:16<1:55:26,  3.47it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 268/24289 [01:17<1:55:22,  3.47it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 268/24289 [01:17<1:55:22,  3.47it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   1%|          | 269/24289 [01:17<1:55:19,  3.47it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   1%|          | 269/24289 [01:17<1:55:19,  3.47it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 270/24289 [01:17<1:55:27,  3.47it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 270/24289 [01:17<1:55:27,  3.47it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   1%|          | 271/24289 [01:18<1:55:24,  3.47it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   1%|          | 271/24289 [01:18<1:55:24,  3.47it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   1%|          | 272/24289 [01:18<1:55:25,  3.47it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   1%|          | 272/24289 [01:18<1:55:25,  3.47it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 273/24289 [01:18<1:55:20,  3.47it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   1%|          | 273/24289 [01:18<1:55:20,  3.47it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   1%|          | 274/24289 [01:18<1:55:20,  3.47it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   1%|          | 274/24289 [01:18<1:55:20,  3.47it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   1%|          | 275/24289 [01:19<1:55:14,  3.47it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   1%|          | 275/24289 [01:19<1:55:14,  3.47it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 276/24289 [01:19<1:55:21,  3.47it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 276/24289 [01:19<1:55:21,  3.47it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 277/24289 [01:19<1:55:18,  3.47it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 277/24289 [01:19<1:55:18,  3.47it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|          | 278/24289 [01:20<1:55:17,  3.47it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|          | 278/24289 [01:20<1:55:17,  3.47it/s, v_num=zwmv, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   1%|          | 279/24289 [01:20<1:55:13,  3.47it/s, v_num=zwmv, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   1%|          | 279/24289 [01:20<1:55:13,  3.47it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 280/24289 [01:20<1:55:16,  3.47it/s, v_num=zwmv, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 280/24289 [01:20<1:55:16,  3.47it/s, v_num=zwmv, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]
Epoch 0:   1%|          | 281/24289 [01:21<1:55:23,  3.47it/s, v_num=zwmv, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]
Epoch 0:   1%|          | 281/24289 [01:21<1:55:23,  3.47it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   1%|          | 282/24289 [01:21<1:55:17,  3.47it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   1%|          | 282/24289 [01:21<1:55:17,  3.47it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 283/24289 [01:21<1:55:14,  3.47it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 283/24289 [01:21<1:55:14,  3.47it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   1%|          | 284/24289 [01:21<1:55:10,  3.47it/s, v_num=zwmv, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   1%|          | 284/24289 [01:21<1:55:10,  3.47it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   1%|          | 285/24289 [01:21<1:55:04,  3.48it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   1%|          | 285/24289 [01:21<1:55:04,  3.48it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]  
Epoch 0:   1%|          | 286/24289 [01:22<1:55:00,  3.48it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 286/24289 [01:22<1:55:00,  3.48it/s, v_num=zwmv, train_loss_step=0.0903, train_metrics/recon_loss_step=0.0903]
Epoch 0:   1%|          | 287/24289 [01:22<1:55:00,  3.48it/s, v_num=zwmv, train_loss_step=0.0903, train_metrics/recon_loss_step=0.0903]
Epoch 0:   1%|          | 287/24289 [01:22<1:55:00,  3.48it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   1%|          | 288/24289 [01:22<1:54:56,  3.48it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   1%|          | 288/24289 [01:22<1:54:56,  3.48it/s, v_num=zwmv, train_loss_step=0.0563, train_metrics/recon_loss_step=0.0563]
Epoch 0:   1%|          | 289/24289 [01:23<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.0563, train_metrics/recon_loss_step=0.0563]
Epoch 0:   1%|          | 289/24289 [01:23<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]  
Epoch 0:   1%|          | 290/24289 [01:23<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]
Epoch 0:   1%|          | 290/24289 [01:23<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 291/24289 [01:23<1:54:45,  3.49it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 291/24289 [01:23<1:54:45,  3.49it/s, v_num=zwmv, train_loss_step=0.0968, train_metrics/recon_loss_step=0.0968]
Epoch 0:   1%|          | 292/24289 [01:23<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.0968, train_metrics/recon_loss_step=0.0968]
Epoch 0:   1%|          | 292/24289 [01:23<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.0536, train_metrics/recon_loss_step=0.0536]
Epoch 0:   1%|          | 293/24289 [01:24<1:54:47,  3.48it/s, v_num=zwmv, train_loss_step=0.0536, train_metrics/recon_loss_step=0.0536]
Epoch 0:   1%|          | 293/24289 [01:24<1:54:47,  3.48it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]  
Epoch 0:   1%|          | 294/24289 [01:24<1:54:46,  3.48it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   1%|          | 294/24289 [01:24<1:54:46,  3.48it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   1%|          | 295/24289 [01:24<1:54:53,  3.48it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   1%|          | 295/24289 [01:24<1:54:53,  3.48it/s, v_num=zwmv, train_loss_step=0.0315, train_metrics/recon_loss_step=0.0315]
Epoch 0:   1%|          | 296/24289 [01:25<1:54:52,  3.48it/s, v_num=zwmv, train_loss_step=0.0315, train_metrics/recon_loss_step=0.0315]
Epoch 0:   1%|          | 296/24289 [01:25<1:54:52,  3.48it/s, v_num=zwmv, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]  
Epoch 0:   1%|          | 297/24289 [01:25<1:54:47,  3.48it/s, v_num=zwmv, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]
Epoch 0:   1%|          | 297/24289 [01:25<1:54:47,  3.48it/s, v_num=zwmv, train_loss_step=0.0848, train_metrics/recon_loss_step=0.0848]
Epoch 0:   1%|          | 298/24289 [01:25<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.0848, train_metrics/recon_loss_step=0.0848]
Epoch 0:   1%|          | 298/24289 [01:25<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]  
Epoch 0:   1%|          | 299/24289 [01:25<1:54:44,  3.48it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]
Epoch 0:   1%|          | 299/24289 [01:25<1:54:44,  3.48it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   1%|          | 300/24289 [01:26<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   1%|          | 300/24289 [01:26<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.150, train_metrics/recon_loss_step=0.150]
Epoch 0:   1%|          | 301/24289 [01:26<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.150, train_metrics/recon_loss_step=0.150]
Epoch 0:   1%|          | 301/24289 [01:26<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   1%|          | 302/24289 [01:26<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   1%|          | 302/24289 [01:26<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 303/24289 [01:27<1:54:51,  3.48it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 303/24289 [01:27<1:54:51,  3.48it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   1%|▏         | 304/24289 [01:27<1:54:51,  3.48it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   1%|▏         | 304/24289 [01:27<1:54:51,  3.48it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|▏         | 305/24289 [01:27<1:54:52,  3.48it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|▏         | 305/24289 [01:27<1:54:52,  3.48it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|▏         | 306/24289 [01:28<1:55:02,  3.47it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|▏         | 306/24289 [01:28<1:55:02,  3.47it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|▏         | 307/24289 [01:28<1:54:59,  3.48it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|▏         | 307/24289 [01:28<1:54:59,  3.48it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|▏         | 308/24289 [01:28<1:54:53,  3.48it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|▏         | 308/24289 [01:28<1:54:53,  3.48it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|▏         | 309/24289 [01:28<1:55:06,  3.47it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|▏         | 309/24289 [01:28<1:55:06,  3.47it/s, v_num=zwmv, train_loss_step=0.235, train_metrics/recon_loss_step=0.235]
Epoch 0:   1%|▏         | 310/24289 [01:29<1:55:00,  3.47it/s, v_num=zwmv, train_loss_step=0.235, train_metrics/recon_loss_step=0.235]
Epoch 0:   1%|▏         | 310/24289 [01:29<1:55:01,  3.47it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   1%|▏         | 311/24289 [01:29<1:54:56,  3.48it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   1%|▏         | 311/24289 [01:29<1:54:56,  3.48it/s, v_num=zwmv, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]
Epoch 0:   1%|▏         | 312/24289 [01:29<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]
Epoch 0:   1%|▏         | 312/24289 [01:29<1:54:51,  3.48it/s, v_num=zwmv, train_loss_step=0.169, train_metrics/recon_loss_step=0.169]
Epoch 0:   1%|▏         | 313/24289 [01:29<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.169, train_metrics/recon_loss_step=0.169]
Epoch 0:   1%|▏         | 313/24289 [01:29<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|▏         | 314/24289 [01:30<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|▏         | 314/24289 [01:30<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]  
Epoch 0:   1%|▏         | 315/24289 [01:30<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]
Epoch 0:   1%|▏         | 315/24289 [01:30<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]
Epoch 0:   1%|▏         | 316/24289 [01:30<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]
Epoch 0:   1%|▏         | 316/24289 [01:30<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|▏         | 317/24289 [01:31<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|▏         | 317/24289 [01:31<1:54:55,  3.48it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   1%|▏         | 318/24289 [01:31<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   1%|▏         | 318/24289 [01:31<1:54:54,  3.48it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|▏         | 319/24289 [01:31<1:54:49,  3.48it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|▏         | 319/24289 [01:31<1:54:50,  3.48it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   1%|▏         | 320/24289 [01:31<1:54:45,  3.48it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   1%|▏         | 320/24289 [01:31<1:54:45,  3.48it/s, v_num=zwmv, train_loss_step=0.0838, train_metrics/recon_loss_step=0.0838]
Epoch 0:   1%|▏         | 321/24289 [01:32<1:54:42,  3.48it/s, v_num=zwmv, train_loss_step=0.0838, train_metrics/recon_loss_step=0.0838]
Epoch 0:   1%|▏         | 321/24289 [01:32<1:54:42,  3.48it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]  
Epoch 0:   1%|▏         | 322/24289 [01:32<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.171, train_metrics/recon_loss_step=0.171]
Epoch 0:   1%|▏         | 322/24289 [01:32<1:54:48,  3.48it/s, v_num=zwmv, train_loss_step=0.018, train_metrics/recon_loss_step=0.018]
Epoch 0:   1%|▏         | 323/24289 [01:32<1:54:46,  3.48it/s, v_num=zwmv, train_loss_step=0.018, train_metrics/recon_loss_step=0.018]
Epoch 0:   1%|▏         | 323/24289 [01:32<1:54:46,  3.48it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 324/24289 [01:33<1:54:45,  3.48it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 324/24289 [01:33<1:54:45,  3.48it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   1%|▏         | 325/24289 [01:33<1:54:42,  3.48it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   1%|▏         | 325/24289 [01:33<1:54:42,  3.48it/s, v_num=zwmv, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]
Epoch 0:   1%|▏         | 326/24289 [01:33<1:54:37,  3.48it/s, v_num=zwmv, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]
Epoch 0:   1%|▏         | 326/24289 [01:33<1:54:37,  3.48it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   1%|▏         | 327/24289 [01:33<1:54:35,  3.49it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   1%|▏         | 327/24289 [01:33<1:54:35,  3.49it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   1%|▏         | 328/24289 [01:34<1:54:31,  3.49it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   1%|▏         | 328/24289 [01:34<1:54:31,  3.49it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 329/24289 [01:34<1:54:30,  3.49it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 329/24289 [01:34<1:54:30,  3.49it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   1%|▏         | 330/24289 [01:34<1:54:27,  3.49it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   1%|▏         | 330/24289 [01:34<1:54:27,  3.49it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]
Epoch 0:   1%|▏         | 331/24289 [01:34<1:54:27,  3.49it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]
Epoch 0:   1%|▏         | 331/24289 [01:34<1:54:27,  3.49it/s, v_num=zwmv, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]
Epoch 0:   1%|▏         | 332/24289 [01:35<1:54:29,  3.49it/s, v_num=zwmv, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]
Epoch 0:   1%|▏         | 332/24289 [01:35<1:54:29,  3.49it/s, v_num=zwmv, train_loss_step=0.0556, train_metrics/recon_loss_step=0.0556]
Epoch 0:   1%|▏         | 333/24289 [01:35<1:54:25,  3.49it/s, v_num=zwmv, train_loss_step=0.0556, train_metrics/recon_loss_step=0.0556]
Epoch 0:   1%|▏         | 333/24289 [01:35<1:54:25,  3.49it/s, v_num=zwmv, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]  
Epoch 0:   1%|▏         | 334/24289 [01:35<1:54:21,  3.49it/s, v_num=zwmv, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]
Epoch 0:   1%|▏         | 334/24289 [01:35<1:54:21,  3.49it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|▏         | 335/24289 [01:35<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|▏         | 335/24289 [01:35<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.223, train_metrics/recon_loss_step=0.223]
Epoch 0:   1%|▏         | 336/24289 [01:36<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.223, train_metrics/recon_loss_step=0.223]
Epoch 0:   1%|▏         | 336/24289 [01:36<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.00478, train_metrics/recon_loss_step=0.00478]
Epoch 0:   1%|▏         | 337/24289 [01:36<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.00478, train_metrics/recon_loss_step=0.00478]
Epoch 0:   1%|▏         | 337/24289 [01:36<1:54:19,  3.49it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]    
Epoch 0:   1%|▏         | 338/24289 [01:36<1:54:20,  3.49it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|▏         | 338/24289 [01:36<1:54:20,  3.49it/s, v_num=zwmv, train_loss_step=0.0974, train_metrics/recon_loss_step=0.0974]
Epoch 0:   1%|▏         | 339/24289 [01:37<1:54:18,  3.49it/s, v_num=zwmv, train_loss_step=0.0974, train_metrics/recon_loss_step=0.0974]
Epoch 0:   1%|▏         | 339/24289 [01:37<1:54:18,  3.49it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]  
Epoch 0:   1%|▏         | 340/24289 [01:37<1:54:25,  3.49it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|▏         | 340/24289 [01:37<1:54:25,  3.49it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|▏         | 341/24289 [01:37<1:54:22,  3.49it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|▏         | 341/24289 [01:37<1:54:22,  3.49it/s, v_num=zwmv, train_loss_step=0.0659, train_metrics/recon_loss_step=0.0659]
Epoch 0:   1%|▏         | 342/24289 [01:38<1:54:22,  3.49it/s, v_num=zwmv, train_loss_step=0.0659, train_metrics/recon_loss_step=0.0659]
Epoch 0:   1%|▏         | 342/24289 [01:38<1:54:22,  3.49it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]  
Epoch 0:   1%|▏         | 343/24289 [01:38<1:54:17,  3.49it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   1%|▏         | 343/24289 [01:38<1:54:17,  3.49it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|▏         | 344/24289 [01:38<1:54:16,  3.49it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   1%|▏         | 344/24289 [01:38<1:54:16,  3.49it/s, v_num=zwmv, train_loss_step=0.311, train_metrics/recon_loss_step=0.311]
Epoch 0:   1%|▏         | 345/24289 [01:38<1:54:13,  3.49it/s, v_num=zwmv, train_loss_step=0.311, train_metrics/recon_loss_step=0.311]
Epoch 0:   1%|▏         | 345/24289 [01:38<1:54:13,  3.49it/s, v_num=zwmv, train_loss_step=0.0638, train_metrics/recon_loss_step=0.0638]
Epoch 0:   1%|▏         | 346/24289 [01:39<1:54:13,  3.49it/s, v_num=zwmv, train_loss_step=0.0638, train_metrics/recon_loss_step=0.0638]
Epoch 0:   1%|▏         | 346/24289 [01:39<1:54:14,  3.49it/s, v_num=zwmv, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]  
Epoch 0:   1%|▏         | 347/24289 [01:39<1:54:12,  3.49it/s, v_num=zwmv, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]
Epoch 0:   1%|▏         | 347/24289 [01:39<1:54:12,  3.49it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 348/24289 [01:39<1:54:17,  3.49it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 348/24289 [01:39<1:54:17,  3.49it/s, v_num=zwmv, train_loss_step=0.0757, train_metrics/recon_loss_step=0.0757]
Epoch 0:   1%|▏         | 349/24289 [01:39<1:54:16,  3.49it/s, v_num=zwmv, train_loss_step=0.0757, train_metrics/recon_loss_step=0.0757]
Epoch 0:   1%|▏         | 349/24289 [01:39<1:54:16,  3.49it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]  
Epoch 0:   1%|▏         | 350/24289 [01:40<1:54:12,  3.49it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   1%|▏         | 350/24289 [01:40<1:54:12,  3.49it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 351/24289 [01:40<1:54:10,  3.49it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 351/24289 [01:40<1:54:10,  3.49it/s, v_num=zwmv, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]
Epoch 0:   1%|▏         | 352/24289 [01:40<1:54:08,  3.50it/s, v_num=zwmv, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]
Epoch 0:   1%|▏         | 352/24289 [01:40<1:54:08,  3.50it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   1%|▏         | 353/24289 [01:40<1:54:04,  3.50it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   1%|▏         | 353/24289 [01:40<1:54:04,  3.50it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 354/24289 [01:41<1:54:07,  3.50it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|▏         | 354/24289 [01:41<1:54:07,  3.50it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   1%|▏         | 355/24289 [01:41<1:54:04,  3.50it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   1%|▏         | 355/24289 [01:41<1:54:04,  3.50it/s, v_num=zwmv, train_loss_step=0.0598, train_metrics/recon_loss_step=0.0598]
Epoch 0:   1%|▏         | 356/24289 [01:41<1:54:01,  3.50it/s, v_num=zwmv, train_loss_step=0.0598, train_metrics/recon_loss_step=0.0598]
Epoch 0:   1%|▏         | 356/24289 [01:41<1:54:02,  3.50it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]  
Epoch 0:   1%|▏         | 357/24289 [01:42<1:53:58,  3.50it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   1%|▏         | 357/24289 [01:42<1:53:58,  3.50it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 358/24289 [01:42<1:53:55,  3.50it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|▏         | 358/24289 [01:42<1:53:55,  3.50it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   1%|▏         | 359/24289 [01:42<1:53:55,  3.50it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   1%|▏         | 359/24289 [01:42<1:53:55,  3.50it/s, v_num=zwmv, train_loss_step=0.232, train_metrics/recon_loss_step=0.232]
Epoch 0:   1%|▏         | 360/24289 [01:42<1:53:50,  3.50it/s, v_num=zwmv, train_loss_step=0.232, train_metrics/recon_loss_step=0.232]
Epoch 0:   1%|▏         | 360/24289 [01:42<1:53:51,  3.50it/s, v_num=zwmv, train_loss_step=0.0579, train_metrics/recon_loss_step=0.0579]
Epoch 0:   1%|▏         | 361/24289 [01:42<1:53:47,  3.50it/s, v_num=zwmv, train_loss_step=0.0579, train_metrics/recon_loss_step=0.0579]
Epoch 0:   1%|▏         | 361/24289 [01:43<1:53:47,  3.50it/s, v_num=zwmv, train_loss_step=0.0927, train_metrics/recon_loss_step=0.0927]
Epoch 0:   1%|▏         | 362/24289 [01:43<1:53:47,  3.50it/s, v_num=zwmv, train_loss_step=0.0927, train_metrics/recon_loss_step=0.0927]
Epoch 0:   1%|▏         | 362/24289 [01:43<1:53:47,  3.50it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]  
Epoch 0:   1%|▏         | 363/24289 [01:43<1:53:51,  3.50it/s, v_num=zwmv, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   1%|▏         | 363/24289 [01:43<1:53:51,  3.50it/s, v_num=zwmv, train_loss_step=0.0611, train_metrics/recon_loss_step=0.0611]
Epoch 0:   1%|▏         | 364/24289 [01:43<1:53:48,  3.50it/s, v_num=zwmv, train_loss_step=0.0611, train_metrics/recon_loss_step=0.0611]
Epoch 0:   1%|▏         | 364/24289 [01:43<1:53:48,  3.50it/s, v_num=zwmv, train_loss_step=0.049, train_metrics/recon_loss_step=0.049]  
Epoch 0:   2%|▏         | 365/24289 [01:44<1:53:43,  3.51it/s, v_num=zwmv, train_loss_step=0.049, train_metrics/recon_loss_step=0.049]
Epoch 0:   2%|▏         | 365/24289 [01:44<1:53:44,  3.51it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   2%|▏         | 366/24289 [01:44<1:53:39,  3.51it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   2%|▏         | 366/24289 [01:44<1:53:39,  3.51it/s, v_num=zwmv, train_loss_step=0.0847, train_metrics/recon_loss_step=0.0847]
Epoch 0:   2%|▏         | 367/24289 [01:44<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.0847, train_metrics/recon_loss_step=0.0847]
Epoch 0:   2%|▏         | 367/24289 [01:44<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.0773, train_metrics/recon_loss_step=0.0773]
Epoch 0:   2%|▏         | 368/24289 [01:44<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.0773, train_metrics/recon_loss_step=0.0773]
Epoch 0:   2%|▏         | 368/24289 [01:44<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.0626, train_metrics/recon_loss_step=0.0626]
Epoch 0:   2%|▏         | 369/24289 [01:45<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.0626, train_metrics/recon_loss_step=0.0626]
Epoch 0:   2%|▏         | 369/24289 [01:45<1:53:36,  3.51it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]  
Epoch 0:   2%|▏         | 370/24289 [01:45<1:53:42,  3.51it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   2%|▏         | 370/24289 [01:45<1:53:42,  3.51it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   2%|▏         | 371/24289 [01:45<1:53:43,  3.51it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   2%|▏         | 371/24289 [01:45<1:53:43,  3.51it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   2%|▏         | 372/24289 [01:46<1:53:39,  3.51it/s, v_num=zwmv, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   2%|▏         | 372/24289 [01:46<1:53:39,  3.51it/s, v_num=zwmv, train_loss_step=0.0527, train_metrics/recon_loss_step=0.0527]
Epoch 0:   2%|▏         | 373/24289 [01:46<1:53:35,  3.51it/s, v_num=zwmv, train_loss_step=0.0527, train_metrics/recon_loss_step=0.0527]
Epoch 0:   2%|▏         | 373/24289 [01:46<1:53:35,  3.51it/s, v_num=zwmv, train_loss_step=0.0925, train_metrics/recon_loss_step=0.0925]
Epoch 0:   2%|▏         | 374/24289 [01:46<1:53:34,  3.51it/s, v_num=zwmv, train_loss_step=0.0925, train_metrics/recon_loss_step=0.0925]
Epoch 0:   2%|▏         | 374/24289 [01:46<1:53:34,  3.51it/s, v_num=zwmv, train_loss_step=0.0545, train_metrics/recon_loss_step=0.0545]
Epoch 0:   2%|▏         | 375/24289 [01:46<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.0545, train_metrics/recon_loss_step=0.0545]
Epoch 0:   2%|▏         | 375/24289 [01:46<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.0964, train_metrics/recon_loss_step=0.0964]
Epoch 0:   2%|▏         | 376/24289 [01:47<1:53:28,  3.51it/s, v_num=zwmv, train_loss_step=0.0964, train_metrics/recon_loss_step=0.0964]
Epoch 0:   2%|▏         | 376/24289 [01:47<1:53:28,  3.51it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]  
Epoch 0:   2%|▏         | 377/24289 [01:47<1:53:25,  3.51it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   2%|▏         | 377/24289 [01:47<1:53:25,  3.51it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   2%|▏         | 378/24289 [01:47<1:53:31,  3.51it/s, v_num=zwmv, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   2%|▏         | 378/24289 [01:47<1:53:31,  3.51it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   2%|▏         | 379/24289 [01:48<1:53:37,  3.51it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   2%|▏         | 379/24289 [01:48<1:53:37,  3.51it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   2%|▏         | 380/24289 [01:48<1:53:33,  3.51it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   2%|▏         | 380/24289 [01:48<1:53:33,  3.51it/s, v_num=zwmv, train_loss_step=0.0846, train_metrics/recon_loss_step=0.0846]
Epoch 0:   2%|▏         | 381/24289 [01:48<1:53:29,  3.51it/s, v_num=zwmv, train_loss_step=0.0846, train_metrics/recon_loss_step=0.0846]
Epoch 0:   2%|▏         | 381/24289 [01:48<1:53:29,  3.51it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]  
Epoch 0:   2%|▏         | 382/24289 [01:48<1:53:27,  3.51it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]
Epoch 0:   2%|▏         | 382/24289 [01:48<1:53:27,  3.51it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   2%|▏         | 383/24289 [01:49<1:53:32,  3.51it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   2%|▏         | 383/24289 [01:49<1:53:32,  3.51it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 384/24289 [01:49<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 384/24289 [01:49<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   2%|▏         | 385/24289 [01:49<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   2%|▏         | 385/24289 [01:49<1:53:30,  3.51it/s, v_num=zwmv, train_loss_step=0.0278, train_metrics/recon_loss_step=0.0278]
Epoch 0:   2%|▏         | 386/24289 [01:49<1:53:29,  3.51it/s, v_num=zwmv, train_loss_step=0.0278, train_metrics/recon_loss_step=0.0278]
Epoch 0:   2%|▏         | 386/24289 [01:49<1:53:29,  3.51it/s, v_num=zwmv, train_loss_step=0.019, train_metrics/recon_loss_step=0.019]  
Epoch 0:   2%|▏         | 387/24289 [01:50<1:53:25,  3.51it/s, v_num=zwmv, train_loss_step=0.019, train_metrics/recon_loss_step=0.019]
Epoch 0:   2%|▏         | 387/24289 [01:50<1:53:25,  3.51it/s, v_num=zwmv, train_loss_step=0.0853, train_metrics/recon_loss_step=0.0853]
Epoch 0:   2%|▏         | 388/24289 [01:50<1:53:27,  3.51it/s, v_num=zwmv, train_loss_step=0.0853, train_metrics/recon_loss_step=0.0853]
Epoch 0:   2%|▏         | 388/24289 [01:50<1:53:27,  3.51it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]  
Epoch 0:   2%|▏         | 389/24289 [01:50<1:53:23,  3.51it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   2%|▏         | 389/24289 [01:50<1:53:23,  3.51it/s, v_num=zwmv, train_loss_step=0.071, train_metrics/recon_loss_step=0.071]
Epoch 0:   2%|▏         | 390/24289 [01:51<1:53:22,  3.51it/s, v_num=zwmv, train_loss_step=0.071, train_metrics/recon_loss_step=0.071]
Epoch 0:   2%|▏         | 390/24289 [01:51<1:53:22,  3.51it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   2%|▏         | 391/24289 [01:51<1:53:21,  3.51it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   2%|▏         | 391/24289 [01:51<1:53:21,  3.51it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   2%|▏         | 392/24289 [01:51<1:53:17,  3.52it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   2%|▏         | 392/24289 [01:51<1:53:17,  3.52it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   2%|▏         | 393/24289 [01:51<1:53:14,  3.52it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   2%|▏         | 393/24289 [01:51<1:53:14,  3.52it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   2%|▏         | 394/24289 [01:52<1:53:13,  3.52it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   2%|▏         | 394/24289 [01:52<1:53:13,  3.52it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   2%|▏         | 395/24289 [01:52<1:53:09,  3.52it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   2%|▏         | 395/24289 [01:52<1:53:09,  3.52it/s, v_num=zwmv, train_loss_step=0.0708, train_metrics/recon_loss_step=0.0708]
Epoch 0:   2%|▏         | 396/24289 [01:52<1:53:12,  3.52it/s, v_num=zwmv, train_loss_step=0.0708, train_metrics/recon_loss_step=0.0708]
Epoch 0:   2%|▏         | 396/24289 [01:52<1:53:12,  3.52it/s, v_num=zwmv, train_loss_step=0.0427, train_metrics/recon_loss_step=0.0427]
Epoch 0:   2%|▏         | 397/24289 [01:52<1:53:12,  3.52it/s, v_num=zwmv, train_loss_step=0.0427, train_metrics/recon_loss_step=0.0427]
Epoch 0:   2%|▏         | 397/24289 [01:52<1:53:13,  3.52it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]  
Epoch 0:   2%|▏         | 398/24289 [01:53<1:53:18,  3.51it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   2%|▏         | 398/24289 [01:53<1:53:18,  3.51it/s, v_num=zwmv, train_loss_step=0.0973, train_metrics/recon_loss_step=0.0973]
Epoch 0:   2%|▏         | 399/24289 [01:53<1:53:15,  3.52it/s, v_num=zwmv, train_loss_step=0.0973, train_metrics/recon_loss_step=0.0973]
Epoch 0:   2%|▏         | 399/24289 [01:53<1:53:15,  3.52it/s, v_num=zwmv, train_loss_step=0.189, train_metrics/recon_loss_step=0.189]  
Epoch 0:   2%|▏         | 400/24289 [01:53<1:53:11,  3.52it/s, v_num=zwmv, train_loss_step=0.189, train_metrics/recon_loss_step=0.189]
Epoch 0:   2%|▏         | 400/24289 [01:53<1:53:11,  3.52it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 401/24289 [01:54<1:53:11,  3.52it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 401/24289 [01:54<1:53:11,  3.52it/s, v_num=zwmv, train_loss_step=0.0644, train_metrics/recon_loss_step=0.0644]
Epoch 0:   2%|▏         | 402/24289 [01:54<1:53:16,  3.51it/s, v_num=zwmv, train_loss_step=0.0644, train_metrics/recon_loss_step=0.0644]
Epoch 0:   2%|▏         | 402/24289 [01:54<1:53:16,  3.51it/s, v_num=zwmv, train_loss_step=0.0323, train_metrics/recon_loss_step=0.0323]
Epoch 0:   2%|▏         | 403/24289 [01:54<1:53:13,  3.52it/s, v_num=zwmv, train_loss_step=0.0323, train_metrics/recon_loss_step=0.0323]
Epoch 0:   2%|▏         | 403/24289 [01:54<1:53:14,  3.52it/s, v_num=zwmv, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]  
Epoch 0:   2%|▏         | 404/24289 [01:54<1:53:14,  3.52it/s, v_num=zwmv, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]
Epoch 0:   2%|▏         | 404/24289 [01:54<1:53:14,  3.52it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   2%|▏         | 405/24289 [01:55<1:53:09,  3.52it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   2%|▏         | 405/24289 [01:55<1:53:10,  3.52it/s, v_num=zwmv, train_loss_step=0.0697, train_metrics/recon_loss_step=0.0697]
Epoch 0:   2%|▏         | 406/24289 [01:55<1:53:07,  3.52it/s, v_num=zwmv, train_loss_step=0.0697, train_metrics/recon_loss_step=0.0697]
Epoch 0:   2%|▏         | 406/24289 [01:55<1:53:07,  3.52it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]  
Epoch 0:   2%|▏         | 407/24289 [01:55<1:53:04,  3.52it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   2%|▏         | 407/24289 [01:55<1:53:04,  3.52it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   2%|▏         | 408/24289 [01:55<1:53:01,  3.52it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   2%|▏         | 408/24289 [01:55<1:53:01,  3.52it/s, v_num=zwmv, train_loss_step=0.074, train_metrics/recon_loss_step=0.074]
Epoch 0:   2%|▏         | 409/24289 [01:56<1:53:03,  3.52it/s, v_num=zwmv, train_loss_step=0.074, train_metrics/recon_loss_step=0.074]
Epoch 0:   2%|▏         | 409/24289 [01:56<1:53:03,  3.52it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   2%|▏         | 410/24289 [01:56<1:53:00,  3.52it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   2%|▏         | 410/24289 [01:56<1:53:00,  3.52it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   2%|▏         | 411/24289 [01:56<1:52:57,  3.52it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   2%|▏         | 411/24289 [01:56<1:52:57,  3.52it/s, v_num=zwmv, train_loss_step=0.167, train_metrics/recon_loss_step=0.167]
Epoch 0:   2%|▏         | 412/24289 [01:56<1:52:55,  3.52it/s, v_num=zwmv, train_loss_step=0.167, train_metrics/recon_loss_step=0.167]
Epoch 0:   2%|▏         | 412/24289 [01:56<1:52:55,  3.52it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   2%|▏         | 413/24289 [01:57<1:52:55,  3.52it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   2%|▏         | 413/24289 [01:57<1:52:55,  3.52it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   2%|▏         | 414/24289 [01:57<1:52:52,  3.53it/s, v_num=zwmv, train_loss_step=0.174, train_metrics/recon_loss_step=0.174]
Epoch 0:   2%|▏         | 414/24289 [01:57<1:52:52,  3.53it/s, v_num=zwmv, train_loss_step=0.0829, train_metrics/recon_loss_step=0.0829]
Epoch 0:   2%|▏         | 415/24289 [01:57<1:52:52,  3.53it/s, v_num=zwmv, train_loss_step=0.0829, train_metrics/recon_loss_step=0.0829]
Epoch 0:   2%|▏         | 415/24289 [01:57<1:52:52,  3.53it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]  
Epoch 0:   2%|▏         | 416/24289 [01:57<1:52:48,  3.53it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   2%|▏         | 416/24289 [01:57<1:52:49,  3.53it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 417/24289 [01:58<1:52:48,  3.53it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 417/24289 [01:58<1:52:48,  3.53it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   2%|▏         | 418/24289 [01:58<1:52:45,  3.53it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   2%|▏         | 418/24289 [01:58<1:52:45,  3.53it/s, v_num=zwmv, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   2%|▏         | 419/24289 [01:58<1:52:42,  3.53it/s, v_num=zwmv, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   2%|▏         | 419/24289 [01:58<1:52:42,  3.53it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 420/24289 [01:58<1:52:41,  3.53it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   2%|▏         | 420/24289 [01:58<1:52:41,  3.53it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   2%|▏         | 421/24289 [01:59<1:52:37,  3.53it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   2%|▏         | 421/24289 [01:59<1:52:37,  3.53it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   2%|▏         | 422/24289 [01:59<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   2%|▏         | 422/24289 [01:59<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.0796, train_metrics/recon_loss_step=0.0796]
Epoch 0:   2%|▏         | 423/24289 [01:59<1:52:35,  3.53it/s, v_num=zwmv, train_loss_step=0.0796, train_metrics/recon_loss_step=0.0796]
Epoch 0:   2%|▏         | 423/24289 [01:59<1:52:35,  3.53it/s, v_num=zwmv, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]  
Epoch 0:   2%|▏         | 424/24289 [02:00<1:52:40,  3.53it/s, v_num=zwmv, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]
Epoch 0:   2%|▏         | 424/24289 [02:00<1:52:40,  3.53it/s, v_num=zwmv, train_loss_step=0.0364, train_metrics/recon_loss_step=0.0364]
Epoch 0:   2%|▏         | 425/24289 [02:00<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.0364, train_metrics/recon_loss_step=0.0364]
Epoch 0:   2%|▏         | 425/24289 [02:00<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.0943, train_metrics/recon_loss_step=0.0943]
Epoch 0:   2%|▏         | 426/24289 [02:00<1:52:34,  3.53it/s, v_num=zwmv, train_loss_step=0.0943, train_metrics/recon_loss_step=0.0943]
Epoch 0:   2%|▏         | 426/24289 [02:00<1:52:35,  3.53it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]  
Epoch 0:   2%|▏         | 427/24289 [02:00<1:52:30,  3.53it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   2%|▏         | 427/24289 [02:00<1:52:31,  3.53it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   2%|▏         | 428/24289 [02:01<1:52:28,  3.54it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   2%|▏         | 428/24289 [02:01<1:52:28,  3.54it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   2%|▏         | 429/24289 [02:01<1:52:31,  3.53it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   2%|▏         | 429/24289 [02:01<1:52:31,  3.53it/s, v_num=zwmv, train_loss_step=0.0902, train_metrics/recon_loss_step=0.0902]
Epoch 0:   2%|▏         | 430/24289 [02:01<1:52:35,  3.53it/s, v_num=zwmv, train_loss_step=0.0902, train_metrics/recon_loss_step=0.0902]
Epoch 0:   2%|▏         | 430/24289 [02:01<1:52:35,  3.53it/s, v_num=zwmv, train_loss_step=0.0247, train_metrics/recon_loss_step=0.0247]
Epoch 0:   2%|▏         | 431/24289 [02:02<1:52:40,  3.53it/s, v_num=zwmv, train_loss_step=0.0247, train_metrics/recon_loss_step=0.0247]
Epoch 0:   2%|▏         | 431/24289 [02:02<1:52:40,  3.53it/s, v_num=zwmv, train_loss_step=0.0247, train_metrics/recon_loss_step=0.0247]
Epoch 0:   2%|▏         | 432/24289 [02:02<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.0247, train_metrics/recon_loss_step=0.0247]
Epoch 0:   2%|▏         | 432/24289 [02:02<1:52:36,  3.53it/s, v_num=zwmv, train_loss_step=0.0655, train_metrics/recon_loss_step=0.0655]
Epoch 0:   2%|▏         | 433/24289 [02:02<1:52:34,  3.53it/s, v_num=zwmv, train_loss_step=0.0655, train_metrics/recon_loss_step=0.0655]
Epoch 0:   2%|▏         | 433/24289 [02:02<1:52:34,  3.53it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]  
Epoch 0:   2%|▏         | 434/24289 [02:02<1:52:33,  3.53it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   2%|▏         | 434/24289 [02:02<1:52:33,  3.53it/s, v_num=zwmv, train_loss_step=0.308, train_metrics/recon_loss_step=0.308]
Epoch 0:   2%|▏         | 435/24289 [02:03<1:52:29,  3.53it/s, v_num=zwmv, train_loss_step=0.308, train_metrics/recon_loss_step=0.308]
Epoch 0:   2%|▏         | 435/24289 [02:03<1:52:29,  3.53it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   2%|▏         | 436/24289 [02:03<1:52:26,  3.54it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   2%|▏         | 436/24289 [02:03<1:52:26,  3.54it/s, v_num=zwmv, train_loss_step=0.0838, train_metrics/recon_loss_step=0.0838]
Epoch 0:   2%|▏         | 437/24289 [02:03<1:52:25,  3.54it/s, v_num=zwmv, train_loss_step=0.0838, train_metrics/recon_loss_step=0.0838]
Epoch 0:   2%|▏         | 437/24289 [02:03<1:52:25,  3.54it/s, v_num=zwmv, train_loss_step=0.369, train_metrics/recon_loss_step=0.369]  
Epoch 0:   2%|▏         | 438/24289 [02:03<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.369, train_metrics/recon_loss_step=0.369]
Epoch 0:   2%|▏         | 438/24289 [02:03<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.0996, train_metrics/recon_loss_step=0.0996]
Epoch 0:   2%|▏         | 439/24289 [02:04<1:52:19,  3.54it/s, v_num=zwmv, train_loss_step=0.0996, train_metrics/recon_loss_step=0.0996]
Epoch 0:   2%|▏         | 439/24289 [02:04<1:52:19,  3.54it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]  
Epoch 0:   2%|▏         | 440/24289 [02:04<1:52:16,  3.54it/s, v_num=zwmv, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]
Epoch 0:   2%|▏         | 440/24289 [02:04<1:52:16,  3.54it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 441/24289 [02:04<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 441/24289 [02:04<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   2%|▏         | 442/24289 [02:05<1:52:26,  3.53it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   2%|▏         | 442/24289 [02:05<1:52:26,  3.53it/s, v_num=zwmv, train_loss_step=0.079, train_metrics/recon_loss_step=0.079]
Epoch 0:   2%|▏         | 443/24289 [02:05<1:52:23,  3.54it/s, v_num=zwmv, train_loss_step=0.079, train_metrics/recon_loss_step=0.079]
Epoch 0:   2%|▏         | 443/24289 [02:05<1:52:23,  3.54it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   2%|▏         | 444/24289 [02:05<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   2%|▏         | 444/24289 [02:05<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   2%|▏         | 445/24289 [02:05<1:52:20,  3.54it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   2%|▏         | 445/24289 [02:05<1:52:20,  3.54it/s, v_num=zwmv, train_loss_step=0.0875, train_metrics/recon_loss_step=0.0875]
Epoch 0:   2%|▏         | 446/24289 [02:06<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.0875, train_metrics/recon_loss_step=0.0875]
Epoch 0:   2%|▏         | 446/24289 [02:06<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.0826, train_metrics/recon_loss_step=0.0826]
Epoch 0:   2%|▏         | 447/24289 [02:06<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.0826, train_metrics/recon_loss_step=0.0826]
Epoch 0:   2%|▏         | 447/24289 [02:06<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.0495, train_metrics/recon_loss_step=0.0495]
Epoch 0:   2%|▏         | 448/24289 [02:06<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.0495, train_metrics/recon_loss_step=0.0495]
Epoch 0:   2%|▏         | 448/24289 [02:06<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]  
Epoch 0:   2%|▏         | 449/24289 [02:06<1:52:20,  3.54it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   2%|▏         | 449/24289 [02:06<1:52:21,  3.54it/s, v_num=zwmv, train_loss_step=0.0858, train_metrics/recon_loss_step=0.0858]
Epoch 0:   2%|▏         | 450/24289 [02:07<1:52:17,  3.54it/s, v_num=zwmv, train_loss_step=0.0858, train_metrics/recon_loss_step=0.0858]
Epoch 0:   2%|▏         | 450/24289 [02:07<1:52:17,  3.54it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]  
Epoch 0:   2%|▏         | 451/24289 [02:07<1:52:16,  3.54it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   2%|▏         | 451/24289 [02:07<1:52:16,  3.54it/s, v_num=zwmv, train_loss_step=0.0962, train_metrics/recon_loss_step=0.0962]
Epoch 0:   2%|▏         | 452/24289 [02:07<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.0962, train_metrics/recon_loss_step=0.0962]
Epoch 0:   2%|▏         | 452/24289 [02:07<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.0418, train_metrics/recon_loss_step=0.0418]
Epoch 0:   2%|▏         | 453/24289 [02:08<1:52:23,  3.53it/s, v_num=zwmv, train_loss_step=0.0418, train_metrics/recon_loss_step=0.0418]
Epoch 0:   2%|▏         | 453/24289 [02:08<1:52:23,  3.53it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]  
Epoch 0:   2%|▏         | 454/24289 [02:08<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   2%|▏         | 454/24289 [02:08<1:52:22,  3.54it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   2%|▏         | 455/24289 [02:08<1:52:20,  3.54it/s, v_num=zwmv, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   2%|▏         | 455/24289 [02:08<1:52:20,  3.54it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   2%|▏         | 456/24289 [02:08<1:52:19,  3.54it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   2%|▏         | 456/24289 [02:08<1:52:19,  3.54it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   2%|▏         | 457/24289 [02:09<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   2%|▏         | 457/24289 [02:09<1:52:18,  3.54it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   2%|▏         | 458/24289 [02:09<1:52:15,  3.54it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   2%|▏         | 458/24289 [02:09<1:52:15,  3.54it/s, v_num=zwmv, train_loss_step=0.0929, train_metrics/recon_loss_step=0.0929]
Epoch 0:   2%|▏         | 459/24289 [02:09<1:52:14,  3.54it/s, v_num=zwmv, train_loss_step=0.0929, train_metrics/recon_loss_step=0.0929]
Epoch 0:   2%|▏         | 459/24289 [02:09<1:52:14,  3.54it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]  
Epoch 0:   2%|▏         | 460/24289 [02:09<1:52:13,  3.54it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   2%|▏         | 460/24289 [02:09<1:52:13,  3.54it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 461/24289 [02:10<1:52:10,  3.54it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 461/24289 [02:10<1:52:10,  3.54it/s, v_num=zwmv, train_loss_step=0.143, train_metrics/recon_loss_step=0.143]
Epoch 0:   2%|▏         | 462/24289 [02:10<1:52:07,  3.54it/s, v_num=zwmv, train_loss_step=0.143, train_metrics/recon_loss_step=0.143]
Epoch 0:   2%|▏         | 462/24289 [02:10<1:52:07,  3.54it/s, v_num=zwmv, train_loss_step=0.080, train_metrics/recon_loss_step=0.080]
Epoch 0:   2%|▏         | 463/24289 [02:10<1:52:04,  3.54it/s, v_num=zwmv, train_loss_step=0.080, train_metrics/recon_loss_step=0.080]
Epoch 0:   2%|▏         | 463/24289 [02:10<1:52:04,  3.54it/s, v_num=zwmv, train_loss_step=0.0503, train_metrics/recon_loss_step=0.0503]
Epoch 0:   2%|▏         | 464/24289 [02:10<1:52:05,  3.54it/s, v_num=zwmv, train_loss_step=0.0503, train_metrics/recon_loss_step=0.0503]
Epoch 0:   2%|▏         | 464/24289 [02:10<1:52:05,  3.54it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]  
Epoch 0:   2%|▏         | 465/24289 [02:11<1:52:03,  3.54it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   2%|▏         | 465/24289 [02:11<1:52:03,  3.54it/s, v_num=zwmv, train_loss_step=0.0591, train_metrics/recon_loss_step=0.0591]
Epoch 0:   2%|▏         | 466/24289 [02:11<1:52:00,  3.55it/s, v_num=zwmv, train_loss_step=0.0591, train_metrics/recon_loss_step=0.0591]
Epoch 0:   2%|▏         | 466/24289 [02:11<1:52:00,  3.54it/s, v_num=zwmv, train_loss_step=0.0526, train_metrics/recon_loss_step=0.0526]
Epoch 0:   2%|▏         | 467/24289 [02:11<1:51:56,  3.55it/s, v_num=zwmv, train_loss_step=0.0526, train_metrics/recon_loss_step=0.0526]
Epoch 0:   2%|▏         | 467/24289 [02:11<1:51:56,  3.55it/s, v_num=zwmv, train_loss_step=0.0735, train_metrics/recon_loss_step=0.0735]
Epoch 0:   2%|▏         | 468/24289 [02:11<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.0735, train_metrics/recon_loss_step=0.0735]
Epoch 0:   2%|▏         | 468/24289 [02:11<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.0457, train_metrics/recon_loss_step=0.0457]
Epoch 0:   2%|▏         | 469/24289 [02:12<1:51:58,  3.55it/s, v_num=zwmv, train_loss_step=0.0457, train_metrics/recon_loss_step=0.0457]
Epoch 0:   2%|▏         | 469/24289 [02:12<1:51:58,  3.55it/s, v_num=zwmv, train_loss_step=0.0405, train_metrics/recon_loss_step=0.0405]
Epoch 0:   2%|▏         | 470/24289 [02:12<1:51:57,  3.55it/s, v_num=zwmv, train_loss_step=0.0405, train_metrics/recon_loss_step=0.0405]
Epoch 0:   2%|▏         | 470/24289 [02:12<1:51:57,  3.55it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]  
Epoch 0:   2%|▏         | 471/24289 [02:12<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   2%|▏         | 471/24289 [02:12<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.0326, train_metrics/recon_loss_step=0.0326]
Epoch 0:   2%|▏         | 472/24289 [02:13<1:51:52,  3.55it/s, v_num=zwmv, train_loss_step=0.0326, train_metrics/recon_loss_step=0.0326]
Epoch 0:   2%|▏         | 472/24289 [02:13<1:51:53,  3.55it/s, v_num=zwmv, train_loss_step=0.0843, train_metrics/recon_loss_step=0.0843]
Epoch 0:   2%|▏         | 473/24289 [02:13<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.0843, train_metrics/recon_loss_step=0.0843]
Epoch 0:   2%|▏         | 473/24289 [02:13<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.0424, train_metrics/recon_loss_step=0.0424]
Epoch 0:   2%|▏         | 474/24289 [02:13<1:51:48,  3.55it/s, v_num=zwmv, train_loss_step=0.0424, train_metrics/recon_loss_step=0.0424]
Epoch 0:   2%|▏         | 474/24289 [02:13<1:51:48,  3.55it/s, v_num=zwmv, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]  
Epoch 0:   2%|▏         | 475/24289 [02:13<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]
Epoch 0:   2%|▏         | 475/24289 [02:13<1:51:55,  3.55it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   2%|▏         | 476/24289 [02:14<1:51:53,  3.55it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   2%|▏         | 476/24289 [02:14<1:51:53,  3.55it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 477/24289 [02:14<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 477/24289 [02:14<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 478/24289 [02:14<1:51:51,  3.55it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 478/24289 [02:14<1:51:51,  3.55it/s, v_num=zwmv, train_loss_step=0.223, train_metrics/recon_loss_step=0.223]
Epoch 0:   2%|▏         | 479/24289 [02:14<1:51:49,  3.55it/s, v_num=zwmv, train_loss_step=0.223, train_metrics/recon_loss_step=0.223]
Epoch 0:   2%|▏         | 479/24289 [02:14<1:51:49,  3.55it/s, v_num=zwmv, train_loss_step=0.0793, train_metrics/recon_loss_step=0.0793]
Epoch 0:   2%|▏         | 480/24289 [02:15<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.0793, train_metrics/recon_loss_step=0.0793]
Epoch 0:   2%|▏         | 480/24289 [02:15<1:51:50,  3.55it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]  
Epoch 0:   2%|▏         | 481/24289 [02:15<1:51:48,  3.55it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   2%|▏         | 481/24289 [02:15<1:51:49,  3.55it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   2%|▏         | 482/24289 [02:15<1:51:47,  3.55it/s, v_num=zwmv, train_loss_step=0.127, train_metrics/recon_loss_step=0.127]
Epoch 0:   2%|▏         | 482/24289 [02:15<1:51:47,  3.55it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   2%|▏         | 483/24289 [02:16<1:51:44,  3.55it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   2%|▏         | 483/24289 [02:16<1:51:44,  3.55it/s, v_num=zwmv, train_loss_step=0.0707, train_metrics/recon_loss_step=0.0707]
Epoch 0:   2%|▏         | 484/24289 [02:16<1:51:43,  3.55it/s, v_num=zwmv, train_loss_step=0.0707, train_metrics/recon_loss_step=0.0707]
Epoch 0:   2%|▏         | 484/24289 [02:16<1:51:43,  3.55it/s, v_num=zwmv, train_loss_step=0.0663, train_metrics/recon_loss_step=0.0663]
Epoch 0:   2%|▏         | 485/24289 [02:16<1:51:44,  3.55it/s, v_num=zwmv, train_loss_step=0.0663, train_metrics/recon_loss_step=0.0663]
Epoch 0:   2%|▏         | 485/24289 [02:16<1:51:44,  3.55it/s, v_num=zwmv, train_loss_step=0.0756, train_metrics/recon_loss_step=0.0756]
Epoch 0:   2%|▏         | 486/24289 [02:16<1:51:42,  3.55it/s, v_num=zwmv, train_loss_step=0.0756, train_metrics/recon_loss_step=0.0756]
Epoch 0:   2%|▏         | 486/24289 [02:16<1:51:42,  3.55it/s, v_num=zwmv, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]  
Epoch 0:   2%|▏         | 487/24289 [02:17<1:51:46,  3.55it/s, v_num=zwmv, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]
Epoch 0:   2%|▏         | 487/24289 [02:17<1:51:46,  3.55it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   2%|▏         | 488/24289 [02:17<1:51:46,  3.55it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   2%|▏         | 488/24289 [02:17<1:51:46,  3.55it/s, v_num=zwmv, train_loss_step=0.0745, train_metrics/recon_loss_step=0.0745]
Epoch 0:   2%|▏         | 489/24289 [02:17<1:51:42,  3.55it/s, v_num=zwmv, train_loss_step=0.0745, train_metrics/recon_loss_step=0.0745]
Epoch 0:   2%|▏         | 489/24289 [02:17<1:51:42,  3.55it/s, v_num=zwmv, train_loss_step=0.0982, train_metrics/recon_loss_step=0.0982]
Epoch 0:   2%|▏         | 490/24289 [02:17<1:51:40,  3.55it/s, v_num=zwmv, train_loss_step=0.0982, train_metrics/recon_loss_step=0.0982]
Epoch 0:   2%|▏         | 490/24289 [02:17<1:51:40,  3.55it/s, v_num=zwmv, train_loss_step=0.0986, train_metrics/recon_loss_step=0.0986]
Epoch 0:   2%|▏         | 491/24289 [02:18<1:51:39,  3.55it/s, v_num=zwmv, train_loss_step=0.0986, train_metrics/recon_loss_step=0.0986]
Epoch 0:   2%|▏         | 491/24289 [02:18<1:51:39,  3.55it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 492/24289 [02:18<1:51:38,  3.55it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 492/24289 [02:18<1:51:38,  3.55it/s, v_num=zwmv, train_loss_step=0.233, train_metrics/recon_loss_step=0.233]  
Epoch 0:   2%|▏         | 493/24289 [02:18<1:51:40,  3.55it/s, v_num=zwmv, train_loss_step=0.233, train_metrics/recon_loss_step=0.233]
Epoch 0:   2%|▏         | 493/24289 [02:18<1:51:40,  3.55it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   2%|▏         | 494/24289 [02:19<1:51:37,  3.55it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   2%|▏         | 494/24289 [02:19<1:51:37,  3.55it/s, v_num=zwmv, train_loss_step=0.0694, train_metrics/recon_loss_step=0.0694]
Epoch 0:   2%|▏         | 495/24289 [02:19<1:51:35,  3.55it/s, v_num=zwmv, train_loss_step=0.0694, train_metrics/recon_loss_step=0.0694]
Epoch 0:   2%|▏         | 495/24289 [02:19<1:51:35,  3.55it/s, v_num=zwmv, train_loss_step=0.0949, train_metrics/recon_loss_step=0.0949]
Epoch 0:   2%|▏         | 496/24289 [02:19<1:51:36,  3.55it/s, v_num=zwmv, train_loss_step=0.0949, train_metrics/recon_loss_step=0.0949]
Epoch 0:   2%|▏         | 496/24289 [02:19<1:51:36,  3.55it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]  
Epoch 0:   2%|▏         | 497/24289 [02:19<1:51:34,  3.55it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   2%|▏         | 497/24289 [02:19<1:51:34,  3.55it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   2%|▏         | 498/24289 [02:20<1:51:31,  3.56it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   2%|▏         | 498/24289 [02:20<1:51:31,  3.56it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]
Epoch 0:   2%|▏         | 499/24289 [02:20<1:51:32,  3.55it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]
Epoch 0:   2%|▏         | 499/24289 [02:20<1:51:32,  3.55it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   2%|▏         | 500/24289 [02:20<1:51:32,  3.55it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   2%|▏         | 500/24289 [02:20<1:51:32,  3.55it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
🔍 LAYER ANALYSIS at step 500

Epoch 0:   2%|▏         | 501/24289 [02:20<1:51:29,  3.56it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   2%|▏         | 501/24289 [02:20<1:51:29,  3.56it/s, v_num=zwmv, train_loss_step=0.0523, train_metrics/recon_loss_step=0.0523]
Epoch 0:   2%|▏         | 502/24289 [02:21<1:51:29,  3.56it/s, v_num=zwmv, train_loss_step=0.0523, train_metrics/recon_loss_step=0.0523]
Epoch 0:   2%|▏         | 502/24289 [02:21<1:51:29,  3.56it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   2%|▏         | 503/24289 [02:21<1:51:27,  3.56it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   2%|▏         | 503/24289 [02:21<1:51:27,  3.56it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   2%|▏         | 504/24289 [02:21<1:51:25,  3.56it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   2%|▏         | 504/24289 [02:21<1:51:25,  3.56it/s, v_num=zwmv, train_loss_step=0.0548, train_metrics/recon_loss_step=0.0548]
Epoch 0:   2%|▏         | 505/24289 [02:21<1:51:26,  3.56it/s, v_num=zwmv, train_loss_step=0.0548, train_metrics/recon_loss_step=0.0548]
Epoch 0:   2%|▏         | 505/24289 [02:21<1:51:26,  3.56it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]  
Epoch 0:   2%|▏         | 506/24289 [02:22<1:51:23,  3.56it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   2%|▏         | 506/24289 [02:22<1:51:23,  3.56it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 507/24289 [02:22<1:51:22,  3.56it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 507/24289 [02:22<1:51:22,  3.56it/s, v_num=zwmv, train_loss_step=0.0988, train_metrics/recon_loss_step=0.0988]
Epoch 0:   2%|▏         | 508/24289 [02:22<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0988, train_metrics/recon_loss_step=0.0988]
Epoch 0:   2%|▏         | 508/24289 [02:22<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0785, train_metrics/recon_loss_step=0.0785]
Epoch 0:   2%|▏         | 509/24289 [02:22<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0785, train_metrics/recon_loss_step=0.0785]
Epoch 0:   2%|▏         | 509/24289 [02:22<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0516, train_metrics/recon_loss_step=0.0516]
Epoch 0:   2%|▏         | 510/24289 [02:23<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0516, train_metrics/recon_loss_step=0.0516]
Epoch 0:   2%|▏         | 510/24289 [02:23<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.0816, train_metrics/recon_loss_step=0.0816]
Epoch 0:   2%|▏         | 511/24289 [02:23<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.0816, train_metrics/recon_loss_step=0.0816]
Epoch 0:   2%|▏         | 511/24289 [02:23<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]  
Epoch 0:   2%|▏         | 512/24289 [02:23<1:51:22,  3.56it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   2%|▏         | 512/24289 [02:23<1:51:22,  3.56it/s, v_num=zwmv, train_loss_step=0.0305, train_metrics/recon_loss_step=0.0305]
Epoch 0:   2%|▏         | 513/24289 [02:24<1:51:21,  3.56it/s, v_num=zwmv, train_loss_step=0.0305, train_metrics/recon_loss_step=0.0305]
Epoch 0:   2%|▏         | 513/24289 [02:24<1:51:21,  3.56it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]  
Epoch 0:   2%|▏         | 514/24289 [02:24<1:51:21,  3.56it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   2%|▏         | 514/24289 [02:24<1:51:21,  3.56it/s, v_num=zwmv, train_loss_step=0.0334, train_metrics/recon_loss_step=0.0334]
Epoch 0:   2%|▏         | 515/24289 [02:24<1:51:18,  3.56it/s, v_num=zwmv, train_loss_step=0.0334, train_metrics/recon_loss_step=0.0334]
Epoch 0:   2%|▏         | 515/24289 [02:24<1:51:18,  3.56it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   2%|▏         | 516/24289 [02:24<1:51:16,  3.56it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   2%|▏         | 516/24289 [02:24<1:51:16,  3.56it/s, v_num=zwmv, train_loss_step=0.0981, train_metrics/recon_loss_step=0.0981]
Epoch 0:   2%|▏         | 517/24289 [02:25<1:51:14,  3.56it/s, v_num=zwmv, train_loss_step=0.0981, train_metrics/recon_loss_step=0.0981]
Epoch 0:   2%|▏         | 517/24289 [02:25<1:51:14,  3.56it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]  
Epoch 0:   2%|▏         | 518/24289 [02:25<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   2%|▏         | 518/24289 [02:25<1:51:19,  3.56it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   2%|▏         | 519/24289 [02:25<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   2%|▏         | 519/24289 [02:25<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   2%|▏         | 520/24289 [02:26<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   2%|▏         | 520/24289 [02:26<1:51:17,  3.56it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   2%|▏         | 521/24289 [02:26<1:51:14,  3.56it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   2%|▏         | 521/24289 [02:26<1:51:14,  3.56it/s, v_num=zwmv, train_loss_step=0.0589, train_metrics/recon_loss_step=0.0589]
Epoch 0:   2%|▏         | 522/24289 [02:26<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.0589, train_metrics/recon_loss_step=0.0589]
Epoch 0:   2%|▏         | 522/24289 [02:26<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.0686, train_metrics/recon_loss_step=0.0686]
Epoch 0:   2%|▏         | 523/24289 [02:26<1:51:13,  3.56it/s, v_num=zwmv, train_loss_step=0.0686, train_metrics/recon_loss_step=0.0686]
Epoch 0:   2%|▏         | 523/24289 [02:26<1:51:13,  3.56it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]  
Epoch 0:   2%|▏         | 524/24289 [02:27<1:51:13,  3.56it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   2%|▏         | 524/24289 [02:27<1:51:13,  3.56it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   2%|▏         | 525/24289 [02:27<1:51:15,  3.56it/s, v_num=zwmv, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   2%|▏         | 525/24289 [02:27<1:51:15,  3.56it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   2%|▏         | 526/24289 [02:27<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   2%|▏         | 526/24289 [02:27<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   2%|▏         | 527/24289 [02:27<1:51:09,  3.56it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   2%|▏         | 527/24289 [02:27<1:51:09,  3.56it/s, v_num=zwmv, train_loss_step=0.0914, train_metrics/recon_loss_step=0.0914]
Epoch 0:   2%|▏         | 528/24289 [02:28<1:51:08,  3.56it/s, v_num=zwmv, train_loss_step=0.0914, train_metrics/recon_loss_step=0.0914]
Epoch 0:   2%|▏         | 528/24289 [02:28<1:51:08,  3.56it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]  
Epoch 0:   2%|▏         | 529/24289 [02:28<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   2%|▏         | 529/24289 [02:28<1:51:12,  3.56it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   2%|▏         | 530/24289 [02:28<1:51:10,  3.56it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   2%|▏         | 530/24289 [02:28<1:51:10,  3.56it/s, v_num=zwmv, train_loss_step=0.0587, train_metrics/recon_loss_step=0.0587]
Epoch 0:   2%|▏         | 531/24289 [02:29<1:51:07,  3.56it/s, v_num=zwmv, train_loss_step=0.0587, train_metrics/recon_loss_step=0.0587]
Epoch 0:   2%|▏         | 531/24289 [02:29<1:51:07,  3.56it/s, v_num=zwmv, train_loss_step=0.071, train_metrics/recon_loss_step=0.071]  
Epoch 0:   2%|▏         | 532/24289 [02:29<1:51:05,  3.56it/s, v_num=zwmv, train_loss_step=0.071, train_metrics/recon_loss_step=0.071]
Epoch 0:   2%|▏         | 532/24289 [02:29<1:51:05,  3.56it/s, v_num=zwmv, train_loss_step=0.0725, train_metrics/recon_loss_step=0.0725]
Epoch 0:   2%|▏         | 533/24289 [02:29<1:51:04,  3.56it/s, v_num=zwmv, train_loss_step=0.0725, train_metrics/recon_loss_step=0.0725]
Epoch 0:   2%|▏         | 533/24289 [02:29<1:51:04,  3.56it/s, v_num=zwmv, train_loss_step=0.0915, train_metrics/recon_loss_step=0.0915]
Epoch 0:   2%|▏         | 534/24289 [02:29<1:51:03,  3.56it/s, v_num=zwmv, train_loss_step=0.0915, train_metrics/recon_loss_step=0.0915]
Epoch 0:   2%|▏         | 534/24289 [02:29<1:51:03,  3.56it/s, v_num=zwmv, train_loss_step=0.0805, train_metrics/recon_loss_step=0.0805]
Epoch 0:   2%|▏         | 535/24289 [02:30<1:51:02,  3.57it/s, v_num=zwmv, train_loss_step=0.0805, train_metrics/recon_loss_step=0.0805]
Epoch 0:   2%|▏         | 535/24289 [02:30<1:51:02,  3.57it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]  
Epoch 0:   2%|▏         | 536/24289 [02:30<1:51:00,  3.57it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   2%|▏         | 536/24289 [02:30<1:51:00,  3.57it/s, v_num=zwmv, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   2%|▏         | 537/24289 [02:30<1:51:00,  3.57it/s, v_num=zwmv, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   2%|▏         | 537/24289 [02:30<1:51:00,  3.57it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   2%|▏         | 538/24289 [02:30<1:50:58,  3.57it/s, v_num=zwmv, train_loss_step=0.160, train_metrics/recon_loss_step=0.160]
Epoch 0:   2%|▏         | 538/24289 [02:30<1:50:58,  3.57it/s, v_num=zwmv, train_loss_step=0.053, train_metrics/recon_loss_step=0.053]
Epoch 0:   2%|▏         | 539/24289 [02:31<1:50:57,  3.57it/s, v_num=zwmv, train_loss_step=0.053, train_metrics/recon_loss_step=0.053]
Epoch 0:   2%|▏         | 539/24289 [02:31<1:50:57,  3.57it/s, v_num=zwmv, train_loss_step=0.267, train_metrics/recon_loss_step=0.267]
Epoch 0:   2%|▏         | 540/24289 [02:31<1:50:55,  3.57it/s, v_num=zwmv, train_loss_step=0.267, train_metrics/recon_loss_step=0.267]
Epoch 0:   2%|▏         | 540/24289 [02:31<1:50:55,  3.57it/s, v_num=zwmv, train_loss_step=0.189, train_metrics/recon_loss_step=0.189]
Epoch 0:   2%|▏         | 541/24289 [02:31<1:50:56,  3.57it/s, v_num=zwmv, train_loss_step=0.189, train_metrics/recon_loss_step=0.189]
Epoch 0:   2%|▏         | 541/24289 [02:31<1:50:56,  3.57it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   2%|▏         | 542/24289 [02:31<1:50:54,  3.57it/s, v_num=zwmv, train_loss_step=0.165, train_metrics/recon_loss_step=0.165]
Epoch 0:   2%|▏         | 542/24289 [02:31<1:50:54,  3.57it/s, v_num=zwmv, train_loss_step=0.0971, train_metrics/recon_loss_step=0.0971]
Epoch 0:   2%|▏         | 543/24289 [02:32<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0971, train_metrics/recon_loss_step=0.0971]
Epoch 0:   2%|▏         | 543/24289 [02:32<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0983, train_metrics/recon_loss_step=0.0983]
Epoch 0:   2%|▏         | 544/24289 [02:32<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0983, train_metrics/recon_loss_step=0.0983]
Epoch 0:   2%|▏         | 544/24289 [02:32<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0101, train_metrics/recon_loss_step=0.0101]
Epoch 0:   2%|▏         | 545/24289 [02:32<1:50:50,  3.57it/s, v_num=zwmv, train_loss_step=0.0101, train_metrics/recon_loss_step=0.0101]
Epoch 0:   2%|▏         | 545/24289 [02:32<1:50:50,  3.57it/s, v_num=zwmv, train_loss_step=0.0942, train_metrics/recon_loss_step=0.0942]
Epoch 0:   2%|▏         | 546/24289 [02:33<1:50:54,  3.57it/s, v_num=zwmv, train_loss_step=0.0942, train_metrics/recon_loss_step=0.0942]
Epoch 0:   2%|▏         | 546/24289 [02:33<1:50:54,  3.57it/s, v_num=zwmv, train_loss_step=0.0423, train_metrics/recon_loss_step=0.0423]
Epoch 0:   2%|▏         | 547/24289 [02:33<1:50:55,  3.57it/s, v_num=zwmv, train_loss_step=0.0423, train_metrics/recon_loss_step=0.0423]
Epoch 0:   2%|▏         | 547/24289 [02:33<1:50:55,  3.57it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]  
Epoch 0:   2%|▏         | 548/24289 [02:33<1:50:53,  3.57it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   2%|▏         | 548/24289 [02:33<1:50:53,  3.57it/s, v_num=zwmv, train_loss_step=0.097, train_metrics/recon_loss_step=0.097]
Epoch 0:   2%|▏         | 549/24289 [02:33<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.097, train_metrics/recon_loss_step=0.097]
Epoch 0:   2%|▏         | 549/24289 [02:33<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0968, train_metrics/recon_loss_step=0.0968]
Epoch 0:   2%|▏         | 550/24289 [02:34<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.0968, train_metrics/recon_loss_step=0.0968]
Epoch 0:   2%|▏         | 550/24289 [02:34<1:50:52,  3.57it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]  
Epoch 0:   2%|▏         | 551/24289 [02:34<1:50:51,  3.57it/s, v_num=zwmv, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   2%|▏         | 551/24289 [02:34<1:50:51,  3.57it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   2%|▏         | 552/24289 [02:34<1:50:50,  3.57it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   2%|▏         | 552/24289 [02:34<1:50:51,  3.57it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   2%|▏         | 553/24289 [02:34<1:50:48,  3.57it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   2%|▏         | 553/24289 [02:34<1:50:48,  3.57it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   2%|▏         | 554/24289 [02:35<1:50:46,  3.57it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   2%|▏         | 554/24289 [02:35<1:50:46,  3.57it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   2%|▏         | 555/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   2%|▏         | 555/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0811, train_metrics/recon_loss_step=0.0811]
Epoch 0:   2%|▏         | 556/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0811, train_metrics/recon_loss_step=0.0811]
Epoch 0:   2%|▏         | 556/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]
Epoch 0:   2%|▏         | 557/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]
Epoch 0:   2%|▏         | 557/24289 [02:35<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0218, train_metrics/recon_loss_step=0.0218]
Epoch 0:   2%|▏         | 558/24289 [02:36<1:50:42,  3.57it/s, v_num=zwmv, train_loss_step=0.0218, train_metrics/recon_loss_step=0.0218]
Epoch 0:   2%|▏         | 558/24289 [02:36<1:50:42,  3.57it/s, v_num=zwmv, train_loss_step=0.0841, train_metrics/recon_loss_step=0.0841]
Epoch 0:   2%|▏         | 559/24289 [02:36<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0841, train_metrics/recon_loss_step=0.0841]
Epoch 0:   2%|▏         | 559/24289 [02:36<1:50:43,  3.57it/s, v_num=zwmv, train_loss_step=0.0689, train_metrics/recon_loss_step=0.0689]
Epoch 0:   2%|▏         | 560/24289 [02:36<1:50:42,  3.57it/s, v_num=zwmv, train_loss_step=0.0689, train_metrics/recon_loss_step=0.0689]
Epoch 0:   2%|▏         | 560/24289 [02:36<1:50:42,  3.57it/s, v_num=zwmv, train_loss_step=0.0689, train_metrics/recon_loss_step=0.0689]
Epoch 0:   2%|▏         | 561/24289 [02:36<1:50:39,  3.57it/s, v_num=zwmv, train_loss_step=0.0689, train_metrics/recon_loss_step=0.0689]
Epoch 0:   2%|▏         | 561/24289 [02:36<1:50:39,  3.57it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]  
Epoch 0:   2%|▏         | 562/24289 [02:37<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   2%|▏         | 562/24289 [02:37<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   2%|▏         | 563/24289 [02:37<1:50:36,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   2%|▏         | 563/24289 [02:37<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   2%|▏         | 564/24289 [02:37<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   2%|▏         | 564/24289 [02:37<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   2%|▏         | 565/24289 [02:38<1:50:35,  3.58it/s, v_num=zwmv, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   2%|▏         | 565/24289 [02:38<1:50:35,  3.58it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   2%|▏         | 566/24289 [02:38<1:50:39,  3.57it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   2%|▏         | 566/24289 [02:38<1:50:39,  3.57it/s, v_num=zwmv, train_loss_step=0.0379, train_metrics/recon_loss_step=0.0379]
Epoch 0:   2%|▏         | 567/24289 [02:38<1:50:38,  3.57it/s, v_num=zwmv, train_loss_step=0.0379, train_metrics/recon_loss_step=0.0379]
Epoch 0:   2%|▏         | 567/24289 [02:38<1:50:38,  3.57it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]  
Epoch 0:   2%|▏         | 568/24289 [02:38<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 568/24289 [02:38<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.082, train_metrics/recon_loss_step=0.082]
Epoch 0:   2%|▏         | 569/24289 [02:39<1:50:34,  3.58it/s, v_num=zwmv, train_loss_step=0.082, train_metrics/recon_loss_step=0.082]
Epoch 0:   2%|▏         | 569/24289 [02:39<1:50:34,  3.58it/s, v_num=zwmv, train_loss_step=0.0672, train_metrics/recon_loss_step=0.0672]
Epoch 0:   2%|▏         | 570/24289 [02:39<1:50:34,  3.58it/s, v_num=zwmv, train_loss_step=0.0672, train_metrics/recon_loss_step=0.0672]
Epoch 0:   2%|▏         | 570/24289 [02:39<1:50:34,  3.58it/s, v_num=zwmv, train_loss_step=0.00488, train_metrics/recon_loss_step=0.00488]
Epoch 0:   2%|▏         | 571/24289 [02:39<1:50:32,  3.58it/s, v_num=zwmv, train_loss_step=0.00488, train_metrics/recon_loss_step=0.00488]
Epoch 0:   2%|▏         | 571/24289 [02:39<1:50:32,  3.58it/s, v_num=zwmv, train_loss_step=0.0565, train_metrics/recon_loss_step=0.0565]  
Epoch 0:   2%|▏         | 572/24289 [02:40<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.0565, train_metrics/recon_loss_step=0.0565]
Epoch 0:   2%|▏         | 572/24289 [02:40<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]  
Epoch 0:   2%|▏         | 573/24289 [02:40<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   2%|▏         | 573/24289 [02:40<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0747, train_metrics/recon_loss_step=0.0747]
Epoch 0:   2%|▏         | 574/24289 [02:40<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0747, train_metrics/recon_loss_step=0.0747]
Epoch 0:   2%|▏         | 574/24289 [02:40<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0418, train_metrics/recon_loss_step=0.0418]
Epoch 0:   2%|▏         | 575/24289 [02:40<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0418, train_metrics/recon_loss_step=0.0418]
Epoch 0:   2%|▏         | 575/24289 [02:40<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]  
Epoch 0:   2%|▏         | 576/24289 [02:41<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]
Epoch 0:   2%|▏         | 576/24289 [02:41<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0489, train_metrics/recon_loss_step=0.0489]
Epoch 0:   2%|▏         | 577/24289 [02:41<1:50:33,  3.57it/s, v_num=zwmv, train_loss_step=0.0489, train_metrics/recon_loss_step=0.0489]
Epoch 0:   2%|▏         | 577/24289 [02:41<1:50:33,  3.57it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  
Epoch 0:   2%|▏         | 578/24289 [02:41<1:50:33,  3.57it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   2%|▏         | 578/24289 [02:41<1:50:33,  3.57it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   2%|▏         | 579/24289 [02:41<1:50:31,  3.58it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   2%|▏         | 579/24289 [02:41<1:50:31,  3.58it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 580/24289 [02:42<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0931, train_metrics/recon_loss_step=0.0931]
Epoch 0:   2%|▏         | 580/24289 [02:42<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]  
Epoch 0:   2%|▏         | 581/24289 [02:42<1:50:34,  3.57it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   2%|▏         | 581/24289 [02:42<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 582/24289 [02:42<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 582/24289 [02:42<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.0692, train_metrics/recon_loss_step=0.0692]
Epoch 0:   2%|▏         | 583/24289 [02:43<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.0692, train_metrics/recon_loss_step=0.0692]
Epoch 0:   2%|▏         | 583/24289 [02:43<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]  
Epoch 0:   2%|▏         | 584/24289 [02:43<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   2%|▏         | 584/24289 [02:43<1:50:37,  3.57it/s, v_num=zwmv, train_loss_step=0.0322, train_metrics/recon_loss_step=0.0322]
Epoch 0:   2%|▏         | 585/24289 [02:43<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0322, train_metrics/recon_loss_step=0.0322]
Epoch 0:   2%|▏         | 585/24289 [02:43<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0615, train_metrics/recon_loss_step=0.0615]
Epoch 0:   2%|▏         | 586/24289 [02:44<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0615, train_metrics/recon_loss_step=0.0615]
Epoch 0:   2%|▏         | 586/24289 [02:44<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0671, train_metrics/recon_loss_step=0.0671]
Epoch 0:   2%|▏         | 587/24289 [02:44<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0671, train_metrics/recon_loss_step=0.0671]
Epoch 0:   2%|▏         | 587/24289 [02:44<1:50:36,  3.57it/s, v_num=zwmv, train_loss_step=0.0597, train_metrics/recon_loss_step=0.0597]
Epoch 0:   2%|▏         | 588/24289 [02:44<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0597, train_metrics/recon_loss_step=0.0597]
Epoch 0:   2%|▏         | 588/24289 [02:44<1:50:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0823, train_metrics/recon_loss_step=0.0823]
Epoch 0:   2%|▏         | 589/24289 [02:44<1:50:34,  3.57it/s, v_num=zwmv, train_loss_step=0.0823, train_metrics/recon_loss_step=0.0823]
Epoch 0:   2%|▏         | 589/24289 [02:44<1:50:34,  3.57it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]  
Epoch 0:   2%|▏         | 590/24289 [02:45<1:50:32,  3.57it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   2%|▏         | 590/24289 [02:45<1:50:32,  3.57it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   2%|▏         | 591/24289 [02:45<1:50:32,  3.57it/s, v_num=zwmv, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   2%|▏         | 591/24289 [02:45<1:50:32,  3.57it/s, v_num=zwmv, train_loss_step=0.0422, train_metrics/recon_loss_step=0.0422]
Epoch 0:   2%|▏         | 592/24289 [02:45<1:50:30,  3.57it/s, v_num=zwmv, train_loss_step=0.0422, train_metrics/recon_loss_step=0.0422]
Epoch 0:   2%|▏         | 592/24289 [02:45<1:50:30,  3.57it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]  
Epoch 0:   2%|▏         | 593/24289 [02:45<1:50:28,  3.57it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   2%|▏         | 593/24289 [02:45<1:50:28,  3.57it/s, v_num=zwmv, train_loss_step=0.0565, train_metrics/recon_loss_step=0.0565]
Epoch 0:   2%|▏         | 594/24289 [02:46<1:50:27,  3.58it/s, v_num=zwmv, train_loss_step=0.0565, train_metrics/recon_loss_step=0.0565]
Epoch 0:   2%|▏         | 594/24289 [02:46<1:50:27,  3.58it/s, v_num=zwmv, train_loss_step=0.214, train_metrics/recon_loss_step=0.214]  
Epoch 0:   2%|▏         | 595/24289 [02:46<1:50:26,  3.58it/s, v_num=zwmv, train_loss_step=0.214, train_metrics/recon_loss_step=0.214]
Epoch 0:   2%|▏         | 595/24289 [02:46<1:50:26,  3.58it/s, v_num=zwmv, train_loss_step=0.0854, train_metrics/recon_loss_step=0.0854]
Epoch 0:   2%|▏         | 596/24289 [02:46<1:50:24,  3.58it/s, v_num=zwmv, train_loss_step=0.0854, train_metrics/recon_loss_step=0.0854]
Epoch 0:   2%|▏         | 596/24289 [02:46<1:50:24,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]  
Epoch 0:   2%|▏         | 597/24289 [02:46<1:50:24,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   2%|▏         | 597/24289 [02:46<1:50:24,  3.58it/s, v_num=zwmv, train_loss_step=0.0765, train_metrics/recon_loss_step=0.0765]
Epoch 0:   2%|▏         | 598/24289 [02:47<1:50:23,  3.58it/s, v_num=zwmv, train_loss_step=0.0765, train_metrics/recon_loss_step=0.0765]
Epoch 0:   2%|▏         | 598/24289 [02:47<1:50:23,  3.58it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]  
Epoch 0:   2%|▏         | 599/24289 [02:47<1:50:21,  3.58it/s, v_num=zwmv, train_loss_step=0.128, train_metrics/recon_loss_step=0.128]
Epoch 0:   2%|▏         | 599/24289 [02:47<1:50:21,  3.58it/s, v_num=zwmv, train_loss_step=0.0611, train_metrics/recon_loss_step=0.0611]
Epoch 0:   2%|▏         | 600/24289 [02:47<1:50:19,  3.58it/s, v_num=zwmv, train_loss_step=0.0611, train_metrics/recon_loss_step=0.0611]
Epoch 0:   2%|▏         | 600/24289 [02:47<1:50:19,  3.58it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]  
Epoch 0:   2%|▏         | 601/24289 [02:47<1:50:18,  3.58it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   2%|▏         | 601/24289 [02:47<1:50:18,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   2%|▏         | 602/24289 [02:48<1:50:17,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   2%|▏         | 602/24289 [02:48<1:50:17,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   2%|▏         | 603/24289 [02:48<1:50:15,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   2%|▏         | 603/24289 [02:48<1:50:15,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   2%|▏         | 604/24289 [02:48<1:50:14,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   2%|▏         | 604/24289 [02:48<1:50:14,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   2%|▏         | 605/24289 [02:48<1:50:14,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   2%|▏         | 605/24289 [02:48<1:50:14,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   2%|▏         | 606/24289 [02:49<1:50:13,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   2%|▏         | 606/24289 [02:49<1:50:13,  3.58it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 607/24289 [02:49<1:50:11,  3.58it/s, v_num=zwmv, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   2%|▏         | 607/24289 [02:49<1:50:11,  3.58it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   3%|▎         | 608/24289 [02:49<1:50:11,  3.58it/s, v_num=zwmv, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   3%|▎         | 608/24289 [02:49<1:50:11,  3.58it/s, v_num=zwmv, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]
Epoch 0:   3%|▎         | 609/24289 [02:50<1:50:10,  3.58it/s, v_num=zwmv, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]
Epoch 0:   3%|▎         | 609/24289 [02:50<1:50:10,  3.58it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   3%|▎         | 610/24289 [02:50<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   3%|▎         | 610/24289 [02:50<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0952, train_metrics/recon_loss_step=0.0952]
Epoch 0:   3%|▎         | 611/24289 [02:50<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0952, train_metrics/recon_loss_step=0.0952]
Epoch 0:   3%|▎         | 611/24289 [02:50<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0307, train_metrics/recon_loss_step=0.0307]
Epoch 0:   3%|▎         | 612/24289 [02:50<1:50:10,  3.58it/s, v_num=zwmv, train_loss_step=0.0307, train_metrics/recon_loss_step=0.0307]
Epoch 0:   3%|▎         | 612/24289 [02:50<1:50:11,  3.58it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]  
Epoch 0:   3%|▎         | 613/24289 [02:51<1:50:10,  3.58it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   3%|▎         | 613/24289 [02:51<1:50:10,  3.58it/s, v_num=zwmv, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   3%|▎         | 614/24289 [02:51<1:50:09,  3.58it/s, v_num=zwmv, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   3%|▎         | 614/24289 [02:51<1:50:09,  3.58it/s, v_num=zwmv, train_loss_step=0.0617, train_metrics/recon_loss_step=0.0617]
Epoch 0:   3%|▎         | 615/24289 [02:51<1:50:09,  3.58it/s, v_num=zwmv, train_loss_step=0.0617, train_metrics/recon_loss_step=0.0617]
Epoch 0:   3%|▎         | 615/24289 [02:51<1:50:09,  3.58it/s, v_num=zwmv, train_loss_step=0.0842, train_metrics/recon_loss_step=0.0842]
Epoch 0:   3%|▎         | 616/24289 [02:51<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0842, train_metrics/recon_loss_step=0.0842]
Epoch 0:   3%|▎         | 616/24289 [02:51<1:50:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0967, train_metrics/recon_loss_step=0.0967]
Epoch 0:   3%|▎         | 617/24289 [02:52<1:50:07,  3.58it/s, v_num=zwmv, train_loss_step=0.0967, train_metrics/recon_loss_step=0.0967]
Epoch 0:   3%|▎         | 617/24289 [02:52<1:50:07,  3.58it/s, v_num=zwmv, train_loss_step=0.0772, train_metrics/recon_loss_step=0.0772]
Epoch 0:   3%|▎         | 618/24289 [02:52<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.0772, train_metrics/recon_loss_step=0.0772]
Epoch 0:   3%|▎         | 618/24289 [02:52<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]  
Epoch 0:   3%|▎         | 619/24289 [02:52<1:50:07,  3.58it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   3%|▎         | 619/24289 [02:52<1:50:07,  3.58it/s, v_num=zwmv, train_loss_step=0.0953, train_metrics/recon_loss_step=0.0953]
Epoch 0:   3%|▎         | 620/24289 [02:53<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.0953, train_metrics/recon_loss_step=0.0953]
Epoch 0:   3%|▎         | 620/24289 [02:53<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]  
Epoch 0:   3%|▎         | 621/24289 [02:53<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   3%|▎         | 621/24289 [02:53<1:50:06,  3.58it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   3%|▎         | 622/24289 [02:53<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   3%|▎         | 622/24289 [02:53<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   3%|▎         | 623/24289 [02:53<1:50:04,  3.58it/s, v_num=zwmv, train_loss_step=0.114, train_metrics/recon_loss_step=0.114]
Epoch 0:   3%|▎         | 623/24289 [02:53<1:50:04,  3.58it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   3%|▎         | 624/24289 [02:54<1:50:02,  3.58it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   3%|▎         | 624/24289 [02:54<1:50:02,  3.58it/s, v_num=zwmv, train_loss_step=0.0824, train_metrics/recon_loss_step=0.0824]
Epoch 0:   3%|▎         | 625/24289 [02:54<1:50:01,  3.58it/s, v_num=zwmv, train_loss_step=0.0824, train_metrics/recon_loss_step=0.0824]
Epoch 0:   3%|▎         | 625/24289 [02:54<1:50:01,  3.58it/s, v_num=zwmv, train_loss_step=0.0946, train_metrics/recon_loss_step=0.0946]
Epoch 0:   3%|▎         | 626/24289 [02:54<1:50:00,  3.58it/s, v_num=zwmv, train_loss_step=0.0946, train_metrics/recon_loss_step=0.0946]
Epoch 0:   3%|▎         | 626/24289 [02:54<1:50:00,  3.58it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]  
Epoch 0:   3%|▎         | 627/24289 [02:54<1:50:03,  3.58it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   3%|▎         | 627/24289 [02:54<1:50:04,  3.58it/s, v_num=zwmv, train_loss_step=0.0625, train_metrics/recon_loss_step=0.0625]
Epoch 0:   3%|▎         | 628/24289 [02:55<1:50:04,  3.58it/s, v_num=zwmv, train_loss_step=0.0625, train_metrics/recon_loss_step=0.0625]
Epoch 0:   3%|▎         | 628/24289 [02:55<1:50:04,  3.58it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]  
Epoch 0:   3%|▎         | 629/24289 [02:55<1:50:03,  3.58it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   3%|▎         | 629/24289 [02:55<1:50:03,  3.58it/s, v_num=zwmv, train_loss_step=0.0703, train_metrics/recon_loss_step=0.0703]
Epoch 0:   3%|▎         | 630/24289 [02:55<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.0703, train_metrics/recon_loss_step=0.0703]
Epoch 0:   3%|▎         | 630/24289 [02:55<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]  
Epoch 0:   3%|▎         | 631/24289 [02:56<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 631/24289 [02:56<1:50:05,  3.58it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 632/24289 [02:56<1:50:02,  3.58it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 632/24289 [02:56<1:50:03,  3.58it/s, v_num=zwmv, train_loss_step=0.0738, train_metrics/recon_loss_step=0.0738]
Epoch 0:   3%|▎         | 633/24289 [02:56<1:50:01,  3.58it/s, v_num=zwmv, train_loss_step=0.0738, train_metrics/recon_loss_step=0.0738]
Epoch 0:   3%|▎         | 633/24289 [02:56<1:50:01,  3.58it/s, v_num=zwmv, train_loss_step=0.0811, train_metrics/recon_loss_step=0.0811]
Epoch 0:   3%|▎         | 634/24289 [02:56<1:50:01,  3.58it/s, v_num=zwmv, train_loss_step=0.0811, train_metrics/recon_loss_step=0.0811]
Epoch 0:   3%|▎         | 634/24289 [02:56<1:50:02,  3.58it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]  
Epoch 0:   3%|▎         | 635/24289 [02:57<1:50:00,  3.58it/s, v_num=zwmv, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   3%|▎         | 635/24289 [02:57<1:50:00,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   3%|▎         | 636/24289 [02:57<1:49:58,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   3%|▎         | 636/24289 [02:57<1:49:58,  3.58it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   3%|▎         | 637/24289 [02:57<1:49:56,  3.59it/s, v_num=zwmv, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   3%|▎         | 637/24289 [02:57<1:49:56,  3.59it/s, v_num=zwmv, train_loss_step=0.0903, train_metrics/recon_loss_step=0.0903]
Epoch 0:   3%|▎         | 638/24289 [02:57<1:49:55,  3.59it/s, v_num=zwmv, train_loss_step=0.0903, train_metrics/recon_loss_step=0.0903]
Epoch 0:   3%|▎         | 638/24289 [02:57<1:49:55,  3.59it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]  
Epoch 0:   3%|▎         | 639/24289 [02:58<1:49:53,  3.59it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   3%|▎         | 639/24289 [02:58<1:49:53,  3.59it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 640/24289 [02:58<1:49:51,  3.59it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 640/24289 [02:58<1:49:51,  3.59it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   3%|▎         | 641/24289 [02:58<1:49:54,  3.59it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   3%|▎         | 641/24289 [02:58<1:49:54,  3.59it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   3%|▎         | 642/24289 [02:59<1:49:53,  3.59it/s, v_num=zwmv, train_loss_step=0.148, train_metrics/recon_loss_step=0.148]
Epoch 0:   3%|▎         | 642/24289 [02:59<1:49:53,  3.59it/s, v_num=zwmv, train_loss_step=0.0748, train_metrics/recon_loss_step=0.0748]
Epoch 0:   3%|▎         | 643/24289 [02:59<1:49:52,  3.59it/s, v_num=zwmv, train_loss_step=0.0748, train_metrics/recon_loss_step=0.0748]
Epoch 0:   3%|▎         | 643/24289 [02:59<1:49:52,  3.59it/s, v_num=zwmv, train_loss_step=0.0592, train_metrics/recon_loss_step=0.0592]
Epoch 0:   3%|▎         | 644/24289 [02:59<1:49:49,  3.59it/s, v_num=zwmv, train_loss_step=0.0592, train_metrics/recon_loss_step=0.0592]
Epoch 0:   3%|▎         | 644/24289 [02:59<1:49:49,  3.59it/s, v_num=zwmv, train_loss_step=0.0402, train_metrics/recon_loss_step=0.0402]
Epoch 0:   3%|▎         | 645/24289 [02:59<1:49:48,  3.59it/s, v_num=zwmv, train_loss_step=0.0402, train_metrics/recon_loss_step=0.0402]
Epoch 0:   3%|▎         | 645/24289 [02:59<1:49:48,  3.59it/s, v_num=zwmv, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]
Epoch 0:   3%|▎         | 646/24289 [02:59<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]
Epoch 0:   3%|▎         | 646/24289 [02:59<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]  
Epoch 0:   3%|▎         | 647/24289 [03:00<1:49:46,  3.59it/s, v_num=zwmv, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   3%|▎         | 647/24289 [03:00<1:49:46,  3.59it/s, v_num=zwmv, train_loss_step=0.227, train_metrics/recon_loss_step=0.227]
Epoch 0:   3%|▎         | 648/24289 [03:00<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.227, train_metrics/recon_loss_step=0.227]
Epoch 0:   3%|▎         | 648/24289 [03:00<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 649/24289 [03:00<1:49:49,  3.59it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 649/24289 [03:00<1:49:49,  3.59it/s, v_num=zwmv, train_loss_step=0.0145, train_metrics/recon_loss_step=0.0145]
Epoch 0:   3%|▎         | 650/24289 [03:01<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.0145, train_metrics/recon_loss_step=0.0145]
Epoch 0:   3%|▎         | 650/24289 [03:01<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.0821, train_metrics/recon_loss_step=0.0821]
Epoch 0:   3%|▎         | 651/24289 [03:01<1:49:46,  3.59it/s, v_num=zwmv, train_loss_step=0.0821, train_metrics/recon_loss_step=0.0821]
Epoch 0:   3%|▎         | 651/24289 [03:01<1:49:46,  3.59it/s, v_num=zwmv, train_loss_step=0.329, train_metrics/recon_loss_step=0.329]  
Epoch 0:   3%|▎         | 652/24289 [03:01<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.329, train_metrics/recon_loss_step=0.329]
Epoch 0:   3%|▎         | 652/24289 [03:01<1:49:47,  3.59it/s, v_num=zwmv, train_loss_step=0.0798, train_metrics/recon_loss_step=0.0798]
Epoch 0:   3%|▎         | 653/24289 [03:01<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.0798, train_metrics/recon_loss_step=0.0798]
Epoch 0:   3%|▎         | 653/24289 [03:01<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.0872, train_metrics/recon_loss_step=0.0872]
Epoch 0:   3%|▎         | 654/24289 [03:02<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.0872, train_metrics/recon_loss_step=0.0872]
Epoch 0:   3%|▎         | 654/24289 [03:02<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.0517, train_metrics/recon_loss_step=0.0517]
Epoch 0:   3%|▎         | 655/24289 [03:02<1:49:48,  3.59it/s, v_num=zwmv, train_loss_step=0.0517, train_metrics/recon_loss_step=0.0517]
Epoch 0:   3%|▎         | 655/24289 [03:02<1:49:48,  3.59it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]  
Epoch 0:   3%|▎         | 656/24289 [03:02<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   3%|▎         | 656/24289 [03:02<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 657/24289 [03:03<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 657/24289 [03:03<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.0682, train_metrics/recon_loss_step=0.0682]
Epoch 0:   3%|▎         | 658/24289 [03:03<1:49:42,  3.59it/s, v_num=zwmv, train_loss_step=0.0682, train_metrics/recon_loss_step=0.0682]
Epoch 0:   3%|▎         | 658/24289 [03:03<1:49:42,  3.59it/s, v_num=zwmv, train_loss_step=0.0906, train_metrics/recon_loss_step=0.0906]
Epoch 0:   3%|▎         | 659/24289 [03:03<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.0906, train_metrics/recon_loss_step=0.0906]
Epoch 0:   3%|▎         | 659/24289 [03:03<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]  
Epoch 0:   3%|▎         | 660/24289 [03:03<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 660/24289 [03:03<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.0837, train_metrics/recon_loss_step=0.0837]
Epoch 0:   3%|▎         | 661/24289 [03:04<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.0837, train_metrics/recon_loss_step=0.0837]
Epoch 0:   3%|▎         | 661/24289 [03:04<1:49:45,  3.59it/s, v_num=zwmv, train_loss_step=0.0599, train_metrics/recon_loss_step=0.0599]
Epoch 0:   3%|▎         | 662/24289 [03:04<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.0599, train_metrics/recon_loss_step=0.0599]
Epoch 0:   3%|▎         | 662/24289 [03:04<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.081, train_metrics/recon_loss_step=0.081]  
Epoch 0:   3%|▎         | 663/24289 [03:04<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.081, train_metrics/recon_loss_step=0.081]
Epoch 0:   3%|▎         | 663/24289 [03:04<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   3%|▎         | 664/24289 [03:05<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   3%|▎         | 664/24289 [03:05<1:49:43,  3.59it/s, v_num=zwmv, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]
Epoch 0:   3%|▎         | 665/24289 [03:05<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]
Epoch 0:   3%|▎         | 665/24289 [03:05<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.0624, train_metrics/recon_loss_step=0.0624]
Epoch 0:   3%|▎         | 666/24289 [03:05<1:49:39,  3.59it/s, v_num=zwmv, train_loss_step=0.0624, train_metrics/recon_loss_step=0.0624]
Epoch 0:   3%|▎         | 666/24289 [03:05<1:49:39,  3.59it/s, v_num=zwmv, train_loss_step=0.0349, train_metrics/recon_loss_step=0.0349]
Epoch 0:   3%|▎         | 667/24289 [03:05<1:49:38,  3.59it/s, v_num=zwmv, train_loss_step=0.0349, train_metrics/recon_loss_step=0.0349]
Epoch 0:   3%|▎         | 667/24289 [03:05<1:49:38,  3.59it/s, v_num=zwmv, train_loss_step=0.0991, train_metrics/recon_loss_step=0.0991]
Epoch 0:   3%|▎         | 668/24289 [03:06<1:49:40,  3.59it/s, v_num=zwmv, train_loss_step=0.0991, train_metrics/recon_loss_step=0.0991]
Epoch 0:   3%|▎         | 668/24289 [03:06<1:49:40,  3.59it/s, v_num=zwmv, train_loss_step=0.0573, train_metrics/recon_loss_step=0.0573]
Epoch 0:   3%|▎         | 669/24289 [03:06<1:49:39,  3.59it/s, v_num=zwmv, train_loss_step=0.0573, train_metrics/recon_loss_step=0.0573]
Epoch 0:   3%|▎         | 669/24289 [03:06<1:49:39,  3.59it/s, v_num=zwmv, train_loss_step=0.0473, train_metrics/recon_loss_step=0.0473]
Epoch 0:   3%|▎         | 670/24289 [03:06<1:49:42,  3.59it/s, v_num=zwmv, train_loss_step=0.0473, train_metrics/recon_loss_step=0.0473]
Epoch 0:   3%|▎         | 670/24289 [03:06<1:49:42,  3.59it/s, v_num=zwmv, train_loss_step=0.0252, train_metrics/recon_loss_step=0.0252]
Epoch 0:   3%|▎         | 671/24289 [03:06<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.0252, train_metrics/recon_loss_step=0.0252]
Epoch 0:   3%|▎         | 671/24289 [03:06<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]  
Epoch 0:   3%|▎         | 672/24289 [03:07<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 672/24289 [03:07<1:49:41,  3.59it/s, v_num=zwmv, train_loss_step=0.224, train_metrics/recon_loss_step=0.224]
Epoch 0:   3%|▎         | 673/24289 [03:07<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.224, train_metrics/recon_loss_step=0.224]
Epoch 0:   3%|▎         | 673/24289 [03:07<1:49:44,  3.59it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 674/24289 [03:08<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 674/24289 [03:08<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.0507, train_metrics/recon_loss_step=0.0507]
Epoch 0:   3%|▎         | 675/24289 [03:08<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.0507, train_metrics/recon_loss_step=0.0507]
Epoch 0:   3%|▎         | 675/24289 [03:08<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]  
Epoch 0:   3%|▎         | 676/24289 [03:08<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   3%|▎         | 676/24289 [03:08<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.0678, train_metrics/recon_loss_step=0.0678]
Epoch 0:   3%|▎         | 677/24289 [03:08<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.0678, train_metrics/recon_loss_step=0.0678]
Epoch 0:   3%|▎         | 677/24289 [03:08<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.270, train_metrics/recon_loss_step=0.270]  
Epoch 0:   3%|▎         | 678/24289 [03:09<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.270, train_metrics/recon_loss_step=0.270]
Epoch 0:   3%|▎         | 678/24289 [03:09<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   3%|▎         | 679/24289 [03:09<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.147, train_metrics/recon_loss_step=0.147]
Epoch 0:   3%|▎         | 679/24289 [03:09<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   3%|▎         | 680/24289 [03:09<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   3%|▎         | 680/24289 [03:09<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   3%|▎         | 681/24289 [03:10<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   3%|▎         | 681/24289 [03:10<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0842, train_metrics/recon_loss_step=0.0842]
Epoch 0:   3%|▎         | 682/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0842, train_metrics/recon_loss_step=0.0842]
Epoch 0:   3%|▎         | 682/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0502, train_metrics/recon_loss_step=0.0502]
Epoch 0:   3%|▎         | 683/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0502, train_metrics/recon_loss_step=0.0502]
Epoch 0:   3%|▎         | 683/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  
Epoch 0:   3%|▎         | 684/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 684/24289 [03:10<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0637, train_metrics/recon_loss_step=0.0637]
Epoch 0:   3%|▎         | 685/24289 [03:11<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0637, train_metrics/recon_loss_step=0.0637]
Epoch 0:   3%|▎         | 685/24289 [03:11<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]  
Epoch 0:   3%|▎         | 686/24289 [03:11<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   3%|▎         | 686/24289 [03:11<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.096, train_metrics/recon_loss_step=0.096]
Epoch 0:   3%|▎         | 687/24289 [03:11<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.096, train_metrics/recon_loss_step=0.096]
Epoch 0:   3%|▎         | 687/24289 [03:11<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.0466, train_metrics/recon_loss_step=0.0466]
Epoch 0:   3%|▎         | 688/24289 [03:12<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.0466, train_metrics/recon_loss_step=0.0466]
Epoch 0:   3%|▎         | 688/24289 [03:12<1:49:51,  3.58it/s, v_num=zwmv, train_loss_step=0.0693, train_metrics/recon_loss_step=0.0693]
Epoch 0:   3%|▎         | 689/24289 [03:12<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0693, train_metrics/recon_loss_step=0.0693]
Epoch 0:   3%|▎         | 689/24289 [03:12<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0938, train_metrics/recon_loss_step=0.0938]
Epoch 0:   3%|▎         | 690/24289 [03:12<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0938, train_metrics/recon_loss_step=0.0938]
Epoch 0:   3%|▎         | 690/24289 [03:12<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0571, train_metrics/recon_loss_step=0.0571]
Epoch 0:   3%|▎         | 691/24289 [03:12<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.0571, train_metrics/recon_loss_step=0.0571]
Epoch 0:   3%|▎         | 691/24289 [03:12<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.0726, train_metrics/recon_loss_step=0.0726]
Epoch 0:   3%|▎         | 692/24289 [03:13<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.0726, train_metrics/recon_loss_step=0.0726]
Epoch 0:   3%|▎         | 692/24289 [03:13<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]  
Epoch 0:   3%|▎         | 693/24289 [03:13<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   3%|▎         | 693/24289 [03:13<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 694/24289 [03:13<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 694/24289 [03:13<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0831, train_metrics/recon_loss_step=0.0831]
Epoch 0:   3%|▎         | 695/24289 [03:13<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.0831, train_metrics/recon_loss_step=0.0831]
Epoch 0:   3%|▎         | 695/24289 [03:13<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.0274, train_metrics/recon_loss_step=0.0274]
Epoch 0:   3%|▎         | 696/24289 [03:14<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0274, train_metrics/recon_loss_step=0.0274]
Epoch 0:   3%|▎         | 696/24289 [03:14<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]  
Epoch 0:   3%|▎         | 697/24289 [03:14<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 697/24289 [03:14<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 698/24289 [03:14<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 698/24289 [03:14<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   3%|▎         | 699/24289 [03:15<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   3%|▎         | 699/24289 [03:15<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.0937, train_metrics/recon_loss_step=0.0937]
Epoch 0:   3%|▎         | 700/24289 [03:15<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0937, train_metrics/recon_loss_step=0.0937]
Epoch 0:   3%|▎         | 700/24289 [03:15<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0378, train_metrics/recon_loss_step=0.0378]
Epoch 0:   3%|▎         | 701/24289 [03:15<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0378, train_metrics/recon_loss_step=0.0378]
Epoch 0:   3%|▎         | 701/24289 [03:15<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0787, train_metrics/recon_loss_step=0.0787]
Epoch 0:   3%|▎         | 702/24289 [03:15<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0787, train_metrics/recon_loss_step=0.0787]
Epoch 0:   3%|▎         | 702/24289 [03:15<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0637, train_metrics/recon_loss_step=0.0637]
Epoch 0:   3%|▎         | 703/24289 [03:16<1:49:52,  3.58it/s, v_num=zwmv, train_loss_step=0.0637, train_metrics/recon_loss_step=0.0637]
Epoch 0:   3%|▎         | 703/24289 [03:16<1:49:52,  3.58it/s, v_num=zwmv, train_loss_step=0.0243, train_metrics/recon_loss_step=0.0243]
Epoch 0:   3%|▎         | 704/24289 [03:16<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.0243, train_metrics/recon_loss_step=0.0243]
Epoch 0:   3%|▎         | 704/24289 [03:16<1:49:50,  3.58it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]  
Epoch 0:   3%|▎         | 705/24289 [03:16<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   3%|▎         | 705/24289 [03:16<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.0552, train_metrics/recon_loss_step=0.0552]
Epoch 0:   3%|▎         | 706/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.0552, train_metrics/recon_loss_step=0.0552]
Epoch 0:   3%|▎         | 706/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  
Epoch 0:   3%|▎         | 707/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 707/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.00435, train_metrics/recon_loss_step=0.00435]
Epoch 0:   3%|▎         | 708/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.00435, train_metrics/recon_loss_step=0.00435]
Epoch 0:   3%|▎         | 708/24289 [03:17<1:49:48,  3.58it/s, v_num=zwmv, train_loss_step=0.286, train_metrics/recon_loss_step=0.286]    
Epoch 0:   3%|▎         | 709/24289 [03:18<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.286, train_metrics/recon_loss_step=0.286]
Epoch 0:   3%|▎         | 709/24289 [03:18<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   3%|▎         | 710/24289 [03:18<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   3%|▎         | 710/24289 [03:18<1:49:44,  3.58it/s, v_num=zwmv, train_loss_step=0.0758, train_metrics/recon_loss_step=0.0758]
Epoch 0:   3%|▎         | 711/24289 [03:18<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0758, train_metrics/recon_loss_step=0.0758]
Epoch 0:   3%|▎         | 711/24289 [03:18<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.0358, train_metrics/recon_loss_step=0.0358]
Epoch 0:   3%|▎         | 712/24289 [03:19<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0358, train_metrics/recon_loss_step=0.0358]
Epoch 0:   3%|▎         | 712/24289 [03:19<1:49:49,  3.58it/s, v_num=zwmv, train_loss_step=0.0321, train_metrics/recon_loss_step=0.0321]
Epoch 0:   3%|▎         | 713/24289 [03:19<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.0321, train_metrics/recon_loss_step=0.0321]
Epoch 0:   3%|▎         | 713/24289 [03:19<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]  
Epoch 0:   3%|▎         | 714/24289 [03:19<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]
Epoch 0:   3%|▎         | 714/24289 [03:19<1:49:47,  3.58it/s, v_num=zwmv, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]
Epoch 0:   3%|▎         | 715/24289 [03:19<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]
Epoch 0:   3%|▎         | 715/24289 [03:19<1:49:46,  3.58it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   3%|▎         | 716/24289 [03:20<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   3%|▎         | 716/24289 [03:20<1:49:45,  3.58it/s, v_num=zwmv, train_loss_step=0.0959, train_metrics/recon_loss_step=0.0959]
Epoch 0:   3%|▎         | 717/24289 [03:20<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.0959, train_metrics/recon_loss_step=0.0959]
Epoch 0:   3%|▎         | 717/24289 [03:20<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.0716, train_metrics/recon_loss_step=0.0716]
Epoch 0:   3%|▎         | 718/24289 [03:20<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0716, train_metrics/recon_loss_step=0.0716]
Epoch 0:   3%|▎         | 718/24289 [03:20<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.0799, train_metrics/recon_loss_step=0.0799]
Epoch 0:   3%|▎         | 719/24289 [03:20<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.0799, train_metrics/recon_loss_step=0.0799]
Epoch 0:   3%|▎         | 719/24289 [03:20<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]  
Epoch 0:   3%|▎         | 720/24289 [03:21<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   3%|▎         | 720/24289 [03:21<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0745, train_metrics/recon_loss_step=0.0745]
Epoch 0:   3%|▎         | 721/24289 [03:21<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0745, train_metrics/recon_loss_step=0.0745]
Epoch 0:   3%|▎         | 721/24289 [03:21<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]  
Epoch 0:   3%|▎         | 722/24289 [03:21<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   3%|▎         | 722/24289 [03:21<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   3%|▎         | 723/24289 [03:21<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   3%|▎         | 723/24289 [03:21<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   3%|▎         | 724/24289 [03:22<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   3%|▎         | 724/24289 [03:22<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0605, train_metrics/recon_loss_step=0.0605]
Epoch 0:   3%|▎         | 725/24289 [03:22<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0605, train_metrics/recon_loss_step=0.0605]
Epoch 0:   3%|▎         | 725/24289 [03:22<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]  
Epoch 0:   3%|▎         | 726/24289 [03:22<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   3%|▎         | 726/24289 [03:22<1:49:43,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 727/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 727/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0747, train_metrics/recon_loss_step=0.0747]
Epoch 0:   3%|▎         | 728/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0747, train_metrics/recon_loss_step=0.0747]
Epoch 0:   3%|▎         | 728/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0746, train_metrics/recon_loss_step=0.0746]
Epoch 0:   3%|▎         | 729/24289 [03:23<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.0746, train_metrics/recon_loss_step=0.0746]
Epoch 0:   3%|▎         | 729/24289 [03:23<1:49:42,  3.58it/s, v_num=zwmv, train_loss_step=0.0802, train_metrics/recon_loss_step=0.0802]
Epoch 0:   3%|▎         | 730/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0802, train_metrics/recon_loss_step=0.0802]
Epoch 0:   3%|▎         | 730/24289 [03:23<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0949, train_metrics/recon_loss_step=0.0949]
Epoch 0:   3%|▎         | 731/24289 [03:24<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0949, train_metrics/recon_loss_step=0.0949]
Epoch 0:   3%|▎         | 731/24289 [03:24<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0393, train_metrics/recon_loss_step=0.0393]
Epoch 0:   3%|▎         | 732/24289 [03:24<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0393, train_metrics/recon_loss_step=0.0393]
Epoch 0:   3%|▎         | 732/24289 [03:24<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0804, train_metrics/recon_loss_step=0.0804]
Epoch 0:   3%|▎         | 733/24289 [03:24<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.0804, train_metrics/recon_loss_step=0.0804]
Epoch 0:   3%|▎         | 733/24289 [03:24<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]  
Epoch 0:   3%|▎         | 734/24289 [03:25<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   3%|▎         | 734/24289 [03:25<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0394, train_metrics/recon_loss_step=0.0394]
Epoch 0:   3%|▎         | 735/24289 [03:25<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0394, train_metrics/recon_loss_step=0.0394]
Epoch 0:   3%|▎         | 735/24289 [03:25<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]  
Epoch 0:   3%|▎         | 736/24289 [03:25<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   3%|▎         | 736/24289 [03:25<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.0601, train_metrics/recon_loss_step=0.0601]
Epoch 0:   3%|▎         | 737/24289 [03:25<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.0601, train_metrics/recon_loss_step=0.0601]
Epoch 0:   3%|▎         | 737/24289 [03:25<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]  
Epoch 0:   3%|▎         | 738/24289 [03:26<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]
Epoch 0:   3%|▎         | 738/24289 [03:26<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   3%|▎         | 739/24289 [03:26<1:49:33,  3.58it/s, v_num=zwmv, train_loss_step=0.146, train_metrics/recon_loss_step=0.146]
Epoch 0:   3%|▎         | 739/24289 [03:26<1:49:33,  3.58it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   3%|▎         | 740/24289 [03:26<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   3%|▎         | 740/24289 [03:26<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   3%|▎         | 741/24289 [03:26<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   3%|▎         | 741/24289 [03:26<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   3%|▎         | 742/24289 [03:27<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.175, train_metrics/recon_loss_step=0.175]
Epoch 0:   3%|▎         | 742/24289 [03:27<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]
Epoch 0:   3%|▎         | 743/24289 [03:27<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.101, train_metrics/recon_loss_step=0.101]
Epoch 0:   3%|▎         | 743/24289 [03:27<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]
Epoch 0:   3%|▎         | 744/24289 [03:27<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]
Epoch 0:   3%|▎         | 744/24289 [03:27<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0631, train_metrics/recon_loss_step=0.0631]
Epoch 0:   3%|▎         | 745/24289 [03:28<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0631, train_metrics/recon_loss_step=0.0631]
Epoch 0:   3%|▎         | 745/24289 [03:28<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  
Epoch 0:   3%|▎         | 746/24289 [03:28<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 746/24289 [03:28<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0982, train_metrics/recon_loss_step=0.0982]
Epoch 0:   3%|▎         | 747/24289 [03:28<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0982, train_metrics/recon_loss_step=0.0982]
Epoch 0:   3%|▎         | 747/24289 [03:28<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0656, train_metrics/recon_loss_step=0.0656]
Epoch 0:   3%|▎         | 748/24289 [03:29<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0656, train_metrics/recon_loss_step=0.0656]
Epoch 0:   3%|▎         | 748/24289 [03:29<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0649, train_metrics/recon_loss_step=0.0649]
Epoch 0:   3%|▎         | 749/24289 [03:29<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0649, train_metrics/recon_loss_step=0.0649]
Epoch 0:   3%|▎         | 749/24289 [03:29<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0746, train_metrics/recon_loss_step=0.0746]
Epoch 0:   3%|▎         | 750/24289 [03:29<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.0746, train_metrics/recon_loss_step=0.0746]
Epoch 0:   3%|▎         | 750/24289 [03:29<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]  
Epoch 0:   3%|▎         | 751/24289 [03:29<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.172, train_metrics/recon_loss_step=0.172]
Epoch 0:   3%|▎         | 751/24289 [03:29<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0435, train_metrics/recon_loss_step=0.0435]
Epoch 0:   3%|▎         | 752/24289 [03:30<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0435, train_metrics/recon_loss_step=0.0435]
Epoch 0:   3%|▎         | 752/24289 [03:30<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]  
Epoch 0:   3%|▎         | 753/24289 [03:30<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   3%|▎         | 753/24289 [03:30<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.0901, train_metrics/recon_loss_step=0.0901]
Epoch 0:   3%|▎         | 754/24289 [03:30<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0901, train_metrics/recon_loss_step=0.0901]
Epoch 0:   3%|▎         | 754/24289 [03:30<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0963, train_metrics/recon_loss_step=0.0963]
Epoch 0:   3%|▎         | 755/24289 [03:31<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0963, train_metrics/recon_loss_step=0.0963]
Epoch 0:   3%|▎         | 755/24289 [03:31<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]  
Epoch 0:   3%|▎         | 756/24289 [03:31<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   3%|▎         | 756/24289 [03:31<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 757/24289 [03:31<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 757/24289 [03:31<1:49:41,  3.58it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   3%|▎         | 758/24289 [03:31<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   3%|▎         | 758/24289 [03:31<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 759/24289 [03:32<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   3%|▎         | 759/24289 [03:32<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0346, train_metrics/recon_loss_step=0.0346]
Epoch 0:   3%|▎         | 760/24289 [03:32<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0346, train_metrics/recon_loss_step=0.0346]
Epoch 0:   3%|▎         | 760/24289 [03:32<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0707, train_metrics/recon_loss_step=0.0707]
Epoch 0:   3%|▎         | 761/24289 [03:32<1:49:42,  3.57it/s, v_num=zwmv, train_loss_step=0.0707, train_metrics/recon_loss_step=0.0707]
Epoch 0:   3%|▎         | 761/24289 [03:32<1:49:42,  3.57it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]  
Epoch 0:   3%|▎         | 762/24289 [03:33<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   3%|▎         | 762/24289 [03:33<1:49:40,  3.58it/s, v_num=zwmv, train_loss_step=0.0734, train_metrics/recon_loss_step=0.0734]
Epoch 0:   3%|▎         | 763/24289 [03:33<1:49:41,  3.57it/s, v_num=zwmv, train_loss_step=0.0734, train_metrics/recon_loss_step=0.0734]
Epoch 0:   3%|▎         | 763/24289 [03:33<1:49:41,  3.57it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]  
Epoch 0:   3%|▎         | 764/24289 [03:33<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]
Epoch 0:   3%|▎         | 764/24289 [03:33<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]
Epoch 0:   3%|▎         | 765/24289 [03:34<1:49:41,  3.57it/s, v_num=zwmv, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]
Epoch 0:   3%|▎         | 765/24289 [03:34<1:49:41,  3.57it/s, v_num=zwmv, train_loss_step=0.232, train_metrics/recon_loss_step=0.232]
Epoch 0:   3%|▎         | 766/24289 [03:34<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.232, train_metrics/recon_loss_step=0.232]
Epoch 0:   3%|▎         | 766/24289 [03:34<1:49:39,  3.58it/s, v_num=zwmv, train_loss_step=0.0881, train_metrics/recon_loss_step=0.0881]
Epoch 0:   3%|▎         | 767/24289 [03:34<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.0881, train_metrics/recon_loss_step=0.0881]
Epoch 0:   3%|▎         | 767/24289 [03:34<1:49:38,  3.58it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]  
Epoch 0:   3%|▎         | 768/24289 [03:34<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   3%|▎         | 768/24289 [03:34<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   3%|▎         | 769/24289 [03:35<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   3%|▎         | 769/24289 [03:35<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0787, train_metrics/recon_loss_step=0.0787]
Epoch 0:   3%|▎         | 770/24289 [03:35<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.0787, train_metrics/recon_loss_step=0.0787]
Epoch 0:   3%|▎         | 770/24289 [03:35<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.0667, train_metrics/recon_loss_step=0.0667]
Epoch 0:   3%|▎         | 771/24289 [03:35<1:49:39,  3.57it/s, v_num=zwmv, train_loss_step=0.0667, train_metrics/recon_loss_step=0.0667]
Epoch 0:   3%|▎         | 771/24289 [03:35<1:49:39,  3.57it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]  
Epoch 0:   3%|▎         | 772/24289 [03:35<1:49:39,  3.57it/s, v_num=zwmv, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   3%|▎         | 772/24289 [03:35<1:49:39,  3.57it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 773/24289 [03:36<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 773/24289 [03:36<1:49:40,  3.57it/s, v_num=zwmv, train_loss_step=0.285, train_metrics/recon_loss_step=0.285]
Epoch 0:   3%|▎         | 774/24289 [03:36<1:49:38,  3.57it/s, v_num=zwmv, train_loss_step=0.285, train_metrics/recon_loss_step=0.285]
Epoch 0:   3%|▎         | 774/24289 [03:36<1:49:38,  3.57it/s, v_num=zwmv, train_loss_step=0.0696, train_metrics/recon_loss_step=0.0696]
Epoch 0:   3%|▎         | 775/24289 [03:36<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0696, train_metrics/recon_loss_step=0.0696]
Epoch 0:   3%|▎         | 775/24289 [03:36<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.0856, train_metrics/recon_loss_step=0.0856]
Epoch 0:   3%|▎         | 776/24289 [03:37<1:49:36,  3.58it/s, v_num=zwmv, train_loss_step=0.0856, train_metrics/recon_loss_step=0.0856]
Epoch 0:   3%|▎         | 776/24289 [03:37<1:49:37,  3.58it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]  
Epoch 0:   3%|▎         | 777/24289 [03:37<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   3%|▎         | 777/24289 [03:37<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.0813, train_metrics/recon_loss_step=0.0813]
Epoch 0:   3%|▎         | 778/24289 [03:37<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.0813, train_metrics/recon_loss_step=0.0813]
Epoch 0:   3%|▎         | 778/24289 [03:37<1:49:35,  3.58it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]  
Epoch 0:   3%|▎         | 779/24289 [03:37<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   3%|▎         | 779/24289 [03:37<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   3%|▎         | 780/24289 [03:38<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   3%|▎         | 780/24289 [03:38<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 781/24289 [03:38<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 781/24289 [03:38<1:49:34,  3.58it/s, v_num=zwmv, train_loss_step=0.0303, train_metrics/recon_loss_step=0.0303]
Epoch 0:   3%|▎         | 782/24289 [03:38<1:49:32,  3.58it/s, v_num=zwmv, train_loss_step=0.0303, train_metrics/recon_loss_step=0.0303]
Epoch 0:   3%|▎         | 782/24289 [03:38<1:49:32,  3.58it/s, v_num=zwmv, train_loss_step=0.0404, train_metrics/recon_loss_step=0.0404]
Epoch 0:   3%|▎         | 783/24289 [03:38<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.0404, train_metrics/recon_loss_step=0.0404]
Epoch 0:   3%|▎         | 783/24289 [03:38<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]  
Epoch 0:   3%|▎         | 784/24289 [03:39<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   3%|▎         | 784/24289 [03:39<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   3%|▎         | 785/24289 [03:39<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   3%|▎         | 785/24289 [03:39<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   3%|▎         | 786/24289 [03:39<1:49:35,  3.57it/s, v_num=zwmv, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   3%|▎         | 786/24289 [03:39<1:49:35,  3.57it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 787/24289 [03:40<1:49:35,  3.57it/s, v_num=zwmv, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   3%|▎         | 787/24289 [03:40<1:49:35,  3.57it/s, v_num=zwmv, train_loss_step=0.0683, train_metrics/recon_loss_step=0.0683]
Epoch 0:   3%|▎         | 788/24289 [03:40<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.0683, train_metrics/recon_loss_step=0.0683]
Epoch 0:   3%|▎         | 788/24289 [03:40<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]  
Epoch 0:   3%|▎         | 789/24289 [03:40<1:49:32,  3.58it/s, v_num=zwmv, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   3%|▎         | 789/24289 [03:40<1:49:32,  3.58it/s, v_num=zwmv, train_loss_step=0.0716, train_metrics/recon_loss_step=0.0716]
Epoch 0:   3%|▎         | 790/24289 [03:41<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.0716, train_metrics/recon_loss_step=0.0716]
Epoch 0:   3%|▎         | 790/24289 [03:41<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]  
Epoch 0:   3%|▎         | 791/24289 [03:41<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.123, train_metrics/recon_loss_step=0.123]
Epoch 0:   3%|▎         | 791/24289 [03:41<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   3%|▎         | 792/24289 [03:41<1:49:31,  3.58it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   3%|▎         | 792/24289 [03:41<1:49:31,  3.58it/s, v_num=zwmv, train_loss_step=0.0775, train_metrics/recon_loss_step=0.0775]
Epoch 0:   3%|▎         | 793/24289 [03:41<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.0775, train_metrics/recon_loss_step=0.0775]
Epoch 0:   3%|▎         | 793/24289 [03:41<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]  
Epoch 0:   3%|▎         | 794/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.177, train_metrics/recon_loss_step=0.177]
Epoch 0:   3%|▎         | 794/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]
Epoch 0:   3%|▎         | 795/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]
Epoch 0:   3%|▎         | 795/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.0839, train_metrics/recon_loss_step=0.0839]
Epoch 0:   3%|▎         | 796/24289 [03:42<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.0839, train_metrics/recon_loss_step=0.0839]
Epoch 0:   3%|▎         | 796/24289 [03:42<1:49:30,  3.58it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]  
Epoch 0:   3%|▎         | 797/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   3%|▎         | 797/24289 [03:42<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.0826, train_metrics/recon_loss_step=0.0826]
Epoch 0:   3%|▎         | 798/24289 [03:43<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.0826, train_metrics/recon_loss_step=0.0826]
Epoch 0:   3%|▎         | 798/24289 [03:43<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.0956, train_metrics/recon_loss_step=0.0956]
Epoch 0:   3%|▎         | 799/24289 [03:43<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0956, train_metrics/recon_loss_step=0.0956]
Epoch 0:   3%|▎         | 799/24289 [03:43<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.076, train_metrics/recon_loss_step=0.076]  
Epoch 0:   3%|▎         | 800/24289 [03:43<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.076, train_metrics/recon_loss_step=0.076]
Epoch 0:   3%|▎         | 800/24289 [03:43<1:49:32,  3.57it/s, v_num=zwmv, train_loss_step=0.070, train_metrics/recon_loss_step=0.070]
Epoch 0:   3%|▎         | 801/24289 [03:44<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.070, train_metrics/recon_loss_step=0.070]
Epoch 0:   3%|▎         | 801/24289 [03:44<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]
Epoch 0:   3%|▎         | 802/24289 [03:44<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.222, train_metrics/recon_loss_step=0.222]
Epoch 0:   3%|▎         | 802/24289 [03:44<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.343, train_metrics/recon_loss_step=0.343]
Epoch 0:   3%|▎         | 803/24289 [03:44<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.343, train_metrics/recon_loss_step=0.343]
Epoch 0:   3%|▎         | 803/24289 [03:44<1:49:34,  3.57it/s, v_num=zwmv, train_loss_step=0.205, train_metrics/recon_loss_step=0.205]
Epoch 0:   3%|▎         | 804/24289 [03:45<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.205, train_metrics/recon_loss_step=0.205]
Epoch 0:   3%|▎         | 804/24289 [03:45<1:49:33,  3.57it/s, v_num=zwmv, train_loss_step=0.0706, train_metrics/recon_loss_step=0.0706]
Epoch 0:   3%|▎         | 805/24289 [03:45<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0706, train_metrics/recon_loss_step=0.0706]
Epoch 0:   3%|▎         | 805/24289 [03:45<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0954, train_metrics/recon_loss_step=0.0954]
Epoch 0:   3%|▎         | 806/24289 [03:45<1:49:30,  3.57it/s, v_num=zwmv, train_loss_step=0.0954, train_metrics/recon_loss_step=0.0954]
Epoch 0:   3%|▎         | 806/24289 [03:45<1:49:30,  3.57it/s, v_num=zwmv, train_loss_step=0.0956, train_metrics/recon_loss_step=0.0956]
Epoch 0:   3%|▎         | 807/24289 [03:45<1:49:28,  3.57it/s, v_num=zwmv, train_loss_step=0.0956, train_metrics/recon_loss_step=0.0956]
Epoch 0:   3%|▎         | 807/24289 [03:45<1:49:28,  3.57it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]  
Epoch 0:   3%|▎         | 808/24289 [03:46<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   3%|▎         | 808/24289 [03:46<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0228, train_metrics/recon_loss_step=0.0228]
Epoch 0:   3%|▎         | 809/24289 [03:46<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0228, train_metrics/recon_loss_step=0.0228]
Epoch 0:   3%|▎         | 809/24289 [03:46<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]  
Epoch 0:   3%|▎         | 810/24289 [03:46<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.137, train_metrics/recon_loss_step=0.137]
Epoch 0:   3%|▎         | 810/24289 [03:46<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.0509, train_metrics/recon_loss_step=0.0509]
Epoch 0:   3%|▎         | 811/24289 [03:46<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.0509, train_metrics/recon_loss_step=0.0509]
Epoch 0:   3%|▎         | 811/24289 [03:46<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]  
Epoch 0:   3%|▎         | 812/24289 [03:47<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   3%|▎         | 812/24289 [03:47<1:49:31,  3.57it/s, v_num=zwmv, train_loss_step=0.0271, train_metrics/recon_loss_step=0.0271]
Epoch 0:   3%|▎         | 813/24289 [03:47<1:49:30,  3.57it/s, v_num=zwmv, train_loss_step=0.0271, train_metrics/recon_loss_step=0.0271]
Epoch 0:   3%|▎         | 813/24289 [03:47<1:49:30,  3.57it/s, v_num=zwmv, train_loss_step=0.0744, train_metrics/recon_loss_step=0.0744]
Epoch 0:   3%|▎         | 814/24289 [03:47<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.0744, train_metrics/recon_loss_step=0.0744]
Epoch 0:   3%|▎         | 814/24289 [03:47<1:49:29,  3.57it/s, v_num=zwmv, train_loss_step=0.0623, train_metrics/recon_loss_step=0.0623]
Epoch 0:   3%|▎         | 815/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.0623, train_metrics/recon_loss_step=0.0623]
Epoch 0:   3%|▎         | 815/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]  
Epoch 0:   3%|▎         | 816/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   3%|▎         | 816/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   3%|▎         | 817/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.158, train_metrics/recon_loss_step=0.158]
Epoch 0:   3%|▎         | 817/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]
Epoch 0:   3%|▎         | 818/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]
Epoch 0:   3%|▎         | 818/24289 [03:48<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 819/24289 [03:49<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   3%|▎         | 819/24289 [03:49<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   3%|▎         | 820/24289 [03:49<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.117, train_metrics/recon_loss_step=0.117]
Epoch 0:   3%|▎         | 820/24289 [03:49<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.0859, train_metrics/recon_loss_step=0.0859]
Epoch 0:   3%|▎         | 821/24289 [03:49<1:49:24,  3.58it/s, v_num=zwmv, train_loss_step=0.0859, train_metrics/recon_loss_step=0.0859]
Epoch 0:   3%|▎         | 821/24289 [03:49<1:49:24,  3.57it/s, v_num=zwmv, train_loss_step=0.0698, train_metrics/recon_loss_step=0.0698]
Epoch 0:   3%|▎         | 822/24289 [03:49<1:49:23,  3.58it/s, v_num=zwmv, train_loss_step=0.0698, train_metrics/recon_loss_step=0.0698]
Epoch 0:   3%|▎         | 822/24289 [03:49<1:49:23,  3.58it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]  
Epoch 0:   3%|▎         | 823/24289 [03:50<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   3%|▎         | 823/24289 [03:50<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.0736, train_metrics/recon_loss_step=0.0736]
Epoch 0:   3%|▎         | 824/24289 [03:50<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.0736, train_metrics/recon_loss_step=0.0736]
Epoch 0:   3%|▎         | 824/24289 [03:50<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]  
Epoch 0:   3%|▎         | 825/24289 [03:50<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   3%|▎         | 825/24289 [03:50<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.0228, train_metrics/recon_loss_step=0.0228]
Epoch 0:   3%|▎         | 826/24289 [03:51<1:49:23,  3.57it/s, v_num=zwmv, train_loss_step=0.0228, train_metrics/recon_loss_step=0.0228]
Epoch 0:   3%|▎         | 826/24289 [03:51<1:49:23,  3.57it/s, v_num=zwmv, train_loss_step=0.0857, train_metrics/recon_loss_step=0.0857]
Epoch 0:   3%|▎         | 827/24289 [03:51<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.0857, train_metrics/recon_loss_step=0.0857]
Epoch 0:   3%|▎         | 827/24289 [03:51<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.0283, train_metrics/recon_loss_step=0.0283]
Epoch 0:   3%|▎         | 828/24289 [03:51<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.0283, train_metrics/recon_loss_step=0.0283]
Epoch 0:   3%|▎         | 828/24289 [03:51<1:49:22,  3.58it/s, v_num=zwmv, train_loss_step=0.082, train_metrics/recon_loss_step=0.082]  
Epoch 0:   3%|▎         | 829/24289 [03:51<1:49:20,  3.58it/s, v_num=zwmv, train_loss_step=0.082, train_metrics/recon_loss_step=0.082]
Epoch 0:   3%|▎         | 829/24289 [03:51<1:49:20,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 830/24289 [03:52<1:49:19,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   3%|▎         | 830/24289 [03:52<1:49:19,  3.58it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   3%|▎         | 831/24289 [03:52<1:49:19,  3.58it/s, v_num=zwmv, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   3%|▎         | 831/24289 [03:52<1:49:19,  3.58it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   3%|▎         | 832/24289 [03:52<1:49:21,  3.57it/s, v_num=zwmv, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   3%|▎         | 832/24289 [03:52<1:49:21,  3.57it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   3%|▎         | 833/24289 [03:53<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   3%|▎         | 833/24289 [03:53<1:49:27,  3.57it/s, v_num=zwmv, train_loss_step=0.0544, train_metrics/recon_loss_step=0.0544]
Epoch 0:   3%|▎         | 834/24289 [03:53<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.0544, train_metrics/recon_loss_step=0.0544]
Epoch 0:   3%|▎         | 834/24289 [03:53<1:49:25,  3.57it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]  
Epoch 0:   3%|▎         | 835/24289 [03:53<1:49:24,  3.57it/s, v_num=zwmv, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   3%|▎         | 835/24289 [03:53<1:49:24,  3.57it/s, v_num=zwmv, train_loss_step=0.0855, train_metrics/recon_loss_step=0.0855]
Epoch 0:   3%|▎         | 836/24289 [03:53<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.0855, train_metrics/recon_loss_step=0.0855]
Epoch 0:   3%|▎         | 836/24289 [03:53<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]  
Epoch 0:   3%|▎         | 837/24289 [03:54<1:49:20,  3.57it/s, v_num=zwmv, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   3%|▎         | 837/24289 [03:54<1:49:20,  3.57it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   3%|▎         | 838/24289 [03:54<1:49:23,  3.57it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   3%|▎         | 838/24289 [03:54<1:49:23,  3.57it/s, v_num=zwmv, train_loss_step=0.0162, train_metrics/recon_loss_step=0.0162]
Epoch 0:   3%|▎         | 839/24289 [03:54<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.0162, train_metrics/recon_loss_step=0.0162]
Epoch 0:   3%|▎         | 839/24289 [03:54<1:49:22,  3.57it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]  
Epoch 0:   3%|▎         | 840/24289 [03:55<1:49:21,  3.57it/s, v_num=zwmv, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   3%|▎         | 840/24289 [03:55<1:49:21,  3.57it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   3%|▎         | 841/24289 [03:55<1:49:20,  3.57it/s, v_num=zwmv, train_loss_step=0.145, train_metrics/recon_loss_step=0.145]
Epoch 0:   3%|▎         | 841/24289 [03:55<1:49:20,  3.57it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]
Epoch 0:   3%|▎         | 842/24289 [03:55<1:49:18,  3.58it/s, v_num=zwmv, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]
Epoch 0:   3%|▎         | 842/24289 [03:55<1:49:18,  3.58it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   3%|▎         | 843/24289 [03:55<1:49:17,  3.58it/s, v_num=zwmv, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   3%|▎         | 843/24289 [03:55<1:49:17,  3.58it/s, v_num=zwmv, train_loss_step=0.260, train_metrics/recon_loss_step=0.260]
Epoch 0:   3%|▎         | 844/24289 [03:56<1:49:17,  3.58it/s, v_num=zwmv, train_loss_step=0.260, train_metrics/recon_loss_step=0.260]
Epoch 0:   3%|▎         | 844/24289 [03:56<1:49:17,  3.58it/s, v_num=zwmv, train_loss_step=0.0725, train_metrics/recon_loss_step=0.0725]
Epoch 0:   3%|▎         | 845/24289 [03:56<1:49:16,  3.58it/s, v_num=zwmv, train_loss_step=0.0725, train_metrics/recon_loss_step=0.0725]
Epoch 0:   3%|▎         | 845/24289 [03:56<1:49:16,  3.58it/s, v_num=zwmv, train_loss_step=0.0721, train_metrics/recon_loss_step=0.0721]
Epoch 0:   3%|▎         | 846/24289 [03:56<1:49:15,  3.58it/s, v_num=zwmv, train_loss_step=0.0721, train_metrics/recon_loss_step=0.0721]
Epoch 0:   3%|▎         | 846/24289 [03:56<1:49:15,  3.58it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]  
Epoch 0:   3%|▎         | 847/24289 [03:56<1:49:13,  3.58it/s, v_num=zwmv, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   3%|▎         | 847/24289 [03:56<1:49:13,  3.58it/s, v_num=zwmv, train_loss_step=0.085, train_metrics/recon_loss_step=0.085]
Epoch 0:   3%|▎         | 848/24289 [03:57<1:49:14,  3.58it/s, v_num=zwmv, train_loss_step=0.085, train_metrics/recon_loss_step=0.085]
Epoch 0:   3%|▎         | 848/24289 [03:57<1:49:14,  3.58it/s, v_num=zwmv, train_loss_step=0.0856, train_metrics/recon_loss_step=0.0856]
Epoch 0:   3%|▎         | 849/24289 [03:57<1:49:12,  3.58it/s, v_num=zwmv, train_loss_step=0.0856, train_metrics/recon_loss_step=0.0856]
Epoch 0:   3%|▎         | 849/24289 [03:57<1:49:12,  3.58it/s, v_num=zwmv, train_loss_step=0.0953, train_metrics/recon_loss_step=0.0953]
Epoch 0:   3%|▎         | 850/24289 [03:57<1:49:10,  3.58it/s, v_num=zwmv, train_loss_step=0.0953, train_metrics/recon_loss_step=0.0953]
Epoch 0:   3%|▎         | 850/24289 [03:57<1:49:10,  3.58it/s, v_num=zwmv, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]
Epoch 0:   4%|▎         | 851/24289 [03:57<1:49:09,  3.58it/s, v_num=zwmv, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]
Epoch 0:   4%|▎         | 851/24289 [03:57<1:49:09,  3.58it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]  
Epoch 0:   4%|▎         | 852/24289 [03:58<1:49:11,  3.58it/s, v_num=zwmv, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   4%|▎         | 852/24289 [03:58<1:49:11,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   4%|▎         | 853/24289 [03:58<1:49:09,  3.58it/s, v_num=zwmv, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   4%|▎         | 853/24289 [03:58<1:49:09,  3.58it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   4%|▎         | 854/24289 [03:58<1:49:08,  3.58it/s, v_num=zwmv, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   4%|▎         | 854/24289 [03:58<1:49:08,  3.58it/s, v_num=zwmv, train_loss_step=0.0947, train_metrics/recon_loss_step=0.0947]
Epoch 0:   4%|▎         | 855/24289 [03:58<1:49:07,  3.58it/s, v_num=zwmv, train_loss_step=0.0947, train_metrics/recon_loss_step=0.0947]
Epoch 0:   4%|▎         | 855/24289 [03:58<1:49:07,  3.58it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]  
Epoch 0:   4%|▎         | 856/24289 [03:59<1:49:07,  3.58it/s, v_num=zwmv, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   4%|▎         | 856/24289 [03:59<1:49:07,  3.58it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   4%|▎         | 857/24289 [03:59<1:49:05,  3.58it/s, v_num=zwmv, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   4%|▎         | 857/24289 [03:59<1:49:05,  3.58it/s, v_num=zwmv, train_loss_step=0.0773, train_metrics/recon_loss_step=0.0773]
Epoch 0:   4%|▎         | 858/24289 [03:59<1:49:04,  3.58it/s, v_num=zwmv, train_loss_step=0.0773, train_metrics/recon_loss_step=0.0773]
Epoch 0:   4%|▎         | 858/24289 [03:59<1:49:04,  3.58it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]  
Epoch 0:   4%|▎         | 859/24289 [03:59<1:49:04,  3.58it/s, v_num=zwmv, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   4%|▎         | 859/24289 [03:59<1:49:04,  3.58it/s, v_num=zwmv, train_loss_step=0.143, train_metrics/recon_loss_step=0.143]
Epoch 0:   4%|▎         | 860/24289 [04:00<1:49:03,  3.58it/s, v_num=zwmv, train_loss_step=0.143, train_metrics/recon