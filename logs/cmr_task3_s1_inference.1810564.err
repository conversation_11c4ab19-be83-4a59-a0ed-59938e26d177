usage: main.py [options] predict [-h] [-c CONFIG] [--print_config [=flags]]
                                 [--seed_everything SEED_EVERYTHING]
                                 [--trainer CONFIG]
                                 [--trainer.accelerator.help CLASS_PATH_OR_NAME]
                                 [--trainer.accelerator ACCELERATOR]
                                 [--trainer.strategy.help CLASS_PATH_OR_NAME]
                                 [--trainer.strategy STRATEGY]
                                 [--trainer.devices DEVICES]
                                 [--trainer.num_nodes NUM_NODES]
                                 [--trainer.precision PRECISION]
                                 [--trainer.logger.help CLASS_PATH_OR_NAME]
                                 [--trainer.logger LOGGER]
                                 [--trainer.callbacks.help CLASS_PATH_OR_NAME]
                                 [--trainer.callbacks CALLBACKS]
                                 [--trainer.fast_dev_run FAST_DEV_RUN]
                                 [--trainer.max_epochs MAX_EPOCHS]
                                 [--trainer.min_epochs MIN_EPOCHS]
                                 [--trainer.max_steps MAX_STEPS]
                                 [--trainer.min_steps MIN_STEPS]
                                 [--trainer.max_time MAX_TIME]
                                 [--trainer.limit_train_batches LIMIT_TRAIN_BATCHES]
                                 [--trainer.limit_val_batches LIMIT_VAL_BATCHES]
                                 [--trainer.limit_test_batches LIMIT_TEST_BATCHES]
                                 [--trainer.limit_predict_batches LIMIT_PREDICT_BATCHES]
                                 [--trainer.overfit_batches OVERFIT_BATCHES]
                                 [--trainer.val_check_interval VAL_CHECK_INTERVAL]
                                 [--trainer.check_val_every_n_epoch CHECK_VAL_EVERY_N_EPOCH]
                                 [--trainer.num_sanity_val_steps NUM_SANITY_VAL_STEPS]
                                 [--trainer.log_every_n_steps LOG_EVERY_N_STEPS]
                                 [--trainer.enable_checkpointing {true,false,null}]
                                 [--trainer.enable_progress_bar {true,false,null}]
                                 [--trainer.enable_model_summary {true,false,null}]
                                 [--trainer.accumulate_grad_batches ACCUMULATE_GRAD_BATCHES]
                                 [--trainer.gradient_clip_val GRADIENT_CLIP_VAL]
                                 [--trainer.gradient_clip_algorithm GRADIENT_CLIP_ALGORITHM]
                                 [--trainer.deterministic DETERMINISTIC]
                                 [--trainer.benchmark {true,false,null}]
                                 [--trainer.inference_mode {true,false}]
                                 [--trainer.use_distributed_sampler {true,false}]
                                 [--trainer.profiler.help CLASS_PATH_OR_NAME]
                                 [--trainer.profiler PROFILER]
                                 [--trainer.detect_anomaly {true,false}]
                                 [--trainer.barebones {true,false}]
                                 [--trainer.plugins.help CLASS_PATH_OR_NAME]
                                 [--trainer.plugins PLUGINS]
                                 [--trainer.sync_batchnorm {true,false}]
                                 [--trainer.reload_dataloaders_every_n_epochs RELOAD_DATALOADERS_EVERY_N_EPOCHS]
                                 [--trainer.default_root_dir DEFAULT_ROOT_DIR]
                                 [--trainer.model_registry MODEL_REGISTRY]
                                 [--model.help [CLASS_PATH_OR_NAME]]
                                 --model CONFIG | CLASS_PATH_OR_NAME | .INIT_ARG_NAME VALUE
                                 [--data.help [CLASS_PATH_OR_NAME]]
                                 [--data CONFIG | CLASS_PATH_OR_NAME | .INIT_ARG_NAME VALUE]
                                 [--optimizer.help [CLASS_PATH_OR_NAME]]
                                 [--optimizer CONFIG | CLASS_PATH_OR_NAME | .INIT_ARG_NAME VALUE]
                                 [--lr_scheduler.help CLASS_PATH_OR_NAME]
                                 [--lr_scheduler CONFIG | CLASS_PATH_OR_NAME | .INIT_ARG_NAME VALUE]
                                 [--return_predictions {true,false,null}]
                                 [--ckpt_path CKPT_PATH]
error: Parser key "data":
  Does not validate against any of the Union subtypes
  Subtypes: [<class 'NoneType'>, <class 'lightning.pytorch.core.datamodule.LightningDataModule'>]
  Errors:
    - Expected a <class 'NoneType'>
    - Problem with given class_path 'pl_modules.InferenceDataModule':
        Validation failed: Key 'pin_memory' is not expected
  Given value type: <class 'dict'>
  Given value: {'class_path': 'pl_modules.InferenceDataModule', 'init_args': {'slice_dataset': 'data.CmrxReconInferenceSliceDataset', 'data_path': '/common/lidxxlab/cmrchallenge/data/CMR2025/Validation/Task3-S1/TaskS1/MultiCoil', 'challenge': 'multicoil', 'test_transform': {'class_path': 'data.CmrxReconDataTransform', 'init_args': {'mask_func': None, 'uniform_resolution': None, 'use_seed': True, 'mask_type': 'cartesian_or_radial', 'test_num_low_frequencies': 16}}, 'num_adj_slices': 5, 'batch_size': 1, 'num_workers': 1, 'pin_memory': True, 'distributed_sampler': True, 'test_filter': {'class_path': 'data.FuncFilterString', 'init_args': {'filter_str': 'T2w/ValidationSet/UnderSample_TaskS1'}}}}
