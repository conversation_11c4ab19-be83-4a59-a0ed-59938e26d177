Python path: /home/<USER>/.conda/envs/promptmr/bin/python
CUDA available: True
=== IMPROVED DISCRIMINATOR TRAINING ===
Config: cmr25-cardiac-domain-adapt-improved.yaml
Total epochs: 10
Batch size per GPU: 2 (effective: 8)
Model: 6 cascades, 48 channels (full capacity)
Domain: 4 feature layers, 5 epochs pretrain, 0.85 accuracy threshold
Expected time: ~6-8 hours
Key improvements:
  - Better discriminator architecture
  - More feature layers [1,2,3,4]
  - Higher accuracy threshold (0.85)
  - Longer pretraining (5 epochs)
  - Improved loss function
==========================
Starting improved discriminator training...
Train from scratch, no pretrain weights loaded
Configuration saved to logs/cmr2025_improved/goa0per6/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]KSPACE_DEBUG: masked_kspace shape: torch.Size([2, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3753653.2500, mean: 10442.4580
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_0.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_0.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_0.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_0.png
Cascade features length: 6
Feature 0 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
Feature 1 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
Feature 2 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
Feature 3 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
Feature 4 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
Feature 5 type: <class 'torch.Tensor'>, shape: torch.Size([2, 50, 392, 152])
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mimproved_discriminator_1506[0m at: [34mhttps://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_improved/runs/goa0per6[0m
Improved discriminator training complete!
Check wandb project: cmr2025_improved
Key metrics to monitor:
  - discriminator_accuracy (should reach >0.85 in pretrain phase)
  - training_phase (should switch to adversarial after pretrain)
  - domain_loss (should be stable)
  - field_strength_error (should decrease)
  - logit statistics (should be reasonable)
