wandb: Appending key for api.wandb.ai to your netrc file: /home/<USER>/.netrc
wandb: W&B API key is configured. Use `wandb login --relogin` to force relogin
[rank: 0] Seed set to 42
GPU available: True (cuda), used: True
TPU available: False, using: 0 TPU cores
HPU available: False, using: 0 HPUs
You are using a CUDA device ('NVIDIA L40S') that has Tensor Cores. To properly utilize them, you should set `torch.set_float32_matmul_precision('medium' | 'high')` which will trade-off precision for performance. For more details, read https://pytorch.org/docs/stable/generated/torch.set_float32_matmul_precision.html#torch.set_float32_matmul_precision
[rank: 0] Seed set to 42
Initializing distributed: GLOBAL_RANK: 0, MEMBER: 1/1
----------------------------------------------------------------------------------------------------
distributed_backend=nccl
All distributed processes registered. Starting with 1 processes
----------------------------------------------------------------------------------------------------

wandb: Using wandb-core as the SDK backend.  Please refer to https://wandb.me/wandb-core for more information.
wandb: Currently logged in as: lisha-zeng (lisha-zeng-cedars-sinai) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.19.9
wandb: Run data is saved locally in logs/wandb/run-20250621_162017-pqlr5ue5
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run fixed_domain_adapt_1614
wandb: ⭐️ View project at https://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_task3
wandb: 🚀 View run at https://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_task3/runs/pqlr5ue5
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                   | Params | Mode 
--------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum   | 0      | train
1 | SSIM             | DistributedMetricSum   | 0      | train
2 | PSNR             | DistributedMetricSum   | 0      | train
3 | ValLoss          | DistributedMetricSum   | 0      | train
4 | TotExamples      | DistributedMetricSum   | 0      | train
5 | TotSliceExamples | DistributedMetricSum   | 0      | train
6 | promptmr         | PromptMR               | 42.1 M | train
7 | loss             | SSIMLoss               | 0      | train
8 | domain_adapt     | DomainAdaptationModule | 2.8 M  | train
9 | domain_criterion | MSELoss                | 0      | train
--------------------------------------------------------------------
39.5 M    Trainable params
5.4 M     Non-trainable params
44.9 M    Total params
179.572   Total estimated model params size (MB)
SLURM auto-requeueing enabled. Setting signal handlers.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/data.py:78: Trying to infer the `batch_size` from an ambiguous collection. The batch size we found is 1. To avoid any miscalculations, use `self.log(..., batch_size=batch_size)`.
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/logger_connector/result.py:439: It is recommended to use `self.log('val_loss', ..., sync_dist=True)` when logging on epoch level in distributed setting to accumulate the metric across devices.
wandb: WARNING Tried to log to step 0 that is less than the current step 3. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 3. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 7. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
wandb: WARNING Tried to log to step 0 that is less than the current step 7. Steps must be monotonically increasing, so this data will be ignored. See https://wandb.me/define-metric to log data out of order.
srun: Job step aborted: Waiting up to 32 seconds for job step to finish.
slurmstepd: error: *** JOB 1811270 ON esplhpc-cp075 CANCELLED AT 2025-06-21T17:25:25 ***
slurmstepd: error: *** STEP 1811270.0 ON esplhpc-cp075 CANCELLED AT 2025-06-21T17:25:25 ***
[rank: 0] Received SIGTERM: 15
Bypassing SIGTERM: 15
