[rank: 0] Seed set to 42
/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/fabric/plugins/environments/slurm.py:204: The `srun` command is available on your system but is not used. HINT: If your intention is to run <PERSON> on SLURM, prepend your python command with `srun` like so: srun python main.py predict --config /common/lidxxlab/Yi/PromptM ...
You are using the plain ModelCheckpoint callback. Consider using LitModelCheckpoint which with seamless uploading to Model registry.
GPU available: True (cuda), used: True
TPU available: False, using: 0 TPU cores
HPU available: False, using: 0 HPUs
Initializing distributed: GLOBAL_RANK: 0, MEMBER: 1/1
----------------------------------------------------------------------------------------------------
distributed_backend=nccl
All distributed processes registered. Starting with 1 processes
----------------------------------------------------------------------------------------------------

Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=11-valvalidation_loss=0.0307.ckpt
/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/call.py:282: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: ["ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}", "ModelCheckpoint{'monitor': None, 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 10, 'train_time_interval': None}"].
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]
Loaded model weights from the checkpoint at /common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=11-valvalidation_loss=0.0307.ckpt
/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/torch/utils/data/dataloader.py:624: UserWarning: This DataLoader will create 4 worker processes in total. Our suggested max number of worker in current system is 1, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.
  warnings.warn(
[rank0]: Traceback (most recent call last):
[rank0]:   File "/common/lidxxlab/Yi/PromptMR-plus2/main.py", line 195, in <module>
[rank0]:     run_cli()
[rank0]:   File "/common/lidxxlab/Yi/PromptMR-plus2/main.py", line 189, in run_cli
[rank0]:     cli = CustomLightningCLI(
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/cli.py", line 398, in __init__
[rank0]:     self._run_subcommand(self.subcommand)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/cli.py", line 708, in _run_subcommand
[rank0]:     fn(**fn_kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/trainer.py", line 887, in predict
[rank0]:     return call._call_and_handle_interrupt(
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/call.py", line 47, in _call_and_handle_interrupt
[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
[rank0]:     return function(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/trainer.py", line 928, in _predict_impl
[rank0]:     results = self._run(model, ckpt_path=ckpt_path)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/trainer.py", line 1012, in _run
[rank0]:     results = self._run_stage()
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/trainer.py", line 1051, in _run_stage
[rank0]:     return self.predict_loop.run()
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/loops/utilities.py", line 179, in _decorator
[rank0]:     return loop_run(self, *args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/loops/prediction_loop.py", line 131, in run
[rank0]:     return self.on_run_end()
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/loops/prediction_loop.py", line 203, in on_run_end
[rank0]:     results = self._on_predict_epoch_end()
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/loops/prediction_loop.py", line 369, in _on_predict_epoch_end
[rank0]:     call._call_callback_hooks(trainer, "on_predict_epoch_end")
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/trainer/call.py", line 227, in _call_callback_hooks
[rank0]:     fn(trainer, trainer.lightning_module, *args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/lightning/pytorch/callbacks/prediction_writer.py", line 163, in on_predict_epoch_end
[rank0]:     self.write_on_epoch_end(trainer, pl_module, trainer.predict_loop.predictions, epoch_batch_indices)
[rank0]:   File "/common/lidxxlab/Yi/PromptMR-plus2/main.py", line 146, in write_on_epoch_end
[rank0]:     torch.distributed.all_gather_object(gathered, predictions)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/torch/distributed/c10d_logger.py", line 81, in wrapper
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/cmrrecon-task3/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py", line 3039, in all_gather_object
[rank0]:     coalesced_output_tensor = torch.empty(
[rank0]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 20.89 GiB. GPU 0 has a total capacity of 31.73 GiB of which 9.92 GiB is free. Including non-PyTorch memory, this process has 21.80 GiB memory in use. Of the allocated memory 21.25 GiB is allocated by PyTorch, and 64.30 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
