Python path: /home/<USER>/.conda/envs/promptmr/bin/python
CUDA available: True
=== FIXED DOMAIN ADAPTATION TRAINING ===
Config: cmr25-cardiac-domain-adapt-fixed.yaml
Total epochs: 50
Batch size per GPU: 1 (effective: 4)
Model: 6 cascades, 48 channels
Domain: Hierarchical feature processing (global/mid/local)
Discriminator: BCE_with_logits for mixed precision compatibility
Accuracy: Fixed calculation for single field strength batches
Loss function: binary_cross_entropy_with_logits (stable)
Learning rate: 0.0003
Layer analysis: ENABLED (real-time feature analysis)
Discriminator checkpoint: Will save when accuracy >= 0.65
Training time: 24 hours
Fixed issues:
  - Mixed precision compatibility (BCE_with_logits)
  - Correct accuracy calculation for single field strength batches
  - Hierarchical feature processing
  - Stable accuracy with sliding average
==========================
Starting fixed domain adaptation training...
Train from scratch, no pretrain weights loaded
Configuration saved to logs/cmr2025_task3/ow8oxhkp/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3753653.2500, mean: 10442.4580
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_0.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_0.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_0.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_0.png
Cascade features length: 6
Feature 0 type: <class 'list'>, shape: no shape
Feature 1 type: <class 'list'>, shape: no shape
Feature 2 type: <class 'list'>, shape: no shape
Feature 3 type: <class 'list'>, shape: no shape
Feature 4 type: <class 'list'>, shape: no shape
Feature 5 type: <class 'list'>, shape: no shape
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5

Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:01<00:01,  0.56it/s]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 19])
  Layer 1: torch.Size([1, 96, 98, 38])
  Layer 2: torch.Size([1, 72, 196, 76])
KSPACE_DEBUG: masked_kspace shape: torch.Size([1, 50, 386, 149, 2])
KSPACE_DEBUG: kspace_mag_np shape: (386, 149)
KSPACE_DEBUG: kspace_mag_np stats - min: 0.0000, max: 3418105.5000, mean: 12657.9951
KSPACE_DEBUG: Saved raw k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_1.npy
KSPACE_DEBUG: Saved k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/kspace_viz_1.png
MASK_DEBUG: No mask available, using original k-space
MASK_DEBUG: Saved masked k-space data to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_1.npy
MASK_DEBUG: Saved masked k-space visualization to /home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug/masked_kspace_viz_1.png
Cascade features length: 6
Feature 0 type: <class 'list'>, shape: no shape
Feature 1 type: <class 'list'>, shape: no shape
Feature 2 type: <class 'list'>, shape: no shape
Feature 3 type: <class 'list'>, shape: no shape
Feature 4 type: <class 'list'>, shape: no shape
Feature 5 type: <class 'list'>, shape: no shape
DEBUG: Parsing field strength from path: Center005_UIH_15T_umr670_P001_T1map.h5
DEBUG: Found 1.5T pattern in Center005_UIH_15T_umr670_P001_T1map.h5

Sanity Checking DataLoader 0: 100%|██████████| 2/2 [00:02<00:00,  0.69it/s]
                                                                           

Training: |          | 0/? [00:00<?, ?it/s]
Training:   0%|          | 0/24289 [00:00<?, ?it/s]
Epoch 0:   0%|          | 0/24289 [00:00<?, ?it/s] 🔄 Epoch 0 starting - Phase: discriminator_pretrain, Alpha: 0.0000
DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
Performing one-time verification of domain adaptation setup...

--- Domain Adaptation Verification ---
Feature extract layers: [1, 3, 5]
Number of cascade features available: 6

Verifying feature extraction layers:
  - Layer 1: list with 3 tensors
    Tensor 0: shape=torch.Size([1, 120, 52, 15]), dtype=torch.float32, device=cuda:0
    Tensor 1: shape=torch.Size([1, 96, 104, 30]), dtype=torch.float32, device=cuda:0
    Tensor 2: shape=torch.Size([1, 72, 208, 60]), dtype=torch.float32, device=cuda:0
  - Layer 3: list with 3 tensors
    Tensor 0: shape=torch.Size([1, 120, 52, 15]), dtype=torch.float32, device=cuda:0
    Tensor 1: shape=torch.Size([1, 96, 104, 30]), dtype=torch.float32, device=cuda:0
    Tensor 2: shape=torch.Size([1, 72, 208, 60]), dtype=torch.float32, device=cuda:0
  - Layer 5: list with 3 tensors
    Tensor 0: shape=torch.Size([1, 120, 52, 15]), dtype=torch.float32, device=cuda:0
    Tensor 1: shape=torch.Size([1, 96, 104, 30]), dtype=torch.float32, device=cuda:0
    Tensor 2: shape=torch.Size([1, 72, 208, 60]), dtype=torch.float32, device=cuda:0

Verifying compressors:
  - Compressor 0: in_channels=72
  - Compressor 1: in_channels=72
  - Compressor 2: in_channels=72
--- End Verification ---


🔍 LAYER ANALYSIS at step 0

============================================================
REAL-TIME LAYER ANALYSIS
============================================================

Layer Analysis (based on 1 samples):
Layer    Samples  Mean       Std        Range      Var       
------------------------------------------------------------
0        <USER>        <GROUP>.0102    0.2404     7.5437     0.0044    
1        3        0.0010     0.2058     7.1550     0.0138    
2        3        0.0031     0.2277     8.7878     0.0020    
3        3        0.0165     0.2189     8.2203     0.0100    
4        3        -0.0021    0.2404     8.2040     0.0082    
5        3        -0.0054    0.2315     8.2907     0.0042    

Layer Ranking (by discriminative power):
  1. Layer 1: 0.098411
  2. Layer 3: 0.081874
  3. Layer 4: 0.067140
  4. Layer 5: 0.034885
  5. Layer 0: 0.033004
  6. Layer 2: 0.017386

Recommended feature_extract_layers:
  Top 3: [1, 3, 4]
  Top 4: [1, 3, 4, 5]
  All: [1, 3, 4, 5, 0, 2]
============================================================
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 0
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 1/24289 [00:01<7:34:35,  0.89it/s]
Epoch 0:   0%|          | 1/24289 [00:01<7:34:50,  0.89it/s, v_num=xhkp, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 1
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 2/24289 [00:01<4:49:34,  1.40it/s, v_num=xhkp, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]
Epoch 0:   0%|          | 2/24289 [00:01<4:49:41,  1.40it/s, v_num=xhkp, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 2
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 3/24289 [00:01<3:44:26,  1.80it/s, v_num=xhkp, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]
Epoch 0:   0%|          | 3/24289 [00:01<3:44:30,  1.80it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 3
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 4/24289 [00:01<3:20:27,  2.02it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]
Epoch 0:   0%|          | 4/24289 [00:01<3:20:30,  2.02it/s, v_num=xhkp, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 4
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 5/24289 [00:02<2:59:41,  2.25it/s, v_num=xhkp, train_loss_step=0.163, train_metrics/recon_loss_step=0.163]
Epoch 0:   0%|          | 5/24289 [00:02<2:59:43,  2.25it/s, v_num=xhkp, train_loss_step=0.179, train_metrics/recon_loss_step=0.179]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 5
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 6/24289 [00:02<2:53:23,  2.33it/s, v_num=xhkp, train_loss_step=0.179, train_metrics/recon_loss_step=0.179]
Epoch 0:   0%|          | 6/24289 [00:02<2:53:25,  2.33it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 6
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 7/24289 [00:03<2:53:37,  2.33it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]
Epoch 0:   0%|          | 7/24289 [00:03<2:53:40,  2.33it/s, v_num=xhkp, train_loss_step=0.311, train_metrics/recon_loss_step=0.311]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 7
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 8/24289 [00:03<2:43:45,  2.47it/s, v_num=xhkp, train_loss_step=0.311, train_metrics/recon_loss_step=0.311]
Epoch 0:   0%|          | 8/24289 [00:03<2:43:47,  2.47it/s, v_num=xhkp, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 8
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 9/24289 [00:03<2:36:45,  2.58it/s, v_num=xhkp, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]
Epoch 0:   0%|          | 9/24289 [00:03<2:36:47,  2.58it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 9
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 10/24289 [00:03<2:40:29,  2.52it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   0%|          | 10/24289 [00:03<2:40:30,  2.52it/s, v_num=xhkp, train_loss_step=0.0675, train_metrics/recon_loss_step=0.0675]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 10
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 11/24289 [00:04<2:35:15,  2.61it/s, v_num=xhkp, train_loss_step=0.0675, train_metrics/recon_loss_step=0.0675]
Epoch 0:   0%|          | 11/24289 [00:04<2:35:16,  2.61it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 11
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 12/24289 [00:04<2:38:14,  2.56it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   0%|          | 12/24289 [00:04<2:38:15,  2.56it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 12
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 13/24289 [00:04<2:34:34,  2.62it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   0%|          | 13/24289 [00:04<2:34:35,  2.62it/s, v_num=xhkp, train_loss_step=0.270, train_metrics/recon_loss_step=0.270]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 13
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 14/24289 [00:05<2:38:44,  2.55it/s, v_num=xhkp, train_loss_step=0.270, train_metrics/recon_loss_step=0.270]
Epoch 0:   0%|          | 14/24289 [00:05<2:38:45,  2.55it/s, v_num=xhkp, train_loss_step=0.244, train_metrics/recon_loss_step=0.244]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 14
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 15/24289 [00:05<2:37:20,  2.57it/s, v_num=xhkp, train_loss_step=0.244, train_metrics/recon_loss_step=0.244]
Epoch 0:   0%|          | 15/24289 [00:05<2:37:21,  2.57it/s, v_num=xhkp, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 15
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 16/24289 [00:06<2:33:25,  2.64it/s, v_num=xhkp, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]
Epoch 0:   0%|          | 16/24289 [00:06<2:33:26,  2.64it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 16
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 17/24289 [00:06<2:32:00,  2.66it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   0%|          | 17/24289 [00:06<2:32:00,  2.66it/s, v_num=xhkp, train_loss_step=0.306, train_metrics/recon_loss_step=0.306]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 17
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 18/24289 [00:06<2:32:30,  2.65it/s, v_num=xhkp, train_loss_step=0.306, train_metrics/recon_loss_step=0.306]
Epoch 0:   0%|          | 18/24289 [00:06<2:32:31,  2.65it/s, v_num=xhkp, train_loss_step=0.0173, train_metrics/recon_loss_step=0.0173]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 18
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 19/24289 [00:07<2:32:34,  2.65it/s, v_num=xhkp, train_loss_step=0.0173, train_metrics/recon_loss_step=0.0173]
Epoch 0:   0%|          | 19/24289 [00:07<2:32:35,  2.65it/s, v_num=xhkp, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 19
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 20/24289 [00:07<2:32:09,  2.66it/s, v_num=xhkp, train_loss_step=0.281, train_metrics/recon_loss_step=0.281]
Epoch 0:   0%|          | 20/24289 [00:07<2:32:09,  2.66it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 20
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 21/24289 [00:07<2:31:38,  2.67it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   0%|          | 21/24289 [00:07<2:31:38,  2.67it/s, v_num=xhkp, train_loss_step=0.238, train_metrics/recon_loss_step=0.238]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 21
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 22/24289 [00:08<2:32:24,  2.65it/s, v_num=xhkp, train_loss_step=0.238, train_metrics/recon_loss_step=0.238]
Epoch 0:   0%|          | 22/24289 [00:08<2:32:25,  2.65it/s, v_num=xhkp, train_loss_step=0.0617, train_metrics/recon_loss_step=0.0617]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 22
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 23/24289 [00:08<2:31:48,  2.66it/s, v_num=xhkp, train_loss_step=0.0617, train_metrics/recon_loss_step=0.0617]
Epoch 0:   0%|          | 23/24289 [00:08<2:31:49,  2.66it/s, v_num=xhkp, train_loss_step=0.191, train_metrics/recon_loss_step=0.191]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 23
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 24/24289 [00:08<2:31:11,  2.67it/s, v_num=xhkp, train_loss_step=0.191, train_metrics/recon_loss_step=0.191]
Epoch 0:   0%|          | 24/24289 [00:08<2:31:11,  2.67it/s, v_num=xhkp, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 24
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 25/24289 [00:09<2:31:15,  2.67it/s, v_num=xhkp, train_loss_step=0.124, train_metrics/recon_loss_step=0.124]
Epoch 0:   0%|          | 25/24289 [00:09<2:31:16,  2.67it/s, v_num=xhkp, train_loss_step=0.0635, train_metrics/recon_loss_step=0.0635]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 25
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 26/24289 [00:09<2:29:15,  2.71it/s, v_num=xhkp, train_loss_step=0.0635, train_metrics/recon_loss_step=0.0635]
Epoch 0:   0%|          | 26/24289 [00:09<2:29:16,  2.71it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 63, 25])
  Layer 1: torch.Size([1, 96, 126, 50])
  Layer 2: torch.Size([1, 72, 252, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 26
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 27/24289 [00:10<2:29:51,  2.70it/s, v_num=xhkp, train_loss_step=0.251, train_metrics/recon_loss_step=0.251]
Epoch 0:   0%|          | 27/24289 [00:10<2:29:52,  2.70it/s, v_num=xhkp, train_loss_step=0.085, train_metrics/recon_loss_step=0.085]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 27
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 28/24289 [00:10<2:29:21,  2.71it/s, v_num=xhkp, train_loss_step=0.085, train_metrics/recon_loss_step=0.085]
Epoch 0:   0%|          | 28/24289 [00:10<2:29:22,  2.71it/s, v_num=xhkp, train_loss_step=0.264, train_metrics/recon_loss_step=0.264]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 28
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 29/24289 [00:10<2:28:19,  2.73it/s, v_num=xhkp, train_loss_step=0.264, train_metrics/recon_loss_step=0.264]
Epoch 0:   0%|          | 29/24289 [00:10<2:28:19,  2.73it/s, v_num=xhkp, train_loss_step=0.0958, train_metrics/recon_loss_step=0.0958]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 23])
  Layer 1: torch.Size([1, 96, 112, 46])
  Layer 2: torch.Size([1, 72, 224, 92])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 29
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 30/24289 [00:10<2:28:02,  2.73it/s, v_num=xhkp, train_loss_step=0.0958, train_metrics/recon_loss_step=0.0958]
Epoch 0:   0%|          | 30/24289 [00:10<2:28:02,  2.73it/s, v_num=xhkp, train_loss_step=0.228, train_metrics/recon_loss_step=0.228]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 30
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 31/24289 [00:11<2:26:16,  2.76it/s, v_num=xhkp, train_loss_step=0.228, train_metrics/recon_loss_step=0.228]
Epoch 0:   0%|          | 31/24289 [00:11<2:26:16,  2.76it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 31
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 32/24289 [00:11<2:26:40,  2.76it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]
Epoch 0:   0%|          | 32/24289 [00:11<2:26:41,  2.76it/s, v_num=xhkp, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 32
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 33/24289 [00:11<2:25:09,  2.79it/s, v_num=xhkp, train_loss_step=0.136, train_metrics/recon_loss_step=0.136]
Epoch 0:   0%|          | 33/24289 [00:11<2:25:09,  2.78it/s, v_num=xhkp, train_loss_step=0.194, train_metrics/recon_loss_step=0.194]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 33
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 34/24289 [00:12<2:23:51,  2.81it/s, v_num=xhkp, train_loss_step=0.194, train_metrics/recon_loss_step=0.194]
Epoch 0:   0%|          | 34/24289 [00:12<2:23:52,  2.81it/s, v_num=xhkp, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 34
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 35/24289 [00:12<2:22:59,  2.83it/s, v_num=xhkp, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   0%|          | 35/24289 [00:12<2:22:59,  2.83it/s, v_num=xhkp, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 35
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 36/24289 [00:12<2:21:52,  2.85it/s, v_num=xhkp, train_loss_step=0.149, train_metrics/recon_loss_step=0.149]
Epoch 0:   0%|          | 36/24289 [00:12<2:21:52,  2.85it/s, v_num=xhkp, train_loss_step=0.235, train_metrics/recon_loss_step=0.235]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 36
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 37/24289 [00:12<2:20:42,  2.87it/s, v_num=xhkp, train_loss_step=0.235, train_metrics/recon_loss_step=0.235]
Epoch 0:   0%|          | 37/24289 [00:12<2:20:42,  2.87it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 37
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 38/24289 [00:13<2:27:42,  2.74it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   0%|          | 38/24289 [00:13<2:27:43,  2.74it/s, v_num=xhkp, train_loss_step=0.0963, train_metrics/recon_loss_step=0.0963]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 38
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 39/24289 [00:14<2:27:39,  2.74it/s, v_num=xhkp, train_loss_step=0.0963, train_metrics/recon_loss_step=0.0963]
Epoch 0:   0%|          | 39/24289 [00:14<2:27:39,  2.74it/s, v_num=xhkp, train_loss_step=0.407, train_metrics/recon_loss_step=0.407]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 39
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 40/24289 [00:14<2:27:17,  2.74it/s, v_num=xhkp, train_loss_step=0.407, train_metrics/recon_loss_step=0.407]
Epoch 0:   0%|          | 40/24289 [00:14<2:27:17,  2.74it/s, v_num=xhkp, train_loss_step=0.0537, train_metrics/recon_loss_step=0.0537]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 40
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 41/24289 [00:14<2:26:32,  2.76it/s, v_num=xhkp, train_loss_step=0.0537, train_metrics/recon_loss_step=0.0537]
Epoch 0:   0%|          | 41/24289 [00:14<2:26:33,  2.76it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 41
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 42/24289 [00:15<2:25:32,  2.78it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   0%|          | 42/24289 [00:15<2:25:32,  2.78it/s, v_num=xhkp, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 42
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 43/24289 [00:15<2:26:09,  2.76it/s, v_num=xhkp, train_loss_step=0.305, train_metrics/recon_loss_step=0.305]
Epoch 0:   0%|          | 43/24289 [00:15<2:26:09,  2.76it/s, v_num=xhkp, train_loss_step=0.507, train_metrics/recon_loss_step=0.507]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 43
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 44/24289 [00:15<2:25:44,  2.77it/s, v_num=xhkp, train_loss_step=0.507, train_metrics/recon_loss_step=0.507]
Epoch 0:   0%|          | 44/24289 [00:15<2:25:45,  2.77it/s, v_num=xhkp, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 15])
  Layer 1: torch.Size([1, 96, 104, 30])
  Layer 2: torch.Size([1, 72, 208, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 44
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 45/24289 [00:16<2:24:47,  2.79it/s, v_num=xhkp, train_loss_step=0.103, train_metrics/recon_loss_step=0.103]
Epoch 0:   0%|          | 45/24289 [00:16<2:24:48,  2.79it/s, v_num=xhkp, train_loss_step=0.322, train_metrics/recon_loss_step=0.322]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 45
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 46/24289 [00:16<2:23:52,  2.81it/s, v_num=xhkp, train_loss_step=0.322, train_metrics/recon_loss_step=0.322]
Epoch 0:   0%|          | 46/24289 [00:16<2:23:53,  2.81it/s, v_num=xhkp, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 46
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 47/24289 [00:16<2:23:06,  2.82it/s, v_num=xhkp, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   0%|          | 47/24289 [00:16<2:23:06,  2.82it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 47
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 48/24289 [00:17<2:28:08,  2.73it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   0%|          | 48/24289 [00:17<2:28:09,  2.73it/s, v_num=xhkp, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 48
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 49/24289 [00:17<2:27:09,  2.75it/s, v_num=xhkp, train_loss_step=0.176, train_metrics/recon_loss_step=0.176]
Epoch 0:   0%|          | 49/24289 [00:17<2:27:09,  2.75it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 49
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 50/24289 [00:18<2:28:06,  2.73it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   0%|          | 50/24289 [00:18<2:28:06,  2.73it/s, v_num=xhkp, train_loss_step=0.339, train_metrics/recon_loss_step=0.339]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 50
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 51/24289 [00:18<2:29:40,  2.70it/s, v_num=xhkp, train_loss_step=0.339, train_metrics/recon_loss_step=0.339]
Epoch 0:   0%|          | 51/24289 [00:18<2:29:40,  2.70it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 51
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 52/24289 [00:19<2:28:41,  2.72it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   0%|          | 52/24289 [00:19<2:28:41,  2.72it/s, v_num=xhkp, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 52
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 53/24289 [00:19<2:29:04,  2.71it/s, v_num=xhkp, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   0%|          | 53/24289 [00:19<2:29:04,  2.71it/s, v_num=xhkp, train_loss_step=0.0238, train_metrics/recon_loss_step=0.0238]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 53
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 54/24289 [00:19<2:28:27,  2.72it/s, v_num=xhkp, train_loss_step=0.0238, train_metrics/recon_loss_step=0.0238]
Epoch 0:   0%|          | 54/24289 [00:19<2:28:27,  2.72it/s, v_num=xhkp, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 54
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 55/24289 [00:20<2:27:33,  2.74it/s, v_num=xhkp, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]
Epoch 0:   0%|          | 55/24289 [00:20<2:27:34,  2.74it/s, v_num=xhkp, train_loss_step=0.0894, train_metrics/recon_loss_step=0.0894]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 55
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 56/24289 [00:20<2:26:35,  2.76it/s, v_num=xhkp, train_loss_step=0.0894, train_metrics/recon_loss_step=0.0894]
Epoch 0:   0%|          | 56/24289 [00:20<2:26:35,  2.76it/s, v_num=xhkp, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 23])
  Layer 1: torch.Size([1, 96, 130, 46])
  Layer 2: torch.Size([1, 72, 260, 92])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 56
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 57/24289 [00:20<2:26:50,  2.75it/s, v_num=xhkp, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   0%|          | 57/24289 [00:20<2:26:50,  2.75it/s, v_num=xhkp, train_loss_step=0.0885, train_metrics/recon_loss_step=0.0885]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 57
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 58/24289 [00:20<2:26:09,  2.76it/s, v_num=xhkp, train_loss_step=0.0885, train_metrics/recon_loss_step=0.0885]
Epoch 0:   0%|          | 58/24289 [00:20<2:26:09,  2.76it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 58
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 59/24289 [00:21<2:26:22,  2.76it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   0%|          | 59/24289 [00:21<2:26:22,  2.76it/s, v_num=xhkp, train_loss_step=0.0172, train_metrics/recon_loss_step=0.0172]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 59
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 60/24289 [00:21<2:25:41,  2.77it/s, v_num=xhkp, train_loss_step=0.0172, train_metrics/recon_loss_step=0.0172]
Epoch 0:   0%|          | 60/24289 [00:21<2:25:42,  2.77it/s, v_num=xhkp, train_loss_step=0.268, train_metrics/recon_loss_step=0.268]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 60
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 61/24289 [00:21<2:24:58,  2.79it/s, v_num=xhkp, train_loss_step=0.268, train_metrics/recon_loss_step=0.268]
Epoch 0:   0%|          | 61/24289 [00:21<2:24:59,  2.79it/s, v_num=xhkp, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 61
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 62/24289 [00:22<2:24:08,  2.80it/s, v_num=xhkp, train_loss_step=0.182, train_metrics/recon_loss_step=0.182]
Epoch 0:   0%|          | 62/24289 [00:22<2:24:08,  2.80it/s, v_num=xhkp, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 22])
  Layer 1: torch.Size([1, 96, 130, 44])
  Layer 2: torch.Size([1, 72, 260, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 62
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 63/24289 [00:22<2:24:21,  2.80it/s, v_num=xhkp, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]
Epoch 0:   0%|          | 63/24289 [00:22<2:24:21,  2.80it/s, v_num=xhkp, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 63
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 64/24289 [00:22<2:24:22,  2.80it/s, v_num=xhkp, train_loss_step=0.107, train_metrics/recon_loss_step=0.107]
Epoch 0:   0%|          | 64/24289 [00:22<2:24:22,  2.80it/s, v_num=xhkp, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 64
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 65/24289 [00:23<2:24:46,  2.79it/s, v_num=xhkp, train_loss_step=0.184, train_metrics/recon_loss_step=0.184]
Epoch 0:   0%|          | 65/24289 [00:23<2:24:46,  2.79it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 65
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 66/24289 [00:23<2:24:01,  2.80it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   0%|          | 66/24289 [00:23<2:24:01,  2.80it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 66
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 67/24289 [00:23<2:23:25,  2.81it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   0%|          | 67/24289 [00:23<2:23:26,  2.81it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 22])
  Layer 1: torch.Size([1, 96, 112, 44])
  Layer 2: torch.Size([1, 72, 224, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 67
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 68/24289 [00:24<2:23:23,  2.82it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   0%|          | 68/24289 [00:24<2:23:23,  2.82it/s, v_num=xhkp, train_loss_step=0.214, train_metrics/recon_loss_step=0.214]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 68
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 69/24289 [00:24<2:22:41,  2.83it/s, v_num=xhkp, train_loss_step=0.214, train_metrics/recon_loss_step=0.214]
Epoch 0:   0%|          | 69/24289 [00:24<2:22:42,  2.83it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 69
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 70/24289 [00:24<2:22:16,  2.84it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]
Epoch 0:   0%|          | 70/24289 [00:24<2:22:16,  2.84it/s, v_num=xhkp, train_loss_step=0.418, train_metrics/recon_loss_step=0.418]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 70
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 71/24289 [00:24<2:21:35,  2.85it/s, v_num=xhkp, train_loss_step=0.418, train_metrics/recon_loss_step=0.418]
Epoch 0:   0%|          | 71/24289 [00:24<2:21:36,  2.85it/s, v_num=xhkp, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 71
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 72/24289 [00:25<2:23:10,  2.82it/s, v_num=xhkp, train_loss_step=0.119, train_metrics/recon_loss_step=0.119]
Epoch 0:   0%|          | 72/24289 [00:25<2:23:10,  2.82it/s, v_num=xhkp, train_loss_step=0.213, train_metrics/recon_loss_step=0.213]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 72
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 73/24289 [00:25<2:22:47,  2.83it/s, v_num=xhkp, train_loss_step=0.213, train_metrics/recon_loss_step=0.213]
Epoch 0:   0%|          | 73/24289 [00:25<2:22:47,  2.83it/s, v_num=xhkp, train_loss_step=0.360, train_metrics/recon_loss_step=0.360]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 73
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 74/24289 [00:26<2:22:17,  2.84it/s, v_num=xhkp, train_loss_step=0.360, train_metrics/recon_loss_step=0.360]
Epoch 0:   0%|          | 74/24289 [00:26<2:22:17,  2.84it/s, v_num=xhkp, train_loss_step=0.260, train_metrics/recon_loss_step=0.260]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 74
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 75/24289 [00:26<2:22:47,  2.83it/s, v_num=xhkp, train_loss_step=0.260, train_metrics/recon_loss_step=0.260]
Epoch 0:   0%|          | 75/24289 [00:26<2:22:47,  2.83it/s, v_num=xhkp, train_loss_step=0.202, train_metrics/recon_loss_step=0.202]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 75
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 76/24289 [00:26<2:22:14,  2.84it/s, v_num=xhkp, train_loss_step=0.202, train_metrics/recon_loss_step=0.202]
Epoch 0:   0%|          | 76/24289 [00:26<2:22:14,  2.84it/s, v_num=xhkp, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 76
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 77/24289 [00:27<2:21:42,  2.85it/s, v_num=xhkp, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   0%|          | 77/24289 [00:27<2:21:43,  2.85it/s, v_num=xhkp, train_loss_step=0.0814, train_metrics/recon_loss_step=0.0814]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 77
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 78/24289 [00:27<2:21:10,  2.86it/s, v_num=xhkp, train_loss_step=0.0814, train_metrics/recon_loss_step=0.0814]
Epoch 0:   0%|          | 78/24289 [00:27<2:21:11,  2.86it/s, v_num=xhkp, train_loss_step=0.0934, train_metrics/recon_loss_step=0.0934]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 78
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 79/24289 [00:27<2:21:32,  2.85it/s, v_num=xhkp, train_loss_step=0.0934, train_metrics/recon_loss_step=0.0934]
Epoch 0:   0%|          | 79/24289 [00:27<2:21:32,  2.85it/s, v_num=xhkp, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 79
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 80/24289 [00:28<2:21:27,  2.85it/s, v_num=xhkp, train_loss_step=0.166, train_metrics/recon_loss_step=0.166]
Epoch 0:   0%|          | 80/24289 [00:28<2:21:27,  2.85it/s, v_num=xhkp, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 80
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 81/24289 [00:28<2:21:40,  2.85it/s, v_num=xhkp, train_loss_step=0.216, train_metrics/recon_loss_step=0.216]
Epoch 0:   0%|          | 81/24289 [00:28<2:21:40,  2.85it/s, v_num=xhkp, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 81
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 82/24289 [00:28<2:21:44,  2.85it/s, v_num=xhkp, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]
Epoch 0:   0%|          | 82/24289 [00:28<2:21:45,  2.85it/s, v_num=xhkp, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 82
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 83/24289 [00:29<2:22:08,  2.84it/s, v_num=xhkp, train_loss_step=0.152, train_metrics/recon_loss_step=0.152]
Epoch 0:   0%|          | 83/24289 [00:29<2:22:08,  2.84it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 83
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 84/24289 [00:29<2:21:57,  2.84it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   0%|          | 84/24289 [00:29<2:21:57,  2.84it/s, v_num=xhkp, train_loss_step=0.243, train_metrics/recon_loss_step=0.243]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 84
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 85/24289 [00:29<2:21:28,  2.85it/s, v_num=xhkp, train_loss_step=0.243, train_metrics/recon_loss_step=0.243]
Epoch 0:   0%|          | 85/24289 [00:29<2:21:28,  2.85it/s, v_num=xhkp, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 85
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 86/24289 [00:30<2:21:31,  2.85it/s, v_num=xhkp, train_loss_step=0.193, train_metrics/recon_loss_step=0.193]
Epoch 0:   0%|          | 86/24289 [00:30<2:21:31,  2.85it/s, v_num=xhkp, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 86
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 87/24289 [00:30<2:22:05,  2.84it/s, v_num=xhkp, train_loss_step=0.248, train_metrics/recon_loss_step=0.248]
Epoch 0:   0%|          | 87/24289 [00:30<2:22:05,  2.84it/s, v_num=xhkp, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 87
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 88/24289 [00:30<2:21:49,  2.84it/s, v_num=xhkp, train_loss_step=0.200, train_metrics/recon_loss_step=0.200]
Epoch 0:   0%|          | 88/24289 [00:30<2:21:49,  2.84it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 38])
  Layer 1: torch.Size([1, 96, 112, 76])
  Layer 2: torch.Size([1, 72, 224, 152])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 88
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 89/24289 [00:31<2:22:13,  2.84it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   0%|          | 89/24289 [00:31<2:22:13,  2.84it/s, v_num=xhkp, train_loss_step=0.320, train_metrics/recon_loss_step=0.320]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 89
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 90/24289 [00:31<2:21:44,  2.85it/s, v_num=xhkp, train_loss_step=0.320, train_metrics/recon_loss_step=0.320]
Epoch 0:   0%|          | 90/24289 [00:31<2:21:44,  2.85it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 35])
  Layer 1: torch.Size([1, 96, 112, 70])
  Layer 2: torch.Size([1, 72, 224, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 90
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 91/24289 [00:32<2:22:03,  2.84it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   0%|          | 91/24289 [00:32<2:22:03,  2.84it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 91
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 92/24289 [00:32<2:22:14,  2.84it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]
Epoch 0:   0%|          | 92/24289 [00:32<2:22:14,  2.84it/s, v_num=xhkp, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 92
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 93/24289 [00:32<2:21:43,  2.85it/s, v_num=xhkp, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   0%|          | 93/24289 [00:32<2:21:43,  2.85it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 93
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 94/24289 [00:32<2:21:14,  2.85it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   0%|          | 94/24289 [00:32<2:21:14,  2.85it/s, v_num=xhkp, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 94
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 95/24289 [00:33<2:21:11,  2.86it/s, v_num=xhkp, train_loss_step=0.142, train_metrics/recon_loss_step=0.142]
Epoch 0:   0%|          | 95/24289 [00:33<2:21:11,  2.86it/s, v_num=xhkp, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 95
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 96/24289 [00:33<2:21:02,  2.86it/s, v_num=xhkp, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   0%|          | 96/24289 [00:33<2:21:02,  2.86it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 96
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 97/24289 [00:33<2:20:40,  2.87it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   0%|          | 97/24289 [00:33<2:20:40,  2.87it/s, v_num=xhkp, train_loss_step=0.242, train_metrics/recon_loss_step=0.242]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 25])
  Layer 1: torch.Size([1, 96, 112, 50])
  Layer 2: torch.Size([1, 72, 224, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 97
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 98/24289 [00:34<2:20:32,  2.87it/s, v_num=xhkp, train_loss_step=0.242, train_metrics/recon_loss_step=0.242]
Epoch 0:   0%|          | 98/24289 [00:34<2:20:33,  2.87it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 98
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 99/24289 [00:34<2:20:06,  2.88it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   0%|          | 99/24289 [00:34<2:20:06,  2.88it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 99
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 100/24289 [00:34<2:19:44,  2.89it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   0%|          | 100/24289 [00:34<2:19:44,  2.88it/s, v_num=xhkp, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 100
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 101/24289 [00:35<2:19:44,  2.88it/s, v_num=xhkp, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   0%|          | 101/24289 [00:35<2:19:44,  2.88it/s, v_num=xhkp, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 101
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 102/24289 [00:35<2:19:28,  2.89it/s, v_num=xhkp, train_loss_step=0.215, train_metrics/recon_loss_step=0.215]
Epoch 0:   0%|          | 102/24289 [00:35<2:19:28,  2.89it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 102
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 103/24289 [00:35<2:19:21,  2.89it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   0%|          | 103/24289 [00:35<2:19:21,  2.89it/s, v_num=xhkp, train_loss_step=0.0358, train_metrics/recon_loss_step=0.0358]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 103
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 104/24289 [00:35<2:18:53,  2.90it/s, v_num=xhkp, train_loss_step=0.0358, train_metrics/recon_loss_step=0.0358]
Epoch 0:   0%|          | 104/24289 [00:35<2:18:53,  2.90it/s, v_num=xhkp, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 20])
  Layer 1: torch.Size([1, 96, 112, 40])
  Layer 2: torch.Size([1, 72, 224, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 104
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 105/24289 [00:36<2:20:03,  2.88it/s, v_num=xhkp, train_loss_step=0.105, train_metrics/recon_loss_step=0.105]
Epoch 0:   0%|          | 105/24289 [00:36<2:20:03,  2.88it/s, v_num=xhkp, train_loss_step=0.201, train_metrics/recon_loss_step=0.201]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 105
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 106/24289 [00:36<2:19:42,  2.89it/s, v_num=xhkp, train_loss_step=0.201, train_metrics/recon_loss_step=0.201]
Epoch 0:   0%|          | 106/24289 [00:36<2:19:42,  2.88it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 106
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 107/24289 [00:37<2:19:47,  2.88it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]
Epoch 0:   0%|          | 107/24289 [00:37<2:19:47,  2.88it/s, v_num=xhkp, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 107
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 108/24289 [00:37<2:19:29,  2.89it/s, v_num=xhkp, train_loss_step=0.181, train_metrics/recon_loss_step=0.181]
Epoch 0:   0%|          | 108/24289 [00:37<2:19:30,  2.89it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 108
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 109/24289 [00:37<2:20:20,  2.87it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   0%|          | 109/24289 [00:37<2:20:20,  2.87it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 109
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 110/24289 [00:38<2:20:29,  2.87it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   0%|          | 110/24289 [00:38<2:20:29,  2.87it/s, v_num=xhkp, train_loss_step=0.0643, train_metrics/recon_loss_step=0.0643]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 110
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 111/24289 [00:38<2:20:02,  2.88it/s, v_num=xhkp, train_loss_step=0.0643, train_metrics/recon_loss_step=0.0643]
Epoch 0:   0%|          | 111/24289 [00:38<2:20:02,  2.88it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 111
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 112/24289 [00:38<2:19:44,  2.88it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   0%|          | 112/24289 [00:38<2:19:44,  2.88it/s, v_num=xhkp, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 112
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 113/24289 [00:39<2:19:20,  2.89it/s, v_num=xhkp, train_loss_step=0.183, train_metrics/recon_loss_step=0.183]
Epoch 0:   0%|          | 113/24289 [00:39<2:19:20,  2.89it/s, v_num=xhkp, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 113
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 114/24289 [00:39<2:19:06,  2.90it/s, v_num=xhkp, train_loss_step=0.272, train_metrics/recon_loss_step=0.272]
Epoch 0:   0%|          | 114/24289 [00:39<2:19:06,  2.90it/s, v_num=xhkp, train_loss_step=0.298, train_metrics/recon_loss_step=0.298]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 114
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 115/24289 [00:40<2:20:24,  2.87it/s, v_num=xhkp, train_loss_step=0.298, train_metrics/recon_loss_step=0.298]
Epoch 0:   0%|          | 115/24289 [00:40<2:20:25,  2.87it/s, v_num=xhkp, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 115
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 116/24289 [00:40<2:20:16,  2.87it/s, v_num=xhkp, train_loss_step=0.206, train_metrics/recon_loss_step=0.206]
Epoch 0:   0%|          | 116/24289 [00:40<2:20:17,  2.87it/s, v_num=xhkp, train_loss_step=0.487, train_metrics/recon_loss_step=0.487]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 25])
  Layer 1: torch.Size([1, 96, 110, 50])
  Layer 2: torch.Size([1, 72, 220, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 116
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 117/24289 [00:40<2:20:41,  2.86it/s, v_num=xhkp, train_loss_step=0.487, train_metrics/recon_loss_step=0.487]
Epoch 0:   0%|          | 117/24289 [00:40<2:20:41,  2.86it/s, v_num=xhkp, train_loss_step=0.0802, train_metrics/recon_loss_step=0.0802]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 117
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 118/24289 [00:41<2:20:32,  2.87it/s, v_num=xhkp, train_loss_step=0.0802, train_metrics/recon_loss_step=0.0802]
Epoch 0:   0%|          | 118/24289 [00:41<2:20:32,  2.87it/s, v_num=xhkp, train_loss_step=0.00534, train_metrics/recon_loss_step=0.00534]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 26])
  Layer 1: torch.Size([1, 96, 130, 52])
  Layer 2: torch.Size([1, 72, 260, 104])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 118
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 119/24289 [00:41<2:20:44,  2.86it/s, v_num=xhkp, train_loss_step=0.00534, train_metrics/recon_loss_step=0.00534]
Epoch 0:   0%|          | 119/24289 [00:41<2:20:44,  2.86it/s, v_num=xhkp, train_loss_step=0.0987, train_metrics/recon_loss_step=0.0987]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 119
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 120/24289 [00:41<2:20:22,  2.87it/s, v_num=xhkp, train_loss_step=0.0987, train_metrics/recon_loss_step=0.0987]
Epoch 0:   0%|          | 120/24289 [00:41<2:20:22,  2.87it/s, v_num=xhkp, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 120
Training phase: discriminator_pretrain

Epoch 0:   0%|          | 121/24289 [00:42<2:21:50,  2.84it/s, v_num=xhkp, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]
Epoch 0:   0%|          | 121/24289 [00:42<2:21:50,  2.84it/s, v_num=xhkp, train_loss_step=0.0765, train_metrics/recon_loss_step=0.0765]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 121
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 122/24289 [00:42<2:21:43,  2.84it/s, v_num=xhkp, train_loss_step=0.0765, train_metrics/recon_loss_step=0.0765]
Epoch 0:   1%|          | 122/24289 [00:42<2:21:44,  2.84it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 122
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 123/24289 [00:43<2:21:19,  2.85it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|          | 123/24289 [00:43<2:21:20,  2.85it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 123
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 124/24289 [00:43<2:21:03,  2.86it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   1%|          | 124/24289 [00:43<2:21:03,  2.86it/s, v_num=xhkp, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 124
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 125/24289 [00:44<2:22:43,  2.82it/s, v_num=xhkp, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]
Epoch 0:   1%|          | 125/24289 [00:44<2:22:43,  2.82it/s, v_num=xhkp, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 125
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 126/24289 [00:44<2:22:41,  2.82it/s, v_num=xhkp, train_loss_step=0.138, train_metrics/recon_loss_step=0.138]
Epoch 0:   1%|          | 126/24289 [00:44<2:22:41,  2.82it/s, v_num=xhkp, train_loss_step=0.0839, train_metrics/recon_loss_step=0.0839]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 126
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 127/24289 [00:44<2:22:20,  2.83it/s, v_num=xhkp, train_loss_step=0.0839, train_metrics/recon_loss_step=0.0839]
Epoch 0:   1%|          | 127/24289 [00:44<2:22:20,  2.83it/s, v_num=xhkp, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 127
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 128/24289 [00:45<2:22:04,  2.83it/s, v_num=xhkp, train_loss_step=0.0863, train_metrics/recon_loss_step=0.0863]
Epoch 0:   1%|          | 128/24289 [00:45<2:22:04,  2.83it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 128
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 129/24289 [00:46<2:26:00,  2.76it/s, v_num=xhkp, train_loss_step=0.210, train_metrics/recon_loss_step=0.210]
Epoch 0:   1%|          | 129/24289 [00:46<2:26:00,  2.76it/s, v_num=xhkp, train_loss_step=0.256, train_metrics/recon_loss_step=0.256]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 129
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 130/24289 [00:47<2:25:37,  2.76it/s, v_num=xhkp, train_loss_step=0.256, train_metrics/recon_loss_step=0.256]
Epoch 0:   1%|          | 130/24289 [00:47<2:25:37,  2.76it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 130
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 131/24289 [00:47<2:25:12,  2.77it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 131/24289 [00:47<2:25:12,  2.77it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 131
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 132/24289 [00:47<2:25:04,  2.78it/s, v_num=xhkp, train_loss_step=0.126, train_metrics/recon_loss_step=0.126]
Epoch 0:   1%|          | 132/24289 [00:47<2:25:05,  2.78it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 132
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 133/24289 [00:47<2:24:46,  2.78it/s, v_num=xhkp, train_loss_step=0.199, train_metrics/recon_loss_step=0.199]
Epoch 0:   1%|          | 133/24289 [00:47<2:24:46,  2.78it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 133
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 134/24289 [00:48<2:24:42,  2.78it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 134/24289 [00:48<2:24:42,  2.78it/s, v_num=xhkp, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 134
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 135/24289 [00:48<2:24:21,  2.79it/s, v_num=xhkp, train_loss_step=0.192, train_metrics/recon_loss_step=0.192]
Epoch 0:   1%|          | 135/24289 [00:48<2:24:21,  2.79it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 135
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 136/24289 [00:48<2:24:02,  2.79it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   1%|          | 136/24289 [00:48<2:24:02,  2.79it/s, v_num=xhkp, train_loss_step=0.0902, train_metrics/recon_loss_step=0.0902]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 136
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 137/24289 [00:49<2:25:33,  2.77it/s, v_num=xhkp, train_loss_step=0.0902, train_metrics/recon_loss_step=0.0902]
Epoch 0:   1%|          | 137/24289 [00:49<2:25:33,  2.77it/s, v_num=xhkp, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 137
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 138/24289 [00:49<2:25:41,  2.76it/s, v_num=xhkp, train_loss_step=0.173, train_metrics/recon_loss_step=0.173]
Epoch 0:   1%|          | 138/24289 [00:49<2:25:41,  2.76it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 138
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 139/24289 [00:50<2:25:29,  2.77it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   1%|          | 139/24289 [00:50<2:25:29,  2.77it/s, v_num=xhkp, train_loss_step=0.0196, train_metrics/recon_loss_step=0.0196]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 139
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 140/24289 [00:50<2:25:09,  2.77it/s, v_num=xhkp, train_loss_step=0.0196, train_metrics/recon_loss_step=0.0196]
Epoch 0:   1%|          | 140/24289 [00:50<2:25:09,  2.77it/s, v_num=xhkp, train_loss_step=0.094, train_metrics/recon_loss_step=0.094]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 140
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 141/24289 [00:51<2:26:20,  2.75it/s, v_num=xhkp, train_loss_step=0.094, train_metrics/recon_loss_step=0.094]
Epoch 0:   1%|          | 141/24289 [00:51<2:26:20,  2.75it/s, v_num=xhkp, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 141
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 142/24289 [00:51<2:25:58,  2.76it/s, v_num=xhkp, train_loss_step=0.209, train_metrics/recon_loss_step=0.209]
Epoch 0:   1%|          | 142/24289 [00:51<2:25:58,  2.76it/s, v_num=xhkp, train_loss_step=0.150, train_metrics/recon_loss_step=0.150]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 142
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 143/24289 [00:51<2:25:36,  2.76it/s, v_num=xhkp, train_loss_step=0.150, train_metrics/recon_loss_step=0.150]
Epoch 0:   1%|          | 143/24289 [00:51<2:25:36,  2.76it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 143
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 144/24289 [00:52<2:25:22,  2.77it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 144/24289 [00:52<2:25:22,  2.77it/s, v_num=xhkp, train_loss_step=0.408, train_metrics/recon_loss_step=0.408]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 144
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 145/24289 [00:52<2:25:13,  2.77it/s, v_num=xhkp, train_loss_step=0.408, train_metrics/recon_loss_step=0.408]
Epoch 0:   1%|          | 145/24289 [00:52<2:25:14,  2.77it/s, v_num=xhkp, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 145
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 146/24289 [00:52<2:24:58,  2.78it/s, v_num=xhkp, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   1%|          | 146/24289 [00:52<2:24:58,  2.78it/s, v_num=xhkp, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 146
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 147/24289 [00:53<2:25:07,  2.77it/s, v_num=xhkp, train_loss_step=0.234, train_metrics/recon_loss_step=0.234]
Epoch 0:   1%|          | 147/24289 [00:53<2:25:07,  2.77it/s, v_num=xhkp, train_loss_step=0.0293, train_metrics/recon_loss_step=0.0293]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 147
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 148/24289 [00:53<2:25:04,  2.77it/s, v_num=xhkp, train_loss_step=0.0293, train_metrics/recon_loss_step=0.0293]
Epoch 0:   1%|          | 148/24289 [00:53<2:25:05,  2.77it/s, v_num=xhkp, train_loss_step=0.0786, train_metrics/recon_loss_step=0.0786]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 148
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 149/24289 [00:53<2:24:43,  2.78it/s, v_num=xhkp, train_loss_step=0.0786, train_metrics/recon_loss_step=0.0786]
Epoch 0:   1%|          | 149/24289 [00:53<2:24:43,  2.78it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 29])
  Layer 1: torch.Size([1, 96, 128, 58])
  Layer 2: torch.Size([1, 72, 256, 116])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 149
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 150/24289 [00:53<2:24:49,  2.78it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   1%|          | 150/24289 [00:53<2:24:49,  2.78it/s, v_num=xhkp, train_loss_step=0.098, train_metrics/recon_loss_step=0.098]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 150
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 151/24289 [00:54<2:24:36,  2.78it/s, v_num=xhkp, train_loss_step=0.098, train_metrics/recon_loss_step=0.098]
Epoch 0:   1%|          | 151/24289 [00:54<2:24:36,  2.78it/s, v_num=xhkp, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 151
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 152/24289 [00:54<2:25:22,  2.77it/s, v_num=xhkp, train_loss_step=0.144, train_metrics/recon_loss_step=0.144]
Epoch 0:   1%|          | 152/24289 [00:54<2:25:22,  2.77it/s, v_num=xhkp, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 16])
  Layer 1: torch.Size([1, 96, 128, 32])
  Layer 2: torch.Size([1, 72, 256, 64])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 152
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 153/24289 [00:55<2:25:18,  2.77it/s, v_num=xhkp, train_loss_step=0.134, train_metrics/recon_loss_step=0.134]
Epoch 0:   1%|          | 153/24289 [00:55<2:25:18,  2.77it/s, v_num=xhkp, train_loss_step=0.323, train_metrics/recon_loss_step=0.323]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 153
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 154/24289 [00:55<2:25:01,  2.77it/s, v_num=xhkp, train_loss_step=0.323, train_metrics/recon_loss_step=0.323]
Epoch 0:   1%|          | 154/24289 [00:55<2:25:01,  2.77it/s, v_num=xhkp, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 154
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 155/24289 [00:55<2:24:43,  2.78it/s, v_num=xhkp, train_loss_step=0.226, train_metrics/recon_loss_step=0.226]
Epoch 0:   1%|          | 155/24289 [00:55<2:24:43,  2.78it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 155
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 156/24289 [00:56<2:24:36,  2.78it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   1%|          | 156/24289 [00:56<2:24:36,  2.78it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 156
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 157/24289 [00:56<2:24:19,  2.79it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 157/24289 [00:56<2:24:19,  2.79it/s, v_num=xhkp, train_loss_step=0.245, train_metrics/recon_loss_step=0.245]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 157
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 158/24289 [00:56<2:24:05,  2.79it/s, v_num=xhkp, train_loss_step=0.245, train_metrics/recon_loss_step=0.245]
Epoch 0:   1%|          | 158/24289 [00:56<2:24:05,  2.79it/s, v_num=xhkp, train_loss_step=0.225, train_metrics/recon_loss_step=0.225]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 158
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 159/24289 [00:56<2:23:46,  2.80it/s, v_num=xhkp, train_loss_step=0.225, train_metrics/recon_loss_step=0.225]
Epoch 0:   1%|          | 159/24289 [00:56<2:23:46,  2.80it/s, v_num=xhkp, train_loss_step=0.0861, train_metrics/recon_loss_step=0.0861]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 159
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 160/24289 [00:57<2:23:31,  2.80it/s, v_num=xhkp, train_loss_step=0.0861, train_metrics/recon_loss_step=0.0861]
Epoch 0:   1%|          | 160/24289 [00:57<2:23:31,  2.80it/s, v_num=xhkp, train_loss_step=0.300, train_metrics/recon_loss_step=0.300]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 160
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 161/24289 [00:57<2:23:16,  2.81it/s, v_num=xhkp, train_loss_step=0.300, train_metrics/recon_loss_step=0.300]
Epoch 0:   1%|          | 161/24289 [00:57<2:23:16,  2.81it/s, v_num=xhkp, train_loss_step=0.224, train_metrics/recon_loss_step=0.224]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 161
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 162/24289 [00:57<2:23:23,  2.80it/s, v_num=xhkp, train_loss_step=0.224, train_metrics/recon_loss_step=0.224]
Epoch 0:   1%|          | 162/24289 [00:57<2:23:23,  2.80it/s, v_num=xhkp, train_loss_step=0.458, train_metrics/recon_loss_step=0.458]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 21])
  Layer 1: torch.Size([1, 96, 130, 42])
  Layer 2: torch.Size([1, 72, 260, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 162
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 163/24289 [00:58<2:23:16,  2.81it/s, v_num=xhkp, train_loss_step=0.458, train_metrics/recon_loss_step=0.458]
Epoch 0:   1%|          | 163/24289 [00:58<2:23:16,  2.81it/s, v_num=xhkp, train_loss_step=0.434, train_metrics/recon_loss_step=0.434]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 163
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 164/24289 [00:58<2:23:08,  2.81it/s, v_num=xhkp, train_loss_step=0.434, train_metrics/recon_loss_step=0.434]
Epoch 0:   1%|          | 164/24289 [00:58<2:23:08,  2.81it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 54, 21])
  Layer 1: torch.Size([1, 96, 108, 42])
  Layer 2: torch.Size([1, 72, 216, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 164
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 165/24289 [00:58<2:22:55,  2.81it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   1%|          | 165/24289 [00:58<2:22:55,  2.81it/s, v_num=xhkp, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 20])
  Layer 1: torch.Size([1, 96, 114, 40])
  Layer 2: torch.Size([1, 72, 228, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 165
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 166/24289 [00:58<2:22:45,  2.82it/s, v_num=xhkp, train_loss_step=0.178, train_metrics/recon_loss_step=0.178]
Epoch 0:   1%|          | 166/24289 [00:58<2:22:45,  2.82it/s, v_num=xhkp, train_loss_step=0.212, train_metrics/recon_loss_step=0.212]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 166
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 167/24289 [00:59<2:22:27,  2.82it/s, v_num=xhkp, train_loss_step=0.212, train_metrics/recon_loss_step=0.212]
Epoch 0:   1%|          | 167/24289 [00:59<2:22:27,  2.82it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 167
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 168/24289 [00:59<2:22:10,  2.83it/s, v_num=xhkp, train_loss_step=0.168, train_metrics/recon_loss_step=0.168]
Epoch 0:   1%|          | 168/24289 [00:59<2:22:10,  2.83it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 168
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 169/24289 [00:59<2:21:53,  2.83it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   1%|          | 169/24289 [00:59<2:21:53,  2.83it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 169
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 170/24289 [00:59<2:21:37,  2.84it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   1%|          | 170/24289 [00:59<2:21:37,  2.84it/s, v_num=xhkp, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 170
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 171/24289 [01:00<2:21:21,  2.84it/s, v_num=xhkp, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   1%|          | 171/24289 [01:00<2:21:21,  2.84it/s, v_num=xhkp, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 171
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 172/24289 [01:00<2:21:04,  2.85it/s, v_num=xhkp, train_loss_step=0.198, train_metrics/recon_loss_step=0.198]
Epoch 0:   1%|          | 172/24289 [01:00<2:21:04,  2.85it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 172
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 173/24289 [01:00<2:20:51,  2.85it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 173/24289 [01:00<2:20:51,  2.85it/s, v_num=xhkp, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 173
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 174/24289 [01:00<2:20:34,  2.86it/s, v_num=xhkp, train_loss_step=0.141, train_metrics/recon_loss_step=0.141]
Epoch 0:   1%|          | 174/24289 [01:00<2:20:35,  2.86it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 174
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 175/24289 [01:01<2:20:22,  2.86it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 175/24289 [01:01<2:20:22,  2.86it/s, v_num=xhkp, train_loss_step=0.219, train_metrics/recon_loss_step=0.219]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 22])
  Layer 1: torch.Size([1, 96, 98, 44])
  Layer 2: torch.Size([1, 72, 196, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 175
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 176/24289 [01:01<2:20:14,  2.87it/s, v_num=xhkp, train_loss_step=0.219, train_metrics/recon_loss_step=0.219]
Epoch 0:   1%|          | 176/24289 [01:01<2:20:14,  2.87it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 176
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 177/24289 [01:01<2:19:58,  2.87it/s, v_num=xhkp, train_loss_step=0.196, train_metrics/recon_loss_step=0.196]
Epoch 0:   1%|          | 177/24289 [01:01<2:19:58,  2.87it/s, v_num=xhkp, train_loss_step=0.0962, train_metrics/recon_loss_step=0.0962]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 177
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 178/24289 [01:01<2:19:51,  2.87it/s, v_num=xhkp, train_loss_step=0.0962, train_metrics/recon_loss_step=0.0962]
Epoch 0:   1%|          | 178/24289 [01:01<2:19:51,  2.87it/s, v_num=xhkp, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 20])
  Layer 1: torch.Size([1, 96, 104, 40])
  Layer 2: torch.Size([1, 72, 208, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 178
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 179/24289 [01:02<2:19:49,  2.87it/s, v_num=xhkp, train_loss_step=0.188, train_metrics/recon_loss_step=0.188]
Epoch 0:   1%|          | 179/24289 [01:02<2:19:49,  2.87it/s, v_num=xhkp, train_loss_step=0.0871, train_metrics/recon_loss_step=0.0871]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 179
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 180/24289 [01:02<2:19:34,  2.88it/s, v_num=xhkp, train_loss_step=0.0871, train_metrics/recon_loss_step=0.0871]
Epoch 0:   1%|          | 180/24289 [01:02<2:19:35,  2.88it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 180
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 181/24289 [01:02<2:19:26,  2.88it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 181/24289 [01:02<2:19:26,  2.88it/s, v_num=xhkp, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 181
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 182/24289 [01:03<2:19:21,  2.88it/s, v_num=xhkp, train_loss_step=0.211, train_metrics/recon_loss_step=0.211]
Epoch 0:   1%|          | 182/24289 [01:03<2:19:21,  2.88it/s, v_num=xhkp, train_loss_step=0.00372, train_metrics/recon_loss_step=0.00372]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 182
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 183/24289 [01:03<2:19:14,  2.89it/s, v_num=xhkp, train_loss_step=0.00372, train_metrics/recon_loss_step=0.00372]
Epoch 0:   1%|          | 183/24289 [01:03<2:19:14,  2.89it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]    DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 23])
  Layer 1: torch.Size([1, 96, 98, 46])
  Layer 2: torch.Size([1, 72, 196, 92])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 183
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 184/24289 [01:03<2:19:13,  2.89it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 184/24289 [01:03<2:19:14,  2.89it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 21])
  Layer 1: torch.Size([1, 96, 106, 42])
  Layer 2: torch.Size([1, 72, 212, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 184
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 185/24289 [01:04<2:20:36,  2.86it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 185/24289 [01:04<2:20:36,  2.86it/s, v_num=xhkp, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 185
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 186/24289 [01:04<2:20:22,  2.86it/s, v_num=xhkp, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|          | 186/24289 [01:04<2:20:22,  2.86it/s, v_num=xhkp, train_loss_step=0.0913, train_metrics/recon_loss_step=0.0913]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 186
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 187/24289 [01:05<2:20:17,  2.86it/s, v_num=xhkp, train_loss_step=0.0913, train_metrics/recon_loss_step=0.0913]
Epoch 0:   1%|          | 187/24289 [01:05<2:20:17,  2.86it/s, v_num=xhkp, train_loss_step=0.0217, train_metrics/recon_loss_step=0.0217]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 187
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 188/24289 [01:05<2:20:03,  2.87it/s, v_num=xhkp, train_loss_step=0.0217, train_metrics/recon_loss_step=0.0217]
Epoch 0:   1%|          | 188/24289 [01:05<2:20:03,  2.87it/s, v_num=xhkp, train_loss_step=0.0643, train_metrics/recon_loss_step=0.0643]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 188
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 189/24289 [01:05<2:20:10,  2.87it/s, v_num=xhkp, train_loss_step=0.0643, train_metrics/recon_loss_step=0.0643]
Epoch 0:   1%|          | 189/24289 [01:05<2:20:11,  2.87it/s, v_num=xhkp, train_loss_step=0.0761, train_metrics/recon_loss_step=0.0761]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 189
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 190/24289 [01:06<2:19:56,  2.87it/s, v_num=xhkp, train_loss_step=0.0761, train_metrics/recon_loss_step=0.0761]
Epoch 0:   1%|          | 190/24289 [01:06<2:19:56,  2.87it/s, v_num=xhkp, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 190
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 191/24289 [01:07<2:21:03,  2.85it/s, v_num=xhkp, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   1%|          | 191/24289 [01:07<2:21:03,  2.85it/s, v_num=xhkp, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 17])
  Layer 1: torch.Size([1, 96, 104, 34])
  Layer 2: torch.Size([1, 72, 208, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 191
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 192/24289 [01:07<2:20:59,  2.85it/s, v_num=xhkp, train_loss_step=0.197, train_metrics/recon_loss_step=0.197]
Epoch 0:   1%|          | 192/24289 [01:07<2:20:59,  2.85it/s, v_num=xhkp, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 18])
  Layer 1: torch.Size([1, 96, 128, 36])
  Layer 2: torch.Size([1, 72, 256, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 192
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 193/24289 [01:07<2:21:29,  2.84it/s, v_num=xhkp, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|          | 193/24289 [01:07<2:21:29,  2.84it/s, v_num=xhkp, train_loss_step=0.300, train_metrics/recon_loss_step=0.300]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 193
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 194/24289 [01:08<2:21:38,  2.84it/s, v_num=xhkp, train_loss_step=0.300, train_metrics/recon_loss_step=0.300]
Epoch 0:   1%|          | 194/24289 [01:08<2:21:38,  2.84it/s, v_num=xhkp, train_loss_step=0.285, train_metrics/recon_loss_step=0.285]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 194
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 195/24289 [01:08<2:21:58,  2.83it/s, v_num=xhkp, train_loss_step=0.285, train_metrics/recon_loss_step=0.285]
Epoch 0:   1%|          | 195/24289 [01:08<2:21:59,  2.83it/s, v_num=xhkp, train_loss_step=0.0251, train_metrics/recon_loss_step=0.0251]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 20])
  Layer 1: torch.Size([1, 96, 98, 40])
  Layer 2: torch.Size([1, 72, 196, 80])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 195
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 196/24289 [01:09<2:21:57,  2.83it/s, v_num=xhkp, train_loss_step=0.0251, train_metrics/recon_loss_step=0.0251]
Epoch 0:   1%|          | 196/24289 [01:09<2:21:57,  2.83it/s, v_num=xhkp, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 196
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 197/24289 [01:09<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]
Epoch 0:   1%|          | 197/24289 [01:09<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 197
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 198/24289 [01:09<2:21:33,  2.84it/s, v_num=xhkp, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 198/24289 [01:09<2:21:33,  2.84it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 198
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 199/24289 [01:10<2:21:54,  2.83it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 199/24289 [01:10<2:21:54,  2.83it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 199
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 200/24289 [01:10<2:21:41,  2.83it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 200/24289 [01:10<2:21:41,  2.83it/s, v_num=xhkp, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 200
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 201/24289 [01:10<2:21:29,  2.84it/s, v_num=xhkp, train_loss_step=0.0992, train_metrics/recon_loss_step=0.0992]
Epoch 0:   1%|          | 201/24289 [01:10<2:21:29,  2.84it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 201
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 202/24289 [01:11<2:21:18,  2.84it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 202/24289 [01:11<2:21:18,  2.84it/s, v_num=xhkp, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 24])
  Layer 1: torch.Size([1, 96, 112, 48])
  Layer 2: torch.Size([1, 72, 224, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 202
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 203/24289 [01:11<2:21:12,  2.84it/s, v_num=xhkp, train_loss_step=0.185, train_metrics/recon_loss_step=0.185]
Epoch 0:   1%|          | 203/24289 [01:11<2:21:12,  2.84it/s, v_num=xhkp, train_loss_step=0.247, train_metrics/recon_loss_step=0.247]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 203
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 204/24289 [01:11<2:21:03,  2.85it/s, v_num=xhkp, train_loss_step=0.247, train_metrics/recon_loss_step=0.247]
Epoch 0:   1%|          | 204/24289 [01:11<2:21:03,  2.85it/s, v_num=xhkp, train_loss_step=0.0945, train_metrics/recon_loss_step=0.0945]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 204
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 205/24289 [01:11<2:20:53,  2.85it/s, v_num=xhkp, train_loss_step=0.0945, train_metrics/recon_loss_step=0.0945]
Epoch 0:   1%|          | 205/24289 [01:11<2:20:53,  2.85it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 205
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 206/24289 [01:12<2:20:39,  2.85it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   1%|          | 206/24289 [01:12<2:20:39,  2.85it/s, v_num=xhkp, train_loss_step=0.0952, train_metrics/recon_loss_step=0.0952]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 206
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 207/24289 [01:12<2:20:26,  2.86it/s, v_num=xhkp, train_loss_step=0.0952, train_metrics/recon_loss_step=0.0952]
Epoch 0:   1%|          | 207/24289 [01:12<2:20:26,  2.86it/s, v_num=xhkp, train_loss_step=0.0818, train_metrics/recon_loss_step=0.0818]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 207
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 208/24289 [01:12<2:20:13,  2.86it/s, v_num=xhkp, train_loss_step=0.0818, train_metrics/recon_loss_step=0.0818]
Epoch 0:   1%|          | 208/24289 [01:12<2:20:13,  2.86it/s, v_num=xhkp, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 208
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 209/24289 [01:15<2:24:15,  2.78it/s, v_num=xhkp, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   1%|          | 209/24289 [01:15<2:24:15,  2.78it/s, v_num=xhkp, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 209
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 210/24289 [01:15<2:24:02,  2.79it/s, v_num=xhkp, train_loss_step=0.116, train_metrics/recon_loss_step=0.116]
Epoch 0:   1%|          | 210/24289 [01:15<2:24:02,  2.79it/s, v_num=xhkp, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 210
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 211/24289 [01:15<2:24:09,  2.78it/s, v_num=xhkp, train_loss_step=0.162, train_metrics/recon_loss_step=0.162]
Epoch 0:   1%|          | 211/24289 [01:15<2:24:09,  2.78it/s, v_num=xhkp, train_loss_step=0.0325, train_metrics/recon_loss_step=0.0325]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 211
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 212/24289 [01:16<2:23:55,  2.79it/s, v_num=xhkp, train_loss_step=0.0325, train_metrics/recon_loss_step=0.0325]
Epoch 0:   1%|          | 212/24289 [01:16<2:23:55,  2.79it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 24])
  Layer 1: torch.Size([1, 96, 130, 48])
  Layer 2: torch.Size([1, 72, 260, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 212
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 213/24289 [01:16<2:23:53,  2.79it/s, v_num=xhkp, train_loss_step=0.115, train_metrics/recon_loss_step=0.115]
Epoch 0:   1%|          | 213/24289 [01:16<2:23:53,  2.79it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 213
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 214/24289 [01:16<2:23:43,  2.79it/s, v_num=xhkp, train_loss_step=0.112, train_metrics/recon_loss_step=0.112]
Epoch 0:   1%|          | 214/24289 [01:16<2:23:43,  2.79it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 214
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 215/24289 [01:16<2:23:37,  2.79it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   1%|          | 215/24289 [01:16<2:23:37,  2.79it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 215
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 216/24289 [01:17<2:23:22,  2.80it/s, v_num=xhkp, train_loss_step=0.120, train_metrics/recon_loss_step=0.120]
Epoch 0:   1%|          | 216/24289 [01:17<2:23:22,  2.80it/s, v_num=xhkp, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 216
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 217/24289 [01:17<2:23:16,  2.80it/s, v_num=xhkp, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]
Epoch 0:   1%|          | 217/24289 [01:17<2:23:16,  2.80it/s, v_num=xhkp, train_loss_step=0.0226, train_metrics/recon_loss_step=0.0226]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 217
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 218/24289 [01:17<2:23:03,  2.80it/s, v_num=xhkp, train_loss_step=0.0226, train_metrics/recon_loss_step=0.0226]
Epoch 0:   1%|          | 218/24289 [01:17<2:23:03,  2.80it/s, v_num=xhkp, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 218
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 219/24289 [01:17<2:22:52,  2.81it/s, v_num=xhkp, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   1%|          | 219/24289 [01:17<2:22:52,  2.81it/s, v_num=xhkp, train_loss_step=0.291, train_metrics/recon_loss_step=0.291]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 219
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 220/24289 [01:18<2:22:40,  2.81it/s, v_num=xhkp, train_loss_step=0.291, train_metrics/recon_loss_step=0.291]
Epoch 0:   1%|          | 220/24289 [01:18<2:22:40,  2.81it/s, v_num=xhkp, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 220
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 221/24289 [01:18<2:22:26,  2.82it/s, v_num=xhkp, train_loss_step=0.195, train_metrics/recon_loss_step=0.195]
Epoch 0:   1%|          | 221/24289 [01:18<2:22:26,  2.82it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 221
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 222/24289 [01:18<2:22:21,  2.82it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 222/24289 [01:18<2:22:21,  2.82it/s, v_num=xhkp, train_loss_step=0.0156, train_metrics/recon_loss_step=0.0156]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 17])
  Layer 1: torch.Size([1, 96, 130, 34])
  Layer 2: torch.Size([1, 72, 260, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 222
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 223/24289 [01:19<2:22:12,  2.82it/s, v_num=xhkp, train_loss_step=0.0156, train_metrics/recon_loss_step=0.0156]
Epoch 0:   1%|          | 223/24289 [01:19<2:22:12,  2.82it/s, v_num=xhkp, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 223
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 224/24289 [01:19<2:21:59,  2.82it/s, v_num=xhkp, train_loss_step=0.170, train_metrics/recon_loss_step=0.170]
Epoch 0:   1%|          | 224/24289 [01:19<2:21:59,  2.82it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 224
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 225/24289 [01:19<2:21:47,  2.83it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 225/24289 [01:19<2:21:47,  2.83it/s, v_num=xhkp, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 225
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 226/24289 [01:19<2:21:34,  2.83it/s, v_num=xhkp, train_loss_step=0.125, train_metrics/recon_loss_step=0.125]
Epoch 0:   1%|          | 226/24289 [01:19<2:21:34,  2.83it/s, v_num=xhkp, train_loss_step=0.0976, train_metrics/recon_loss_step=0.0976]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 17])
  Layer 1: torch.Size([1, 96, 128, 34])
  Layer 2: torch.Size([1, 72, 256, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 226
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 227/24289 [01:20<2:22:10,  2.82it/s, v_num=xhkp, train_loss_step=0.0976, train_metrics/recon_loss_step=0.0976]
Epoch 0:   1%|          | 227/24289 [01:20<2:22:10,  2.82it/s, v_num=xhkp, train_loss_step=0.180, train_metrics/recon_loss_step=0.180]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 227
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 228/24289 [01:20<2:22:04,  2.82it/s, v_num=xhkp, train_loss_step=0.180, train_metrics/recon_loss_step=0.180]
Epoch 0:   1%|          | 228/24289 [01:20<2:22:04,  2.82it/s, v_num=xhkp, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 228
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 229/24289 [01:21<2:21:53,  2.83it/s, v_num=xhkp, train_loss_step=0.157, train_metrics/recon_loss_step=0.157]
Epoch 0:   1%|          | 229/24289 [01:21<2:21:53,  2.83it/s, v_num=xhkp, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 229
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 230/24289 [01:21<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.187, train_metrics/recon_loss_step=0.187]
Epoch 0:   1%|          | 230/24289 [01:21<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.331, train_metrics/recon_loss_step=0.331]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 24])
  Layer 1: torch.Size([1, 96, 128, 48])
  Layer 2: torch.Size([1, 72, 256, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 230
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 231/24289 [01:21<2:21:49,  2.83it/s, v_num=xhkp, train_loss_step=0.331, train_metrics/recon_loss_step=0.331]
Epoch 0:   1%|          | 231/24289 [01:21<2:21:49,  2.83it/s, v_num=xhkp, train_loss_step=0.0764, train_metrics/recon_loss_step=0.0764]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 231
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 232/24289 [01:22<2:21:56,  2.82it/s, v_num=xhkp, train_loss_step=0.0764, train_metrics/recon_loss_step=0.0764]
Epoch 0:   1%|          | 232/24289 [01:22<2:21:56,  2.82it/s, v_num=xhkp, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 13])
  Layer 1: torch.Size([1, 96, 128, 26])
  Layer 2: torch.Size([1, 72, 256, 52])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 232
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 233/24289 [01:22<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.130, train_metrics/recon_loss_step=0.130]
Epoch 0:   1%|          | 233/24289 [01:22<2:21:45,  2.83it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 233
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 234/24289 [01:22<2:21:32,  2.83it/s, v_num=xhkp, train_loss_step=0.102, train_metrics/recon_loss_step=0.102]
Epoch 0:   1%|          | 234/24289 [01:22<2:21:32,  2.83it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 234
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 235/24289 [01:22<2:21:21,  2.84it/s, v_num=xhkp, train_loss_step=0.109, train_metrics/recon_loss_step=0.109]
Epoch 0:   1%|          | 235/24289 [01:22<2:21:21,  2.84it/s, v_num=xhkp, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 235
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 236/24289 [01:23<2:21:11,  2.84it/s, v_num=xhkp, train_loss_step=0.104, train_metrics/recon_loss_step=0.104]
Epoch 0:   1%|          | 236/24289 [01:23<2:21:11,  2.84it/s, v_num=xhkp, train_loss_step=0.0831, train_metrics/recon_loss_step=0.0831]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 236
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 237/24289 [01:23<2:21:08,  2.84it/s, v_num=xhkp, train_loss_step=0.0831, train_metrics/recon_loss_step=0.0831]
Epoch 0:   1%|          | 237/24289 [01:23<2:21:08,  2.84it/s, v_num=xhkp, train_loss_step=0.0847, train_metrics/recon_loss_step=0.0847]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 237
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 238/24289 [01:23<2:20:57,  2.84it/s, v_num=xhkp, train_loss_step=0.0847, train_metrics/recon_loss_step=0.0847]
Epoch 0:   1%|          | 238/24289 [01:23<2:20:57,  2.84it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 238
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 239/24289 [01:23<2:20:50,  2.85it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 239/24289 [01:23<2:20:50,  2.85it/s, v_num=xhkp, train_loss_step=0.335, train_metrics/recon_loss_step=0.335]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 239
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 240/24289 [01:24<2:20:40,  2.85it/s, v_num=xhkp, train_loss_step=0.335, train_metrics/recon_loss_step=0.335]
Epoch 0:   1%|          | 240/24289 [01:24<2:20:40,  2.85it/s, v_num=xhkp, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 240
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 241/24289 [01:24<2:20:29,  2.85it/s, v_num=xhkp, train_loss_step=0.0645, train_metrics/recon_loss_step=0.0645]
Epoch 0:   1%|          | 241/24289 [01:24<2:20:29,  2.85it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 241
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 242/24289 [01:24<2:20:18,  2.86it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|          | 242/24289 [01:24<2:20:18,  2.86it/s, v_num=xhkp, train_loss_step=0.0678, train_metrics/recon_loss_step=0.0678]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 14])
  Layer 1: torch.Size([1, 96, 98, 28])
  Layer 2: torch.Size([1, 72, 196, 56])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 242
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 243/24289 [01:25<2:20:15,  2.86it/s, v_num=xhkp, train_loss_step=0.0678, train_metrics/recon_loss_step=0.0678]
Epoch 0:   1%|          | 243/24289 [01:25<2:20:15,  2.86it/s, v_num=xhkp, train_loss_step=0.299, train_metrics/recon_loss_step=0.299]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 243
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 244/24289 [01:25<2:20:12,  2.86it/s, v_num=xhkp, train_loss_step=0.299, train_metrics/recon_loss_step=0.299]
Epoch 0:   1%|          | 244/24289 [01:25<2:20:12,  2.86it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 244
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 245/24289 [01:25<2:20:00,  2.86it/s, v_num=xhkp, train_loss_step=0.155, train_metrics/recon_loss_step=0.155]
Epoch 0:   1%|          | 245/24289 [01:25<2:20:00,  2.86it/s, v_num=xhkp, train_loss_step=0.0828, train_metrics/recon_loss_step=0.0828]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 245
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 246/24289 [01:25<2:19:50,  2.87it/s, v_num=xhkp, train_loss_step=0.0828, train_metrics/recon_loss_step=0.0828]
Epoch 0:   1%|          | 246/24289 [01:25<2:19:50,  2.87it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 246
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 247/24289 [01:26<2:19:42,  2.87it/s, v_num=xhkp, train_loss_step=0.154, train_metrics/recon_loss_step=0.154]
Epoch 0:   1%|          | 247/24289 [01:26<2:19:42,  2.87it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 18])
  Layer 1: torch.Size([1, 96, 110, 36])
  Layer 2: torch.Size([1, 72, 220, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 247
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 248/24289 [01:26<2:19:34,  2.87it/s, v_num=xhkp, train_loss_step=0.133, train_metrics/recon_loss_step=0.133]
Epoch 0:   1%|          | 248/24289 [01:26<2:19:34,  2.87it/s, v_num=xhkp, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 19])
  Layer 1: torch.Size([1, 96, 130, 38])
  Layer 2: torch.Size([1, 72, 260, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 248
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 249/24289 [01:26<2:19:34,  2.87it/s, v_num=xhkp, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 249/24289 [01:26<2:19:34,  2.87it/s, v_num=xhkp, train_loss_step=0.0585, train_metrics/recon_loss_step=0.0585]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 249
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 250/24289 [01:26<2:19:23,  2.87it/s, v_num=xhkp, train_loss_step=0.0585, train_metrics/recon_loss_step=0.0585]
Epoch 0:   1%|          | 250/24289 [01:26<2:19:23,  2.87it/s, v_num=xhkp, train_loss_step=0.0798, train_metrics/recon_loss_step=0.0798]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 21])
  Layer 1: torch.Size([1, 96, 114, 42])
  Layer 2: torch.Size([1, 72, 228, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 250
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 251/24289 [01:27<2:19:51,  2.86it/s, v_num=xhkp, train_loss_step=0.0798, train_metrics/recon_loss_step=0.0798]
Epoch 0:   1%|          | 251/24289 [01:27<2:19:51,  2.86it/s, v_num=xhkp, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 251
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 252/24289 [01:27<2:19:42,  2.87it/s, v_num=xhkp, train_loss_step=0.110, train_metrics/recon_loss_step=0.110]
Epoch 0:   1%|          | 252/24289 [01:27<2:19:42,  2.87it/s, v_num=xhkp, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 252
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 253/24289 [01:28<2:19:33,  2.87it/s, v_num=xhkp, train_loss_step=0.129, train_metrics/recon_loss_step=0.129]
Epoch 0:   1%|          | 253/24289 [01:28<2:19:33,  2.87it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 52, 21])
  Layer 1: torch.Size([1, 96, 104, 42])
  Layer 2: torch.Size([1, 72, 208, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 253
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 254/24289 [01:28<2:19:25,  2.87it/s, v_num=xhkp, train_loss_step=0.203, train_metrics/recon_loss_step=0.203]
Epoch 0:   1%|          | 254/24289 [01:28<2:19:25,  2.87it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 254
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 255/24289 [01:30<2:21:33,  2.83it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 255/24289 [01:30<2:21:33,  2.83it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 21])
  Layer 1: torch.Size([1, 96, 112, 42])
  Layer 2: torch.Size([1, 72, 224, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 255
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 256/24289 [01:30<2:21:25,  2.83it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 256/24289 [01:30<2:21:25,  2.83it/s, v_num=xhkp, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 256
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 257/24289 [01:30<2:21:21,  2.83it/s, v_num=xhkp, train_loss_step=0.161, train_metrics/recon_loss_step=0.161]
Epoch 0:   1%|          | 257/24289 [01:30<2:21:21,  2.83it/s, v_num=xhkp, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 257
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 258/24289 [01:31<2:21:26,  2.83it/s, v_num=xhkp, train_loss_step=0.151, train_metrics/recon_loss_step=0.151]
Epoch 0:   1%|          | 258/24289 [01:31<2:21:26,  2.83it/s, v_num=xhkp, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 18])
  Layer 1: torch.Size([1, 96, 96, 36])
  Layer 2: torch.Size([1, 72, 192, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 258
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 259/24289 [01:31<2:21:41,  2.83it/s, v_num=xhkp, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|          | 259/24289 [01:31<2:21:41,  2.83it/s, v_num=xhkp, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 56, 28])
  Layer 1: torch.Size([1, 96, 112, 56])
  Layer 2: torch.Size([1, 72, 224, 112])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 259
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 260/24289 [01:32<2:21:46,  2.82it/s, v_num=xhkp, train_loss_step=0.159, train_metrics/recon_loss_step=0.159]
Epoch 0:   1%|          | 260/24289 [01:32<2:21:46,  2.82it/s, v_num=xhkp, train_loss_step=0.288, train_metrics/recon_loss_step=0.288]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 260
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 261/24289 [01:32<2:21:35,  2.83it/s, v_num=xhkp, train_loss_step=0.288, train_metrics/recon_loss_step=0.288]
Epoch 0:   1%|          | 261/24289 [01:32<2:21:35,  2.83it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 261
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 262/24289 [01:32<2:21:23,  2.83it/s, v_num=xhkp, train_loss_step=0.131, train_metrics/recon_loss_step=0.131]
Epoch 0:   1%|          | 262/24289 [01:32<2:21:23,  2.83it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 262
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 263/24289 [01:32<2:21:12,  2.84it/s, v_num=xhkp, train_loss_step=0.135, train_metrics/recon_loss_step=0.135]
Epoch 0:   1%|          | 263/24289 [01:32<2:21:12,  2.84it/s, v_num=xhkp, train_loss_step=0.0619, train_metrics/recon_loss_step=0.0619]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 263
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 264/24289 [01:33<2:21:07,  2.84it/s, v_num=xhkp, train_loss_step=0.0619, train_metrics/recon_loss_step=0.0619]
Epoch 0:   1%|          | 264/24289 [01:33<2:21:07,  2.84it/s, v_num=xhkp, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 264
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 265/24289 [01:33<2:20:56,  2.84it/s, v_num=xhkp, train_loss_step=0.0647, train_metrics/recon_loss_step=0.0647]
Epoch 0:   1%|          | 265/24289 [01:33<2:20:56,  2.84it/s, v_num=xhkp, train_loss_step=0.0641, train_metrics/recon_loss_step=0.0641]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 265
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 266/24289 [01:33<2:20:44,  2.84it/s, v_num=xhkp, train_loss_step=0.0641, train_metrics/recon_loss_step=0.0641]
Epoch 0:   1%|          | 266/24289 [01:33<2:20:45,  2.84it/s, v_num=xhkp, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 266
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 267/24289 [01:33<2:20:47,  2.84it/s, v_num=xhkp, train_loss_step=0.100, train_metrics/recon_loss_step=0.100]
Epoch 0:   1%|          | 267/24289 [01:33<2:20:47,  2.84it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 267
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 268/24289 [01:34<2:20:36,  2.85it/s, v_num=xhkp, train_loss_step=0.108, train_metrics/recon_loss_step=0.108]
Epoch 0:   1%|          | 268/24289 [01:34<2:20:36,  2.85it/s, v_num=xhkp, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 268
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 269/24289 [01:34<2:20:28,  2.85it/s, v_num=xhkp, train_loss_step=0.0884, train_metrics/recon_loss_step=0.0884]
Epoch 0:   1%|          | 269/24289 [01:34<2:20:28,  2.85it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 269
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 270/24289 [01:35<2:20:53,  2.84it/s, v_num=xhkp, train_loss_step=0.139, train_metrics/recon_loss_step=0.139]
Epoch 0:   1%|          | 270/24289 [01:35<2:20:53,  2.84it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 53, 18])
  Layer 1: torch.Size([1, 96, 106, 36])
  Layer 2: torch.Size([1, 72, 212, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 270
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 271/24289 [01:35<2:20:45,  2.84it/s, v_num=xhkp, train_loss_step=0.164, train_metrics/recon_loss_step=0.164]
Epoch 0:   1%|          | 271/24289 [01:35<2:20:45,  2.84it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 25])
  Layer 1: torch.Size([1, 96, 114, 50])
  Layer 2: torch.Size([1, 72, 228, 100])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 271
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 272/24289 [01:36<2:22:26,  2.81it/s, v_num=xhkp, train_loss_step=0.186, train_metrics/recon_loss_step=0.186]
Epoch 0:   1%|          | 272/24289 [01:36<2:22:26,  2.81it/s, v_num=xhkp, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 17])
  Layer 1: torch.Size([1, 96, 96, 34])
  Layer 2: torch.Size([1, 72, 192, 68])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 272
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 273/24289 [01:37<2:22:16,  2.81it/s, v_num=xhkp, train_loss_step=0.208, train_metrics/recon_loss_step=0.208]
Epoch 0:   1%|          | 273/24289 [01:37<2:22:16,  2.81it/s, v_num=xhkp, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 57, 24])
  Layer 1: torch.Size([1, 96, 114, 48])
  Layer 2: torch.Size([1, 72, 228, 96])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 273
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 274/24289 [01:37<2:22:11,  2.81it/s, v_num=xhkp, train_loss_step=0.140, train_metrics/recon_loss_step=0.140]
Epoch 0:   1%|          | 274/24289 [01:37<2:22:12,  2.81it/s, v_num=xhkp, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 274
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 275/24289 [01:37<2:22:01,  2.82it/s, v_num=xhkp, train_loss_step=0.153, train_metrics/recon_loss_step=0.153]
Epoch 0:   1%|          | 275/24289 [01:37<2:22:01,  2.82it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 55, 35])
  Layer 1: torch.Size([1, 96, 110, 70])
  Layer 2: torch.Size([1, 72, 220, 140])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 275
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 276/24289 [01:39<2:24:35,  2.77it/s, v_num=xhkp, train_loss_step=0.118, train_metrics/recon_loss_step=0.118]
Epoch 0:   1%|          | 276/24289 [01:39<2:24:35,  2.77it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 22])
  Layer 1: torch.Size([1, 96, 96, 44])
  Layer 2: torch.Size([1, 72, 192, 88])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 276
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 277/24289 [01:39<2:24:26,  2.77it/s, v_num=xhkp, train_loss_step=0.122, train_metrics/recon_loss_step=0.122]
Epoch 0:   1%|          | 277/24289 [01:39<2:24:26,  2.77it/s, v_num=xhkp, train_loss_step=0.143, train_metrics/recon_loss_step=0.143]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 21])
  Layer 1: torch.Size([1, 96, 128, 42])
  Layer 2: torch.Size([1, 72, 256, 84])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 277
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 278/24289 [01:40<2:24:21,  2.77it/s, v_num=xhkp, train_loss_step=0.143, train_metrics/recon_loss_step=0.143]
Epoch 0:   1%|          | 278/24289 [01:40<2:24:21,  2.77it/s, v_num=xhkp, train_loss_step=0.180, train_metrics/recon_loss_step=0.180]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 49, 15])
  Layer 1: torch.Size([1, 96, 98, 30])
  Layer 2: torch.Size([1, 72, 196, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 278
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 279/24289 [01:40<2:24:11,  2.78it/s, v_num=xhkp, train_loss_step=0.180, train_metrics/recon_loss_step=0.180]
Epoch 0:   1%|          | 279/24289 [01:40<2:24:11,  2.78it/s, v_num=xhkp, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 65, 18])
  Layer 1: torch.Size([1, 96, 130, 36])
  Layer 2: torch.Size([1, 72, 260, 72])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 279
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 280/24289 [01:40<2:24:16,  2.77it/s, v_num=xhkp, train_loss_step=0.121, train_metrics/recon_loss_step=0.121]
Epoch 0:   1%|          | 280/24289 [01:40<2:24:17,  2.77it/s, v_num=xhkp, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 31])
  Layer 1: torch.Size([1, 96, 128, 62])
  Layer 2: torch.Size([1, 72, 256, 124])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 280
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 281/24289 [01:41<2:24:22,  2.77it/s, v_num=xhkp, train_loss_step=0.190, train_metrics/recon_loss_step=0.190]
Epoch 0:   1%|          | 281/24289 [01:41<2:24:22,  2.77it/s, v_num=xhkp, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 281
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 282/24289 [01:41<2:24:12,  2.77it/s, v_num=xhkp, train_loss_step=0.111, train_metrics/recon_loss_step=0.111]
Epoch 0:   1%|          | 282/24289 [01:41<2:24:12,  2.77it/s, v_num=xhkp, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 282
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 283/24289 [01:41<2:24:02,  2.78it/s, v_num=xhkp, train_loss_step=0.113, train_metrics/recon_loss_step=0.113]
Epoch 0:   1%|          | 283/24289 [01:41<2:24:02,  2.78it/s, v_num=xhkp, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 15])
  Layer 1: torch.Size([1, 96, 96, 30])
  Layer 2: torch.Size([1, 72, 192, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 283
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 284/24289 [01:42<2:23:53,  2.78it/s, v_num=xhkp, train_loss_step=0.132, train_metrics/recon_loss_step=0.132]
Epoch 0:   1%|          | 284/24289 [01:42<2:23:53,  2.78it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 29, 11])
  Layer 1: torch.Size([1, 96, 58, 22])
  Layer 2: torch.Size([1, 72, 116, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 284
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 285/24289 [01:42<2:23:43,  2.78it/s, v_num=xhkp, train_loss_step=0.106, train_metrics/recon_loss_step=0.106]
Epoch 0:   1%|          | 285/24289 [01:42<2:23:43,  2.78it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 15])
  Layer 1: torch.Size([1, 96, 56, 30])
  Layer 2: torch.Size([1, 72, 112, 60])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 285
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 286/24289 [01:42<2:23:34,  2.79it/s, v_num=xhkp, train_loss_step=0.156, train_metrics/recon_loss_step=0.156]
Epoch 0:   1%|          | 286/24289 [01:42<2:23:34,  2.79it/s, v_num=xhkp, train_loss_step=0.0641, train_metrics/recon_loss_step=0.0641]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 286
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 287/24289 [01:42<2:23:25,  2.79it/s, v_num=xhkp, train_loss_step=0.0641, train_metrics/recon_loss_step=0.0641]
Epoch 0:   1%|          | 287/24289 [01:42<2:23:25,  2.79it/s, v_num=xhkp, train_loss_step=0.0468, train_metrics/recon_loss_step=0.0468]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 64, 14])
  Layer 1: torch.Size([1, 96, 128, 28])
  Layer 2: torch.Size([1, 72, 256, 56])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 287
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 288/24289 [01:43<2:23:17,  2.79it/s, v_num=xhkp, train_loss_step=0.0468, train_metrics/recon_loss_step=0.0468]
Epoch 0:   1%|          | 288/24289 [01:43<2:23:17,  2.79it/s, v_num=xhkp, train_loss_step=0.0514, train_metrics/recon_loss_step=0.0514]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 288
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 289/24289 [01:43<2:23:10,  2.79it/s, v_num=xhkp, train_loss_step=0.0514, train_metrics/recon_loss_step=0.0514]
Epoch 0:   1%|          | 289/24289 [01:43<2:23:10,  2.79it/s, v_num=xhkp, train_loss_step=0.301, train_metrics/recon_loss_step=0.301]  DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 28, 11])
  Layer 1: torch.Size([1, 96, 56, 22])
  Layer 2: torch.Size([1, 72, 112, 44])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 289
Training phase: discriminator_pretrain

Epoch 0:   1%|          | 290/24289 [01:43<2:22:59,  2.80it/s, v_num=xhkp, train_loss_step=0.301, train_metrics/recon_loss_step=0.301]
Epoch 0:   1%|          | 290/24289 [01:43<2:22:59,  2.80it/s, v_num=xhkp, train_loss_step=0.150, train_metrics/recon_loss_step=0.150]DEBUG: Cascade 0 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 1 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 2 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 3 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 4 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Cascade 5 features:
  Layer 0: torch.Size([1, 120, 48, 19])
  Layer 1: torch.Size([1, 96, 96, 38])
  Layer 2: torch.Size([1, 72, 192, 76])
DEBUG: Training phase: discriminator_pretrain, Epoch: 0
DEBUG: Number of cascade features: 6
Error in domain adaptation: 'list' object has no attribute 'shape'
Traceback:
Current epoch: 0, batch_idx: 290
Training phase: discriminator_pretrain
