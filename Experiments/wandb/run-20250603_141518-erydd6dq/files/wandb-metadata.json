{"os": "Linux-4.18.0-348.el8.x86_64-x86_64-with-glibc2.28", "python": "CPython 3.10.16", "startedAt": "2025-06-03T21:15:18.953775Z", "args": ["fit", "--config", "configs/train/pmr-plus/cmr25-cardiac-task3_30to15.yaml", "--trainer.max_epochs=50", "--data.init_args.batch_size=1", "--trainer.devices=4", "--trainer.logger.init_args.project=cmr2025_task3", "--trainer.logger.init_args.tags=[baseline,promptmr_plus,comparison]", "--trainer.logger.init_args.name=pmr_plus_baseline_comparison-15to30-db", "--trainer.logger.init_args.log_model=all", "--trainer.logger.init_args.offline=false"], "program": "/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/main.py", "codePath": "main.py", "email": "<EMAIL>", "root": "/common/lidxxlab/cmrchallenge/task3/Experiments", "host": "esplhpc-cp077", "executable": "/home/<USER>/miniconda3/envs/cmrrecon-task3/bin/python", "codePathLocal": "main.py", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA L40S", "gpu_count": 4, "disk": {"/": {"total": "462588203008", "used": "15476879360"}}, "memory": {"total": "1081925943296"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}], "slurm": {"cluster_name": "slurm-compbio", "conf": "/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf", "cpu_bind": "quiet,mask_cpu:0x0000000000000FFF", "cpu_bind_list": "0x0000000000000FFF", "cpu_bind_type": "mask_cpu:", "cpu_bind_verbose": "quiet", "cpus_on_node": "12", "cpus_per_task": "12", "distribution": "cyclic", "gpus_on_node": "4", "gtids": "0", "job_account": "user", "job_cpus_per_node": "12", "job_end_time": "**********", "job_gid": "23023", "job_gpus": "0,1,2,3", "job_id": "1620964", "job_name": "cmr_baseline", "job_nodelist": "esplhpc-cp077", "job_num_nodes": "1", "job_partition": "gpu", "job_qos": "normal", "job_start_time": "**********", "job_uid": "1235884", "job_user": "zengl2", "jobid": "1620964", "launch_node_ipaddr": "************", "localid": "0", "mem_per_node": "204800", "nnodes": "1", "nodeid": "0", "nodelist": "esplhpc-cp077", "nprocs": "1", "ntasks": "1", "prio_process": "0", "procid": "0", "srun_comm_host": "************", "srun_comm_port": "36925", "step_gpus": "0,1,2,3", "step_id": "0", "step_launcher_port": "36925", "step_nodelist": "esplhpc-cp077", "step_num_nodes": "1", "step_num_tasks": "1", "step_tasks_per_node": "1", "stepid": "0", "submit_dir": "/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/scripts", "submit_host": "esplhpccompbio-lv03", "task_pid": "904305", "tasks_per_node": "1", "topology_addr": "esplhpc-cp077", "topology_addr_pattern": "node", "tres_per_task": "cpu:12", "umask": "0022"}, "cudaVersion": "12.4"}