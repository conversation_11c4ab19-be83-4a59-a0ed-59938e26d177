async-timeout==5.0.1
Werkzeug==3.1.3
annotated-types==0.7.0
setuptools==72.1.0
imageio==2.37.0
multidict==6.4.3
certifi==2025.1.31
einops==0.8.1
nvidia-cudnn-cu11==********
pydantic_core==2.33.1
nvidia-nvtx-cu12==12.4.127
pydantic==2.11.3
aiohttp==3.11.16
torch==2.6.0+cu118
torchmetrics==1.7.1
nvidia-cuda-runtime-cu12==12.4.127
nvidia-curand-cu11==*********
pillow==11.1.0
sympy==1.13.1
lazy_loader==0.4
attrs==25.3.0
typing_extensions==4.12.2
h5py==3.13.0
nvidia-cusparse-cu11==*********
mkl-service==2.4.0
nvidia-cublas-cu11==*********
aiohappyeyeballs==2.6.1
packaging==24.2
python-dateutil==2.9.0.post0
nvidia-cusparselt-cu12==0.6.2
PySocks==1.7.1
fsspec==2025.3.2
nvidia-nvtx-cu11==11.8.86
nvidia-nccl-cu12==2.21.5
mkl_fft==1.3.11
tensorboard-data-server==0.7.2
pip==25.0
nvidia-cuda-runtime-cu11==11.8.89
Jinja2==3.1.6
urllib3==2.3.0
MarkupSafe==3.0.2
lightning==2.5.1
nvidia-cudnn-cu12==********
pyparsing==3.2.3
protobuf==5.29.4
kiwisolver==1.4.8
torchvision==0.21.0+cu118
nvidia-cusolver-cu12==********
jsonargparse==4.38.0
platformdirs==4.3.7
Brotli==1.0.9
idna==3.7
nvidia-cuda-cupti-cu11==11.8.87
wheel==0.45.1
nvidia-cusolver-cu11==*********
grpcio==1.71.0
docker-pycreds==0.4.0
torchaudio==2.6.0+cu118
absl-py==2.2.2
nvidia-cuda-cupti-cu12==12.4.127
six==1.17.0
mpmath==1.3.0
nvidia-nccl-cu11==2.21.5
nvidia-cufft-cu11==*********
mkl_random==1.2.8
pytorch-lightning==2.5.1
Markdown==3.8
gitdb==4.0.12
wandb==0.19.9
aiosignal==1.3.2
fonttools==4.57.0
psutil==7.0.0
frozenlist==1.5.0
tensorboard==2.19.0
PyYAML==6.0.2
propcache==0.3.1
requests==2.32.3
lightning-utilities==0.14.3
typeshed_client==2.7.0
smmap==5.0.2
nvidia-nvjitlink-cu12==12.4.127
tqdm==4.67.1
nvidia-curand-cu12==**********
charset-normalizer==3.3.2
docstring_parser==0.16
nvidia-cuda-nvrtc-cu11==11.8.89
filelock==3.18.0
scipy==1.15.2
nvidia-cufft-cu12==********
contourpy==1.3.2
networkx==3.4.2
importlib_resources==6.5.2
scikit-image==0.25.2
nvidia-cusparse-cu12==**********
GitPython==3.1.44
matplotlib==3.10.1
setproctitle==1.3.5
cycler==0.12.1
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cublas-cu12==********
typing-inspection==0.4.0
sentry-sdk==2.26.1
triton==3.2.0
yarl==1.19.0
numpy==2.2.4
tifffile==2025.3.30
click==8.1.8
jaraco.context==5.3.0
tomli==2.0.1
jaraco.text==3.12.1
ordered-set==4.1.0
wheel==0.43.0
typing_extensions==4.12.2
importlib_resources==6.4.0
packaging==24.1
platformdirs==4.2.2
autocommand==2.2.2
jaraco.functools==4.0.1
inflect==7.3.1
typeguard==4.3.0
backports.tarfile==1.2.0
more-itertools==10.3.0
zipp==3.19.2
importlib_metadata==8.0.0
