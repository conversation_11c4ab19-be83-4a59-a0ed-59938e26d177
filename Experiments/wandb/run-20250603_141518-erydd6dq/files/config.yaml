_instantiator:
    value: lightning.pytorch.cli.instantiate_module
_wandb:
    value:
        cli_version: 0.19.9
        m:
            - "1": val_images_idx_798.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": trainer/global_step
              "6":
                - 3
              "7": []
            - "1": val_images_idx_798.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/nmse
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/ssim
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_798.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_798._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_798.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_798.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1347.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": validation_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_824.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1435._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1084.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_233.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_798.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1169._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/psnr
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_736.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1471.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_416.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1345.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_741.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_777.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_173.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_111.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_270._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.10.16
        t:
            "1":
                - 1
                - 41
                - 55
                - 106
            "2":
                - 1
                - 41
                - 55
                - 106
            "3":
                - 7
                - 13
                - 15
                - 18
                - 23
                - 55
                - 66
            "4": 3.10.16
            "5": 0.19.9
            "8":
                - 5
            "12": 0.19.9
            "13": linux-x86_64
adaptive_input:
    value: true
compute_sens_per_coil:
    value: false
feature_dim:
    value:
        - 72
        - 96
        - 120
learnable_prompt:
    value: false
len_prompt:
    value:
        - 5
        - 5
        - 5
lr:
    value: 0.0002
lr_gamma:
    value: 0.1
lr_step_size:
    value: 11
model_version:
    value: promptmr_v2
n_bottleneck_cab:
    value: 3
n_buffer:
    value: 4
n_dec_cab:
    value:
        - 2
        - 2
        - 3
n_enc_cab:
    value:
        - 2
        - 3
        - 3
n_feat0:
    value: 48
n_history:
    value: 11
n_skip_cab:
    value:
        - 1
        - 1
        - 1
no_use_ca:
    value: false
num_adj_slices:
    value: 5
num_cascades:
    value: 12
num_log_images:
    value: 16
pretrain:
    value: false
pretrain_weights_path:
    value: null
prompt_dim:
    value:
        - 24
        - 48
        - 72
prompt_size:
    value:
        - 64
        - 32
        - 16
sens_feature_dim:
    value:
        - 36
        - 48
        - 60
sens_n_feat0:
    value: 24
sens_prompt_dim:
    value:
        - 12
        - 24
        - 36
use_checkpoint:
    value: false
use_sens_adj:
    value: true
weight_decay:
    value: 0.01
