Configuration saved to /common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/config.yaml
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 92.9 M | train
7 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
82.5 M    Trainable params
10.4 M    Non-trainable params
92.9 M    Total params
371.436   Total estimated model params size (MB)
4547      Modules in train mode
0         Modules in eval mode
SLURM auto-requeueing enabled. Setting signal handlers.
Epoch 15:  34%|███▎      | 4068/12067 [31:27<1:01:51,  2.15it/s, v_num=d6dq, train_loss=0.0827, validation_loss=0.0585]   
                                                                            
Epoch 0, global step 12067: 'validation_loss' reached 0.06853 (best 0.06853), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/checkpoints/best-epochepoch=00-valvalidation_loss=0.0685.ckpt' as top 1
Epoch 1, global step 24134: 'validation_loss' reached 0.06348 (best 0.06348), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/checkpoints/best-epochepoch=01-valvalidation_loss=0.0635.ckpt' as top 1
Epoch 2, global step 36201: 'validation_loss' reached 0.06084 (best 0.06084), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/checkpoints/best-epochepoch=02-valvalidation_loss=0.0608.ckpt' as top 1
Epoch 3, global step 48268: 'validation_loss' was not in top 1
Epoch 4, global step 60335: 'validation_loss' was not in top 1
Epoch 5, global step 72402: 'validation_loss' was not in top 1
Epoch 6, global step 84469: 'validation_loss' reached 0.05962 (best 0.05962), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/checkpoints/best-epochepoch=06-valvalidation_loss=0.0596.ckpt' as top 1
Epoch 7, global step 96536: 'validation_loss' was not in top 1
Epoch 8, global step 108603: 'validation_loss' was not in top 1
Epoch 9, global step 120670: 'validation_loss' was not in top 1
Epoch 10, global step 132737: 'validation_loss' was not in top 1
Epoch 11, global step 144804: 'validation_loss' was not in top 1
Epoch 12, global step 156871: 'validation_loss' was not in top 1
Epoch 13, global step 168938: 'validation_loss' was not in top 1
Epoch 14, global step 181005: 'validation_loss' reached 0.05848 (best 0.05848), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/erydd6dq/checkpoints/best-epochepoch=14-valvalidation_loss=0.0585.ckpt' as top 1
[rank: 0] Received SIGTERM: 15
Bypassing SIGTERM: 15
