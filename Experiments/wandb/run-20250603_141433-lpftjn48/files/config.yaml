_instantiator:
    value: lightning.pytorch.cli.instantiate_module
_wandb:
    value:
        cli_version: 0.19.9
        m:
            - "1": val_images_idx_305.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": trainer/global_step
              "6":
                - 3
              "7": []
            - "1": val_images_idx_1343.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/nmse
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1226.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1553.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_305.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/psnr
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_420.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_212.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1922.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/ssim
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_331.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": validation_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1343.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_482.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_650.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1509.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_471.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.10.16
        t:
            "1":
                - 1
                - 41
                - 55
                - 106
            "2":
                - 1
                - 41
                - 55
                - 106
            "3":
                - 7
                - 13
                - 15
                - 18
                - 23
                - 55
                - 66
            "4": 3.10.16
            "5": 0.19.9
            "8":
                - 5
            "12": 0.19.9
            "13": linux-x86_64
adaptive_input:
    value: true
compute_sens_per_coil:
    value: false
feature_dim:
    value:
        - 72
        - 96
        - 120
learnable_prompt:
    value: false
len_prompt:
    value:
        - 5
        - 5
        - 5
lr:
    value: 0.0002
lr_gamma:
    value: 0.1
lr_step_size:
    value: 11
model_version:
    value: promptmr_v2
n_bottleneck_cab:
    value: 3
n_buffer:
    value: 4
n_dec_cab:
    value:
        - 2
        - 2
        - 3
n_enc_cab:
    value:
        - 2
        - 3
        - 3
n_feat0:
    value: 48
n_history:
    value: 11
n_skip_cab:
    value:
        - 1
        - 1
        - 1
no_use_ca:
    value: false
num_adj_slices:
    value: 5
num_cascades:
    value: 12
num_log_images:
    value: 16
pretrain:
    value: false
pretrain_weights_path:
    value: null
prompt_dim:
    value:
        - 24
        - 48
        - 72
prompt_size:
    value:
        - 64
        - 32
        - 16
sens_feature_dim:
    value:
        - 36
        - 48
        - 60
sens_n_feat0:
    value: 24
sens_prompt_dim:
    value:
        - 12
        - 24
        - 36
use_checkpoint:
    value: false
use_sens_adj:
    value: true
weight_decay:
    value: 0.01
