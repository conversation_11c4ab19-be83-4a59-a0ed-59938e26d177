Configuration saved to /common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/config.yaml
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 92.9 M | train
7 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
82.5 M    Trainable params
10.4 M    Non-trainable params
92.9 M    Total params
371.436   Total estimated model params size (MB)
4547      Modules in train mode
0         Modules in eval mode
SLURM auto-requeueing enabled. Setting signal handlers.
Epoch 30: 100%|██████████| 12222/12222 [1:47:12<00:00,  1.90it/s, v_num=jn48, train_loss=0.017, validation_loss=0.0328]    
                                                                            
Epoch 0, global step 12222: 'validation_loss' reached 0.04564 (best 0.04564), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=00-valvalidation_loss=0.0456.ckpt' as top 1
Epoch 1, global step 24444: 'validation_loss' was not in top 1
Epoch 2, global step 36666: 'validation_loss' reached 0.03734 (best 0.03734), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=02-valvalidation_loss=0.0373.ckpt' as top 1
Epoch 3, global step 48888: 'validation_loss' was not in top 1
Epoch 4, global step 61110: 'validation_loss' reached 0.03725 (best 0.03725), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=04-valvalidation_loss=0.0373.ckpt' as top 1
Epoch 5, global step 73332: 'validation_loss' reached 0.03517 (best 0.03517), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=05-valvalidation_loss=0.0352.ckpt' as top 1
Epoch 6, global step 85554: 'validation_loss' reached 0.03368 (best 0.03368), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=06-valvalidation_loss=0.0337.ckpt' as top 1
Epoch 7, global step 97776: 'validation_loss' was not in top 1
Epoch 8, global step 109998: 'validation_loss' reached 0.03325 (best 0.03325), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=08-valvalidation_loss=0.0333.ckpt' as top 1
Epoch 9, global step 122220: 'validation_loss' was not in top 1
Epoch 10, global step 134442: 'validation_loss' reached 0.03324 (best 0.03324), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=10-valvalidation_loss=0.0332.ckpt' as top 1
Epoch 11, global step 146664: 'validation_loss' reached 0.03068 (best 0.03068), saving model to '/common/lidxxlab/cmrchallenge/task3/Experiments/cmr2025_task3/lpftjn48/checkpoints/best-epochepoch=11-valvalidation_loss=0.0307.ckpt' as top 1
Epoch 12, global step 158886: 'validation_loss' was not in top 1
Epoch 13, global step 171108: 'validation_loss' was not in top 1
Epoch 14, global step 183330: 'validation_loss' was not in top 1
Epoch 15, global step 195552: 'validation_loss' was not in top 1
Epoch 16, global step 207774: 'validation_loss' was not in top 1
Epoch 17, global step 219996: 'validation_loss' was not in top 1
Epoch 18, global step 232218: 'validation_loss' was not in top 1
Epoch 19, global step 244440: 'validation_loss' was not in top 1
Epoch 20, global step 256662: 'validation_loss' was not in top 1
Epoch 21, global step 268884: 'validation_loss' was not in top 1
Epoch 22, global step 281106: 'validation_loss' was not in top 1
Epoch 23, global step 293328: 'validation_loss' was not in top 1
Epoch 24, global step 305550: 'validation_loss' was not in top 1
Epoch 25, global step 317772: 'validation_loss' was not in top 1
Epoch 26, global step 329994: 'validation_loss' was not in top 1
Epoch 27, global step 342216: 'validation_loss' was not in top 1
Epoch 28, global step 354438: 'validation_loss' was not in top 1
Epoch 29, global step 366660: 'validation_loss' was not in top 1
Epoch 30, global step 378882: 'validation_loss' was not in top 1
Epoch 31, global step 391104: 'validation_loss' was not in top 1
Epoch 32, global step 403326: 'validation_loss' was not in top 1
Epoch 33, global step 415548: 'validation_loss' was not in top 1
Epoch 34, global step 427770: 'validation_loss' was not in top 1
Epoch 35, global step 439992: 'validation_loss' was not in top 1
Epoch 36, global step 452214: 'validation_loss' was not in top 1
Epoch 37, global step 464436: 'validation_loss' was not in top 1
Epoch 38, global step 476658: 'validation_loss' was not in top 1
Epoch 39, global step 488880: 'validation_loss' was not in top 1
Epoch 40, global step 501102: 'validation_loss' was not in top 1
Epoch 41, global step 513324: 'validation_loss' was not in top 1
Epoch 42, global step 525546: 'validation_loss' was not in top 1
Epoch 43, global step 537768: 'validation_loss' was not in top 1
Epoch 44, global step 549990: 'validation_loss' was not in top 1
Epoch 45, global step 562212: 'validation_loss' was not in top 1
Epoch 46, global step 574434: 'validation_loss' was not in top 1
Epoch 47, global step 586656: 'validation_loss' was not in top 1
Epoch 48, global step 598878: 'validation_loss' was not in top 1
Epoch 49, global step 611100: 'validation_loss' was not in top 1
`Trainer.fit` stopped: `max_epochs=50` reached.
