#!/usr/bin/env python3
"""
Real-time layer analysis script that can be run during training to quickly assess
which cascade layers are most informative for field strength discrimination.
"""

import torch
import torch.nn as nn
import numpy as np
import os
import sys
from collections import defaultdict

# Add the project root to the path
sys.path.append('/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3')

from models.domain_adapt_utils import extract_field_strength_from_path


class RealTimeLayerAnalyzer:
    """Real-time analyzer for cascade layer features."""
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.layer_stats = defaultdict(list)
        self.field_strengths = []
        
        # Register hooks
        self._register_hooks()
    
    def _register_hooks(self):
        """Register hooks to capture features from each cascade layer."""
        def get_features_hook(layer_idx):
            def hook(module, input, output):
                if output is not None:
                    # Calculate basic statistics
                    with torch.no_grad():
                        mean_val = output.mean().item()
                        std_val = output.std().item()
                        min_val = output.min().item()
                        max_val = output.max().item()
                        
                        self.layer_stats[layer_idx].append({
                            'mean': mean_val,
                            'std': std_val,
                            'min': min_val,
                            'max': max_val,
                            'shape': list(output.shape)
                        })
            return hook
        
        # Register hooks for each cascade block
        for i, cascade_block in enumerate(self.model.promptmr.cascade_blocks):
            cascade_block.register_forward_hook(get_features_hook(i))
    
    def analyze_batch(self, batch, batch_idx):
        """Analyze a single batch and extract field strength information."""
        # Extract field strength from file paths
        if hasattr(batch, 'fname'):
            for fname in batch['fname']:
                field_strength = extract_field_strength_from_path(fname)
                self.field_strengths.append(field_strength)
        
        # Forward pass (this will trigger hooks and collect stats)
        with torch.no_grad():
            _ = self.model(batch['masked_kspace'], batch['mask'], 
                         batch['num_low_frequencies'], batch['mask_type'])
    
    def get_layer_analysis(self):
        """Get analysis results for all layers."""
        if not self.layer_stats:
            return None
        
        analysis = {}
        
        for layer_idx in range(6):
            if layer_idx not in self.layer_stats:
                continue
            
            stats = self.layer_stats[layer_idx]
            
            # Calculate aggregate statistics
            means = [s['mean'] for s in stats]
            stds = [s['std'] for s in stats]
            mins = [s['min'] for s in stats]
            maxs = [s['max'] for s in stats]
            
            analysis[layer_idx] = {
                'sample_count': len(stats),
                'avg_mean': np.mean(means),
                'avg_std': np.mean(stds),
                'avg_min': np.mean(mins),
                'avg_max': np.mean(maxs),
                'mean_range': np.mean(maxs) - np.mean(mins),
                'std_range': np.std(means),  # How much the mean varies across samples
                'shape': stats[0]['shape'] if stats else None
            }
        
        return analysis
    
    def get_field_strength_distribution(self):
        """Get field strength distribution."""
        if not self.field_strengths:
            return None
        
        field_strengths = np.array(self.field_strengths)
        return {
            'total_samples': len(field_strengths),
            '1.5T_count': (field_strengths == 1.5).sum(),
            '3.0T_count': (field_strengths == 3.0).sum(),
            '1.5T_ratio': (field_strengths == 1.5).sum() / len(field_strengths),
            '3.0T_ratio': (field_strengths == 3.0).sum() / len(field_strengths)
        }
    
    def print_analysis(self):
        """Print the analysis results."""
        layer_analysis = self.get_layer_analysis()
        field_dist = self.get_field_strength_distribution()
        
        if not layer_analysis:
            print("No layer analysis data available")
            return
        
        print("\n" + "="*60)
        print("REAL-TIME LAYER ANALYSIS")
        print("="*60)
        
        # Print field strength distribution
        if field_dist:
            print(f"\nField Strength Distribution:")
            print(f"  Total samples: {field_dist['total_samples']}")
            print(f"  1.5T: {field_dist['1.5T_count']} ({field_dist['1.5T_ratio']:.2%})")
            print(f"  3.0T: {field_dist['3.0T_count']} ({field_dist['3.0T_ratio']:.2%})")
        
        # Print layer analysis
        print(f"\nLayer Analysis:")
        print(f"{'Layer':<6} {'Samples':<8} {'Mean':<10} {'Std':<10} {'Range':<10} {'Var':<10}")
        print("-" * 60)
        
        layer_scores = {}
        
        for layer_idx in range(6):
            if layer_idx not in layer_analysis:
                continue
            
            analysis = layer_analysis[layer_idx]
            
            # Calculate a simple score based on feature variability
            # Higher std_range suggests more discriminative features
            score = analysis['std_range'] * analysis['mean_range']
            layer_scores[layer_idx] = score
            
            print(f"{layer_idx:<6} {analysis['sample_count']:<8} "
                  f"{analysis['avg_mean']:<10.4f} {analysis['avg_std']:<10.4f} "
                  f"{analysis['mean_range']:<10.4f} {analysis['std_range']:<10.4f}")
        
        # Print recommendations
        if layer_scores:
            sorted_layers = sorted(layer_scores.items(), key=lambda x: x[1], reverse=True)
            
            print(f"\nLayer Ranking (by discriminative power):")
            for i, (layer_idx, score) in enumerate(sorted_layers):
                print(f"  {i+1}. Layer {layer_idx}: {score:.6f}")
            
            print(f"\nRecommended feature_extract_layers:")
            print(f"  Top 3: {[layer for layer, _ in sorted_layers[:3]]}")
            print(f"  Top 4: {[layer for layer, _ in sorted_layers[:4]]}")
            print(f"  All: {[layer for layer, _ in sorted_layers]}")
        
        print("="*60)
        
        return layer_scores


def integrate_with_training_module():
    """Show how to integrate this analyzer with the training module."""
    
    # This would be added to the DomainAdaptMRModule class
    integration_code = '''
    class DomainAdaptMRModule(PromptMrModule):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            # Add analyzer
            self.layer_analyzer = RealTimeLayerAnalyzer(self, self.device)
        
        def training_step(self, batch, batch_idx):
            # Existing training code...
            
            # Add layer analysis every N steps
            if batch_idx % 100 == 0:  # Analyze every 100 steps
                self.layer_analyzer.analyze_batch(batch, batch_idx)
                
                # Print analysis every 500 steps
                if batch_idx % 500 == 0:
                    self.layer_analyzer.print_analysis()
            
            # Rest of training code...
    '''
    
    print("Integration code:")
    print(integration_code)


def quick_test():
    """Quick test function to demonstrate the analyzer."""
    print("Real-time Layer Analyzer Test")
    print("="*40)
    
    # This would be used during training
    print("""
    Usage during training:
    
    1. Initialize analyzer in your training module:
       self.layer_analyzer = RealTimeLayerAnalyzer(self, self.device)
    
    2. Call analyze_batch in training_step:
       self.layer_analyzer.analyze_batch(batch, batch_idx)
    
    3. Print analysis periodically:
       if batch_idx % 500 == 0:
           self.layer_analyzer.print_analysis()
    
    This will help you identify which layers are most informative
    for field strength discrimination in real-time.
    """)


if __name__ == "__main__":
    quick_test()
    integrate_with_training_module() 