#!/usr/bin/env python3
"""
Analyze the correlation between different cascade layer features and field strength.
This script helps identify which layers are most informative for field strength discrimination.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.metrics import mutual_info_score
import wandb
import os
from pathlib import Path

# Add the project root to the path
import sys
sys.path.append('/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3')

from pl_modules.DomainAdaptMRModule import DomainAdaptMRModule
from data.mri_data import CmrxReconSliceDataset
from data.mri_data import CmrxReconDataTransform
from data.mri_data import CmrxRecon25MaskFunc
from torch.utils.data import DataLoader
from models.domain_adapt_utils import extract_field_strength_from_path


class FeatureAnalyzer:
    """Analyze features from different cascade layers for field strength correlation."""
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.features_by_layer = {i: [] for i in range(6)}  # 6 cascade layers
        self.field_strengths = []
        self.file_paths = []
        
        # Register hooks to capture features
        self._register_hooks()
    
    def _register_hooks(self):
        """Register hooks to capture features from each cascade layer."""
        def get_features_hook(layer_idx):
            def hook(module, input, output):
                if output is not None:
                    # Store features for this layer
                    self.features_by_layer[layer_idx].append(output.detach().cpu())
            return hook
        
        # Register hooks for each cascade block
        for i, cascade_block in enumerate(self.model.promptmr.cascade_blocks):
            cascade_block.register_forward_hook(get_features_hook(i))
    
    def collect_features(self, dataloader, max_samples=1000):
        """Collect features from multiple samples."""
        print(f"Collecting features from up to {max_samples} samples...")
        
        self.model.eval()
        sample_count = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(dataloader):
                if sample_count >= max_samples:
                    break
                
                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # Clear previous features
                for layer_idx in self.features_by_layer:
                    self.features_by_layer[layer_idx] = []
                
                # Forward pass (this will trigger hooks)
                _ = self.model(batch['masked_kspace'], batch['mask'], 
                             batch['num_low_frequencies'], batch['mask_type'])
                
                # Extract field strength from file paths
                if hasattr(batch, 'fname'):
                    for fname in batch['fname']:
                        field_strength = extract_field_strength_from_path(fname)
                        self.field_strengths.append(field_strength)
                        self.file_paths.append(fname)
                
                # Store features for each layer
                for layer_idx in range(6):
                    if self.features_by_layer[layer_idx]:
                        # Take the first sample's features
                        features = self.features_by_layer[layer_idx][0]
                        self.features_by_layer[layer_idx].append(features)
                
                sample_count += 1
                
                if sample_count % 100 == 0:
                    print(f"Processed {sample_count} samples...")
        
        print(f"Collected features from {sample_count} samples")
    
    def analyze_feature_statistics(self):
        """Analyze basic statistics of features for each layer."""
        print("\n=== Feature Statistics Analysis ===")
        
        for layer_idx in range(6):
            if not self.features_by_layer[layer_idx]:
                continue
                
            features = torch.stack(self.features_by_layer[layer_idx])
            
            # Basic statistics
            mean_val = features.mean().item()
            std_val = features.std().item()
            min_val = features.min().item()
            max_val = features.max().item()
            
            print(f"Layer {layer_idx}:")
            print(f"  Shape: {features.shape}")
            print(f"  Mean: {mean_val:.4f}")
            print(f"  Std: {std_val:.4f}")
            print(f"  Range: [{min_val:.4f}, {max_val:.4f}]")
            
            # Check for NaN or Inf
            if torch.isnan(features).any():
                print(f"  WARNING: Contains NaN values!")
            if torch.isinf(features).any():
                print(f"  WARNING: Contains Inf values!")
    
    def analyze_field_strength_correlation(self):
        """Analyze correlation between features and field strength."""
        print("\n=== Field Strength Correlation Analysis ===")
        
        field_strengths = np.array(self.field_strengths)
        unique_strengths = np.unique(field_strengths)
        print(f"Field strengths in dataset: {unique_strengths}")
        print(f"1.5T samples: {(field_strengths == 1.5).sum()}")
        print(f"3.0T samples: {(field_strengths == 3.0).sum()}")
        
        correlation_scores = {}
        
        for layer_idx in range(6):
            if not self.features_by_layer[layer_idx]:
                continue
                
            features = torch.stack(self.features_by_layer[layer_idx])
            
            # Flatten features for correlation analysis
            features_flat = features.view(features.shape[0], -1)
            
            # Calculate correlation for each feature dimension
            correlations = []
            for feat_idx in range(features_flat.shape[1]):
                if feat_idx < 100:  # Sample first 100 features to avoid memory issues
                    corr = np.corrcoef(features_flat[:, feat_idx].numpy(), field_strengths)[0, 1]
                    if not np.isnan(corr):
                        correlations.append(abs(corr))
            
            if correlations:
                mean_corr = np.mean(correlations)
                max_corr = np.max(correlations)
                correlation_scores[layer_idx] = {
                    'mean_correlation': mean_corr,
                    'max_correlation': max_corr,
                    'high_corr_features': sum(1 for c in correlations if c > 0.3)
                }
                
                print(f"Layer {layer_idx}:")
                print(f"  Mean absolute correlation: {mean_corr:.4f}")
                print(f"  Max absolute correlation: {max_corr:.4f}")
                print(f"  Features with high correlation (>0.3): {correlation_scores[layer_idx]['high_corr_features']}")
        
        return correlation_scores
    
    def visualize_feature_distributions(self, save_dir="feature_analysis"):
        """Visualize feature distributions for different field strengths."""
        os.makedirs(save_dir, exist_ok=True)
        
        print("\n=== Creating Feature Distribution Visualizations ===")
        
        for layer_idx in range(6):
            if not self.features_by_layer[layer_idx]:
                continue
                
            features = torch.stack(self.features_by_layer[layer_idx])
            features_flat = features.view(features.shape[0], -1)
            
            # Sample a few feature dimensions for visualization
            n_features_to_plot = min(16, features_flat.shape[1])
            feature_indices = np.random.choice(features_flat.shape[1], n_features_to_plot, replace=False)
            
            fig, axes = plt.subplots(4, 4, figsize=(16, 12))
            fig.suptitle(f'Feature Distributions - Layer {layer_idx}', fontsize=16)
            
            for i, feat_idx in enumerate(feature_indices):
                row, col = i // 4, i % 4
                
                # Get feature values for each field strength
                feat_15t = features_flat[field_strengths == 1.5, feat_idx].numpy()
                feat_30t = features_flat[field_strengths == 3.0, feat_idx].numpy()
                
                # Plot distributions
                axes[row, col].hist(feat_15t, alpha=0.7, label='1.5T', bins=20, color='blue')
                axes[row, col].hist(feat_30t, alpha=0.7, label='3.0T', bins=20, color='red')
                axes[row, col].set_title(f'Feature {feat_idx}')
                axes[row, col].legend()
                axes[row, col].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(f"{save_dir}/layer_{layer_idx}_distributions.png", dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"Saved distribution plot for layer {layer_idx}")
    
    def perform_tsne_analysis(self, save_dir="feature_analysis"):
        """Perform t-SNE analysis to visualize feature separability."""
        print("\n=== Performing t-SNE Analysis ===")
        
        for layer_idx in range(6):
            if not self.features_by_layer[layer_idx]:
                continue
                
            features = torch.stack(self.features_by_layer[layer_idx])
            features_flat = features.view(features.shape[0], -1)
            
            # Use PCA for dimensionality reduction before t-SNE
            pca = PCA(n_components=50)
            features_pca = pca.fit_transform(features_flat.numpy())
            
            # Perform t-SNE
            tsne = TSNE(n_components=2, random_state=42, perplexity=30)
            features_tsne = tsne.fit_transform(features_pca)
            
            # Create visualization
            plt.figure(figsize=(10, 8))
            
            # Plot 1.5T samples
            mask_15t = field_strengths == 1.5
            plt.scatter(features_tsne[mask_15t, 0], features_tsne[mask_15t, 1], 
                       c='blue', label='1.5T', alpha=0.7, s=30)
            
            # Plot 3.0T samples
            mask_30t = field_strengths == 3.0
            plt.scatter(features_tsne[mask_30t, 0], features_tsne[mask_30t, 1], 
                       c='red', label='3.0T', alpha=0.7, s=30)
            
            plt.title(f't-SNE Visualization - Layer {layer_idx}')
            plt.xlabel('t-SNE 1')
            plt.ylabel('t-SNE 2')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.savefig(f"{save_dir}/layer_{layer_idx}_tsne.png", dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"Saved t-SNE plot for layer {layer_idx}")
    
    def calculate_separability_score(self):
        """Calculate a separability score for each layer."""
        print("\n=== Separability Score Analysis ===")
        
        separability_scores = {}
        
        for layer_idx in range(6):
            if not self.features_by_layer[layer_idx]:
                continue
                
            features = torch.stack(self.features_by_layer[layer_idx])
            features_flat = features.view(features.shape[0], -1)
            
            # Calculate mean features for each field strength
            mask_15t = field_strengths == 1.5
            mask_30t = field_strengths == 3.0
            
            if mask_15t.sum() == 0 or mask_30t.sum() == 0:
                print(f"Layer {layer_idx}: Skipping - missing field strength data")
                continue
            
            mean_15t = features_flat[mask_15t].mean(dim=0)
            mean_30t = features_flat[mask_30t].mean(dim=0)
            
            # Calculate distance between means
            distance = torch.norm(mean_30t - mean_15t).item()
            
            # Calculate within-class variance
            var_15t = features_flat[mask_15t].var(dim=0).mean().item()
            var_30t = features_flat[mask_30t].var(dim=0).mean().item()
            avg_variance = (var_15t + var_30t) / 2
            
            # Fisher's discriminant ratio (simplified)
            separability = distance / (avg_variance + 1e-8)
            
            separability_scores[layer_idx] = separability
            
            print(f"Layer {layer_idx}:")
            print(f"  Distance between means: {distance:.4f}")
            print(f"  Average variance: {avg_variance:.4f}")
            print(f"  Separability score: {separability:.4f}")
        
        return separability_scores


def main():
    """Main analysis function."""
    print("Starting feature correlation analysis...")
    
    # Initialize model
    model = DomainAdaptMRModule(
        num_cascades=6,
        chans=48,
        feature_extract_layers=[0, 1, 2, 3, 4, 5],  # Use all layers for analysis
        discriminator_pretrain_epochs=5,
        discriminator_accuracy_threshold=0.65
    )
    
    # Load a pretrained checkpoint if available
    checkpoint_path = "logs/cmr2025_task3/latest_checkpoint.ckpt"
    if os.path.exists(checkpoint_path):
        print(f"Loading checkpoint from {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint['state_dict'])
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Initialize data
    mask_func = CmrxRecon25MaskFunc(
        num_low_frequencies=[16],
        num_adj_slices=5,
        mask_path="/common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5"
    )
    
    transform = CmrxReconDataTransform(
        mask_func=mask_func,
        uniform_resolution=None,
        use_seed=False
    )
    
    dataset = CmrxReconSliceDataset(
        root="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/train_15T_and_30T",
        challenge="multicoil",
        transform=transform
    )
    
    dataloader = DataLoader(dataset, batch_size=1, shuffle=True, num_workers=4)
    
    # Initialize analyzer
    analyzer = FeatureAnalyzer(model, device)
    
    # Collect features
    analyzer.collect_features(dataloader, max_samples=500)
    
    # Perform analyses
    analyzer.analyze_feature_statistics()
    correlation_scores = analyzer.analyze_field_strength_correlation()
    separability_scores = analyzer.calculate_separability_score()
    
    # Create visualizations
    analyzer.visualize_feature_distributions()
    analyzer.perform_tsne_analysis()
    
    # Print summary
    print("\n=== SUMMARY ===")
    print("Recommended feature layers based on analysis:")
    
    # Sort layers by separability score
    sorted_layers = sorted(separability_scores.items(), key=lambda x: x[1], reverse=True)
    
    for layer_idx, score in sorted_layers:
        corr_info = correlation_scores.get(layer_idx, {})
        print(f"Layer {layer_idx}: Separability={score:.4f}, "
              f"Mean Correlation={corr_info.get('mean_correlation', 0):.4f}")
    
    print(f"\nTop 3 recommended layers: {[layer for layer, _ in sorted_layers[:3]]}")


if __name__ == "__main__":
    main() 