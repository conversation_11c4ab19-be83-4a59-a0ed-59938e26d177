#!/bin/bash
#SBATCH --job-name=cmr_fixed_domain_adapt
#SBATCH --partition=gpu
#SBATCH --cpus-per-task=8
#SBATCH --mem=100G
#SBATCH --time=24:00:00         # 24 hours for fixed domain adaptation training
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/logs/cmr_fixed_domain_adapt_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/logs/cmr_fixed_domain_adapt_%j.err

# Working directory
WORKDIR=/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3
cd $WORKDIR

# Create a log directory
mkdir -p /common/lidxxlab/cmrchallenge/task3/logs

# Initialize conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: Print environment information
echo "Python path: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

export PYTHONPATH=$WORKDIR:$PYTHONPATH

# 设置wandb API密钥
export WANDB_API_KEY="****************************************"
wandb login $WANDB_API_KEY

# Use the fixed configuration with all improvements
CONFIG_FILE="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml"
MAX_EPOCHS=50   # Increased epochs for 24-hour training
BATCH_SIZE=1    # Keep batch size 1 for stability

echo "=== FIXED DOMAIN ADAPTATION TRAINING ==="
echo "Config: cmr25-cardiac-domain-adapt-fixed.yaml"
echo "Total epochs: $MAX_EPOCHS"
echo "Batch size per GPU: $BATCH_SIZE (effective: $(($BATCH_SIZE * 4)))"
echo "Model: 6 cascades, 48 channels"
echo "Domain: Hierarchical feature processing (global/mid/local)"
echo "Discriminator: BCE_with_logits for mixed precision compatibility"
echo "Accuracy: Fixed calculation for single field strength batches"
echo "Loss function: binary_cross_entropy_with_logits (stable)"
echo "Learning rate: 0.0003"
echo "Layer analysis: ENABLED (real-time feature analysis)"
echo "Discriminator checkpoint: Will save when accuracy >= 0.65"
echo "Training time: 24 hours"
echo "Fixed issues:"
echo "  - Mixed precision compatibility (BCE_with_logits)"
echo "  - Correct accuracy calculation for single field strength batches"
echo "  - Hierarchical feature processing"
echo "  - Stable accuracy with sliding average"
echo "=========================="

# Disable wandb model logging for speed
export WANDB_LOG_MODEL=false
export WANDB_WATCH=false

# Start training with fixed domain adaptation configuration
echo "Starting fixed domain adaptation training..."
srun python main.py fit \
    --config $CONFIG_FILE \
    --trainer.max_epochs=$MAX_EPOCHS \
    --data.init_args.batch_size=$BATCH_SIZE \
    --trainer.devices=4 \
    --trainer.logger.init_args.project="cmr2025_task3" \
    --trainer.logger.init_args.tags="[fixed_domain_adapt,hierarchical_features,BCE_with_logits,stable_accuracy]" \
    --trainer.logger.init_args.name="fixed_domain_adapt_$(date +%H%M)" \
    --trainer.logger.init_args.offline=false \
    --model.feature_extract_layers="[1, 3, 5]" \
    --model.enable_layer_analysis=true \
    --model.lr=0.0003 \
    --model.discriminator_pretrain_epochs=3 \
    --model.discriminator_accuracy_threshold=0.65

echo "Fixed domain adaptation training complete!"
echo "Check wandb project: cmr2025_task3"
echo "Check discriminator checkpoints: discriminator_checkpoints/"
echo "Key metrics to monitor:"
echo "  - discriminator_accuracy (should be stable, not 0/1 jumping)"
echo "  - training_phase (should switch to adversarial after epoch 3)"
echo "  - discriminator_loss (should decrease during pretrain)"
echo "  - domain_loss (should be stable, no NaN)"
echo "  - layer_analysis (real-time feature analysis every 500 steps)"
echo "  - discriminator_checkpoint/* (saved when threshold reached)" 