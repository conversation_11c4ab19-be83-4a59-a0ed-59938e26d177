#!/bin/bash
#SBATCH --job-name=cmr_layer_analysis
#SBATCH --partition=gpu
#SBATCH --cpus-per-task=8
#SBATCH --mem=100G
#SBATCH --time=24:00:00         # 24 hours for layer analysis training
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/logs/cmr_layer_analysis_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/logs/cmr_layer_analysis_%j.err

# Working directory
WORKDIR=/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3
cd $WORKDIR

# Create a log directory
mkdir -p /common/lidxxlab/cmrchallenge/task3/logs

# Initialize conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: Print environment information
echo "Python path: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

export PYTHONPATH=$WORKDIR:$PYTHONPATH

# 设置wandb API密钥
export WANDB_API_KEY="****************************************"
wandb login $WANDB_API_KEY

# Use the improved configuration with layer analysis
CONFIG_FILE="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml"
MAX_EPOCHS=15   # Use full 15 epochs as specified in config
BATCH_SIZE=1    # Keep batch size 1 for stability

echo "=== LAYER ANALYSIS DISCRIMINATOR TRAINING ==="
echo "Config: cmr25-cardiac-domain-adapt-fixed.yaml"
echo "Total epochs: $MAX_EPOCHS"
echo "Batch size per GPU: $BATCH_SIZE (effective: $(($BATCH_SIZE * 4)))"
echo "Model: 6 cascades, 48 channels"
echo "Domain: ALL feature layers [0,1,2,3,4,5], pretrain 5 epochs"
echo "Discriminator: Increased capacity (256 hidden dim, 3 layers)"
echo "Loss function: binary_cross_entropy (not with_logits)"
echo "Learning rate: 0.0003 (increased from 0.0002)"
echo "Layer analysis: ENABLED (real-time feature analysis)"
echo "Discriminator checkpoint: Will save when accuracy >= 0.65"
echo "Expected time: ~12-15 hours"
echo "=========================="

# Disable wandb model logging for speed
export WANDB_LOG_MODEL=false
export WANDB_WATCH=false

# Start training with layer analysis configuration
echo "Starting layer analysis discriminator training..."
srun python main.py fit \
    --config $CONFIG_FILE \
    --trainer.max_epochs=$MAX_EPOCHS \
    --data.init_args.batch_size=$BATCH_SIZE \
    --trainer.devices=4 \
    --trainer.logger.init_args.project="cmr2025_extended" \
    --trainer.logger.init_args.tags="[layer_analysis,improved,discriminator,domain_adaptation]" \
    --trainer.logger.init_args.name="layer_analysis_$(date +%H%M)" \
    --trainer.logger.init_args.offline=false \
    --model.feature_extract_layers="[0, 1, 2, 3, 4, 5]" \
    --model.enable_layer_analysis=true \
    --model.lr=0.0003 \
    --model.discriminator_pretrain_epochs=5 \
    --model.discriminator_accuracy_threshold=0.65

echo "Layer analysis discriminator training complete!"
echo "Check wandb project: cmr2025_extended"
echo "Check discriminator checkpoints: discriminator_checkpoints/"
echo "Key metrics to monitor:"
echo "  - discriminator_accuracy (should reach 0.65 in pretrain phase)"
echo "  - training_phase (should switch to adversarial after epoch 5)"
echo "  - discriminator_loss (should decrease during pretrain)"
echo "  - domain_loss (should be stable)"
echo "  - layer_analysis (real-time feature analysis every 500 steps)"
echo "  - discriminator_checkpoint/* (saved when threshold reached)" 