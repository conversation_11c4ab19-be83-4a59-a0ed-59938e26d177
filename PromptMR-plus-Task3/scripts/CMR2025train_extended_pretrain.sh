#!/bin/bash
#SBATCH --job-name=cmr_extended
#SBATCH --partition=gpu
#SBATCH --cpus-per-task=8
#SBATCH --mem=100G
#SBATCH --time=12:00:00         # 12 hours for extended training
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/logs/cmr_extended_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/logs/cmr_extended_%j.err

# Working directory
WORKDIR=/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3
cd $WORKDIR

# Create a log directory
mkdir -p /common/lidxxlab/cmrchallenge/task3/logs

# Initialize conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: Print environment information
echo "Python path: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

export PYTHONPATH=$WORKDIR:$PYTHONPATH

# 设置wandb API密钥
export WANDB_API_KEY="****************************************"
wandb login $WANDB_API_KEY

# Use the fixed configuration with extended pretrain
CONFIG_FILE="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml"
MAX_EPOCHS=15   # Use full 15 epochs as specified in config
BATCH_SIZE=1    # Keep batch size 1 for stability

echo "=== EXTENDED PRETRAIN DISCRIMINATOR TRAINING ==="
echo "Config: cmr25-cardiac-domain-adapt-fixed.yaml"
echo "Total epochs: $MAX_EPOCHS"
echo "Batch size per GPU: $BATCH_SIZE (effective: $(($BATCH_SIZE * 4)))"
echo "Model: 6 cascades, 48 channels"
echo "Domain: 2 feature layers [2,3], pretrain 5 epochs"
echo "Discriminator: Increased capacity (256 hidden dim, 3 layers)"
echo "Loss function: binary_cross_entropy (not with_logits)"
echo "Discriminator checkpoint: Will save when accuracy >= 0.65"
echo "Expected time: ~8-10 hours"
echo "=========================="

# Disable wandb model logging for speed
export WANDB_LOG_MODEL=false
export WANDB_WATCH=false

# Start training with extended configuration
echo "Starting extended pretrain discriminator training..."
srun python main.py fit \
    --config $CONFIG_FILE \
    --trainer.max_epochs=$MAX_EPOCHS \
    --data.init_args.batch_size=$BATCH_SIZE \
    --trainer.devices=4 \
    --trainer.logger.init_args.project="cmr2025_extended" \
    --trainer.logger.init_args.tags="[extended,pretrain,discriminator,domain_adaptation]" \
    --trainer.logger.init_args.name="extended_pretrain_$(date +%H%M)" \
    --trainer.logger.init_args.offline=false

echo "Extended pretrain discriminator training complete!"
echo "Check wandb project: cmr2025_extended"
echo "Check discriminator checkpoints: discriminator_checkpoints/"
echo "Key metrics to monitor:"
echo "  - discriminator_accuracy (should reach 0.65 in pretrain phase)"
echo "  - training_phase (should switch to adversarial after epoch 5)"
echo "  - discriminator_loss (should decrease during pretrain)"
echo "  - domain_loss (should be stable)"
echo "  - discriminator_checkpoint/* (saved when threshold reached)" 