#!/bin/bash
#SBATCH --job-name=cmr_fixed_disc
#SBATCH --partition=gpu
#SBATCH --cpus-per-task=8
#SBATCH --mem=100G
#SBATCH --time=8:00:00          # 8 hours for full training
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/logs/cmr_fixed_disc_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/logs/cmr_fixed_disc_%j.err

# Working directory
WORKDIR=/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3
cd $WORKDIR

# Create a log directory
mkdir -p /common/lidxxlab/cmrchallenge/task3/logs

# Initialize conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: Print environment information
echo "Python path: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

export PYTHONPATH=$WORKDIR:$PYTHONPATH

# 设置wandb API密钥
export WANDB_API_KEY="****************************************"
wandb login $WANDB_API_KEY

# Use the fixed configuration
CONFIG_FILE="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml"
MAX_EPOCHS=10   # Use full 10 epochs as specified in config
BATCH_SIZE=1    # Keep batch size 1 for stability

echo "=== FIXED DISCRIMINATOR TRAINING ==="
echo "Config: cmr25-cardiac-domain-adapt-fixed.yaml"
echo "Total epochs: $MAX_EPOCHS"
echo "Batch size per GPU: $BATCH_SIZE (effective: $(($BATCH_SIZE * 4)))"
echo "Model: 6 cascades, 48 channels"
echo "Domain: 2 feature layers [2,3], pretrain 2 epochs"
echo "Discriminator: Increased capacity (256 hidden dim, 3 layers)"
echo "Loss function: binary_cross_entropy (not with_logits)"
echo "Expected time: ~4-6 hours"
echo "=========================="

# Disable wandb model logging for speed
export WANDB_LOG_MODEL=false
export WANDB_WATCH=false

# Start training with fixed configuration
echo "Starting fixed discriminator training..."
srun python main.py fit \
    --config $CONFIG_FILE \
    --trainer.max_epochs=$MAX_EPOCHS \
    --data.init_args.batch_size=$BATCH_SIZE \
    --trainer.devices=4 \
    --trainer.logger.init_args.project="cmr2025_fixed" \
    --trainer.logger.init_args.tags="[fixed,discriminator,domain_adaptation]" \
    --trainer.logger.init_args.name="fixed_discriminator_$(date +%H%M)" \
    --trainer.logger.init_args.offline=false

echo "Fixed discriminator training complete!"
echo "Check wandb project: cmr2025_fixed"
echo "Key metrics to monitor:"
echo "  - discriminator_accuracy (should improve in pretrain phase)"
echo "  - training_phase (should switch to adversarial after epoch 2)"
echo "  - discriminator_loss (should decrease during pretrain)"
echo "  - domain_loss (should be stable)" 