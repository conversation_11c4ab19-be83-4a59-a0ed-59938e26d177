#!/bin/bash

# CMR2025 Task3 Training Script with Fixed Domain Adaptation
# Fixed issues:
# 1. BCE_with_logits for mixed precision compatibility
# 2. Hierarchical feature processing (global/mid/local)
# 3. Correct accuracy calculation for single field strength batches
# 4. Stable accuracy with sliding average

#SBATCH --job-name=cmr_fixed_da
#SBATCH --output=logs/cmr_fixed_domain_adapt_%j.out
#SBATCH --error=logs/cmr_fixed_domain_adapt_%j.err
#SBATCH --time=24:00:00
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --partition=gpu

# Load modules
module load cuda/11.8
module load anaconda3

# Activate conda environment
source activate promptmr

# Set environment variables
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="${PYTHONPATH}:/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3"

# Create logs directory
mkdir -p logs

# Training configuration
CONFIG_FILE="PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt.yaml"
EXPERIMENT_NAME="cmr2025_fixed_domain_adapt"
WANDB_PROJECT="cmr2025_task3"

echo "Starting CMR2025 Task3 training with fixed domain adaptation..."
echo "Config file: $CONFIG_FILE"
echo "Experiment name: $EXPERIMENT_NAME"
echo "Wandb project: $WANDB_PROJECT"
echo "Training time: 24 hours"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Start training
cd PromptMR-plus-Task3

python train.py \
    --config $CONFIG_FILE \
    --experiment_name $EXPERIMENT_NAME \
    --wandb_project $WANDB_PROJECT \
    --wandb_entity lisha-zeng-cedars-sinai \
    --precision 16-mixed \
    --accelerator gpu \
    --devices 1 \
    --max_epochs 50 \
    --log_every_n_steps 100 \
    --val_check_interval 0.25 \
    --check_val_every_n_epoch 1 \
    --num_sanity_val_steps 2 \
    --enable_progress_bar True \
    --deterministic False \
    --benchmark True \
    --gradient_clip_val 1.0 \
    --accumulate_grad_batches 1 \
    --sync_batchnorm False \
    --reload_dataloaders_every_n_epochs 0 \
    --default_root_dir logs \
    --callbacks discriminator_checkpoint

echo "Training completed!" 