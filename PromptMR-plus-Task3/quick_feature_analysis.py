#!/usr/bin/env python3
"""
Quick feature analysis script to identify which cascade layers are most informative for field strength.
This can be run during or after training to guide feature layer selection.
"""

import torch
import torch.nn.functional as F
import numpy as np
from sklearn.metrics import accuracy_score
import os
import sys

# Add the project root to the path
sys.path.append('/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3')

from pl_modules.DomainAdaptMRModule import DomainAdaptMRModule
from models.domain_adapt_utils import extract_field_strength_from_path


class QuickFeatureAnalyzer:
    """Quick analyzer to test discriminator performance on individual layers."""
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.layer_features = {i: [] for i in range(6)}
        self.field_strengths = []
        
        # Register hooks
        self._register_hooks()
    
    def _register_hooks(self):
        """Register hooks to capture features from each cascade layer."""
        def get_features_hook(layer_idx):
            def hook(module, input, output):
                if output is not None:
                    self.layer_features[layer_idx].append(output.detach().cpu())
            return hook
        
        # Register hooks for each cascade block
        for i, cascade_block in enumerate(self.model.promptmr.cascade_blocks):
            cascade_block.register_forward_hook(get_features_hook(i))
    
    def test_layer_discriminator(self, layer_idx, features, field_strengths):
        """Test how well a simple discriminator can classify field strength using features from a single layer."""
        
        # Flatten features
        features_flat = features.view(features.shape[0], -1)
        
        # Create simple discriminator for this layer
        input_dim = features_flat.shape[1]
        discriminator = torch.nn.Sequential(
            torch.nn.Linear(input_dim, 128),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(128, 64),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(64, 1),
            torch.nn.Sigmoid()
        ).to(self.device)
        
        # Convert field strengths to binary labels (1.5T -> 0, 3.0T -> 1)
        labels = torch.tensor([1.0 if fs == 3.0 else 0.0 for fs in field_strengths], 
                            dtype=torch.float32).to(self.device)
        
        # Move features to device
        features_flat = features_flat.to(self.device)
        
        # Simple training loop
        optimizer = torch.optim.Adam(discriminator.parameters(), lr=0.001)
        criterion = torch.nn.BCELoss()
        
        best_accuracy = 0.0
        patience = 10
        patience_counter = 0
        
        for epoch in range(50):
            # Forward pass
            predictions = discriminator(features_flat)
            loss = criterion(predictions.squeeze(), labels)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # Calculate accuracy
            with torch.no_grad():
                pred_binary = (predictions.squeeze() > 0.5).float()
                accuracy = (pred_binary == labels).float().mean().item()
                
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    break
        
        return best_accuracy
    
    def analyze_all_layers(self, dataloader, max_samples=200):
        """Analyze all layers and return their discriminative power."""
        print(f"Analyzing features from up to {max_samples} samples...")
        
        self.model.eval()
        sample_count = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(dataloader):
                if sample_count >= max_samples:
                    break
                
                # Move batch to device
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # Clear previous features
                for layer_idx in self.layer_features:
                    self.layer_features[layer_idx] = []
                
                # Forward pass
                _ = self.model(batch['masked_kspace'], batch['mask'], 
                             batch['num_low_frequencies'], batch['mask_type'])
                
                # Extract field strength
                if hasattr(batch, 'fname'):
                    for fname in batch['fname']:
                        field_strength = extract_field_strength_from_path(fname)
                        self.field_strengths.append(field_strength)
                
                # Store features
                for layer_idx in range(6):
                    if self.layer_features[layer_idx]:
                        features = self.layer_features[layer_idx][0]
                        self.layer_features[layer_idx].append(features)
                
                sample_count += 1
                
                if sample_count % 50 == 0:
                    print(f"Processed {sample_count} samples...")
        
        print(f"Collected features from {sample_count} samples")
        
        # Test each layer
        layer_scores = {}
        
        for layer_idx in range(6):
            if not self.layer_features[layer_idx]:
                print(f"Layer {layer_idx}: No features collected")
                continue
            
            print(f"\nTesting Layer {layer_idx}...")
            features = torch.stack(self.layer_features[layer_idx])
            
            # Test discriminator performance
            accuracy = self.test_layer_discriminator(layer_idx, features, self.field_strengths)
            layer_scores[layer_idx] = accuracy
            
            print(f"Layer {layer_idx} accuracy: {accuracy:.4f}")
        
        return layer_scores
    
    def print_recommendations(self, layer_scores):
        """Print recommendations based on layer scores."""
        print("\n=== LAYER ANALYSIS RESULTS ===")
        
        # Sort layers by accuracy
        sorted_layers = sorted(layer_scores.items(), key=lambda x: x[1], reverse=True)
        
        print("Layer performance ranking:")
        for i, (layer_idx, accuracy) in enumerate(sorted_layers):
            print(f"{i+1}. Layer {layer_idx}: {accuracy:.4f}")
        
        print(f"\nRecommended feature_extract_layers:")
        print(f"  Top 3: {[layer for layer, _ in sorted_layers[:3]]}")
        print(f"  Top 4: {[layer for layer, _ in sorted_layers[:4]]}")
        print(f"  All layers: {[layer for layer, _ in sorted_layers]}")
        
        # Check if any layer has good performance
        good_layers = [layer for layer, acc in sorted_layers if acc > 0.7]
        if good_layers:
            print(f"\n✅ Good performing layers (>70%): {good_layers}")
        else:
            print(f"\n⚠️ No layers achieve >70% accuracy. Best: {sorted_layers[0][1]:.4f}")
        
        return sorted_layers


def main():
    """Main function to run the analysis."""
    print("Starting quick feature layer analysis...")
    
    # Initialize model
    model = DomainAdaptMRModule(
        num_cascades=6,
        chans=48,
        feature_extract_layers=[0, 1, 2, 3, 4, 5],  # Use all layers for analysis
        discriminator_pretrain_epochs=5,
        discriminator_accuracy_threshold=0.65
    )
    
    # Load checkpoint if available
    checkpoint_paths = [
        "logs/cmr2025_task3/latest_checkpoint.ckpt",
        "logs/cmr2025_task3/best_checkpoint.ckpt",
        "logs/cmr2025_task3/epoch=0-step=1000.ckpt"  # Early checkpoint
    ]
    
    checkpoint_loaded = False
    for checkpoint_path in checkpoint_paths:
        if os.path.exists(checkpoint_path):
            print(f"Loading checkpoint from {checkpoint_path}")
            try:
                checkpoint = torch.load(checkpoint_path, map_location='cpu')
                model.load_state_dict(checkpoint['state_dict'])
                checkpoint_loaded = True
                break
            except Exception as e:
                print(f"Failed to load {checkpoint_path}: {e}")
    
    if not checkpoint_loaded:
        print("No checkpoint loaded - using random weights")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Initialize data
    from data.mri_data import CmrxReconSliceDataset, CmrxReconDataTransform, CmrxRecon25MaskFunc
    from torch.utils.data import DataLoader
    
    mask_func = CmrxRecon25MaskFunc(
        num_low_frequencies=[16],
        num_adj_slices=5,
        mask_path="/common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5"
    )
    
    transform = CmrxReconDataTransform(
        mask_func=mask_func,
        uniform_resolution=None,
        use_seed=False
    )
    
    dataset = CmrxReconSliceDataset(
        root="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/train_15T_and_30T",
        challenge="multicoil",
        transform=transform
    )
    
    dataloader = DataLoader(dataset, batch_size=1, shuffle=True, num_workers=2)
    
    # Run analysis
    analyzer = QuickFeatureAnalyzer(model, device)
    layer_scores = analyzer.analyze_all_layers(dataloader, max_samples=200)
    
    # Print recommendations
    recommendations = analyzer.print_recommendations(layer_scores)
    
    # Save results
    results = {
        'layer_scores': layer_scores,
        'recommendations': recommendations,
        'field_strength_distribution': {
            '1.5T': sum(1 for fs in analyzer.field_strengths if fs == 1.5),
            '3.0T': sum(1 for fs in analyzer.field_strengths if fs == 3.0)
        }
    }
    
    print(f"\nField strength distribution:")
    print(f"  1.5T: {results['field_strength_distribution']['1.5T']}")
    print(f"  3.0T: {results['field_strength_distribution']['3.0T']}")
    
    # Save results to file
    import json
    with open('feature_layer_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to feature_layer_analysis_results.json")


if __name__ == "__main__":
    main() 