"""
Domain Adaptation Lightning Module for PromptMR-plus

This module extends the base PromptMrModule to add domain adversarial training
for field strength invariant features.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from collections import defaultdict
import wandb
import os
from lightning.pytorch.callbacks import Callback

from pl_modules.promptmr_module import PromptMrModule
from models.domain_adapt import DomainAdaptationModule
from models.domain_adapt_utils import create_field_strength_batch_tensor, analyze_field_strength_distribution
from data import transforms


class LayerAnalyzer:
    """Real-time analyzer for cascade layer features."""
    
    def __init__(self):
        self.layer_stats = defaultdict(list)
        self.analysis_count = 0
    
    def analyze_features(self, cascade_features, training_phase):
        """Analyze features from cascade blocks in real-time."""
        if not cascade_features:
            return
        
        self.analysis_count += 1
        
        # Analyze each layer
        for layer_idx, feature in enumerate(cascade_features):
            if feature is None:
                continue
            
            # Handle case where feature is a list of tensors
            if isinstance(feature, list):
                # Analyze each tensor in the list
                for tensor_idx, tensor in enumerate(feature):
                    if tensor is None:
                        continue
                    
                    with torch.no_grad():
                        mean_val = tensor.mean().item()
                        std_val = tensor.std().item()
                        min_val = tensor.min().item()
                        max_val = tensor.max().item()
                        
                        # Use a unique key for each tensor in the list
                        tensor_key = f"{layer_idx}_{tensor_idx}"
                        if tensor_key not in self.layer_stats:
                            self.layer_stats[tensor_key] = []
                        
                        self.layer_stats[tensor_key].append({
                            'mean': mean_val,
                            'std': std_val,
                            'min': min_val,
                            'max': max_val,
                            'shape': list(tensor.shape),
                            'phase': training_phase
                        })
            else:
                # Handle case where feature is a single tensor
                with torch.no_grad():
                    mean_val = feature.mean().item()
                    std_val = feature.std().item()
                    min_val = feature.min().item()
                    max_val = feature.max().item()
                    
                    if layer_idx not in self.layer_stats:
                        self.layer_stats[layer_idx] = []
                    
                    self.layer_stats[layer_idx].append({
                        'mean': mean_val,
                        'std': std_val,
                        'min': min_val,
                        'max': max_val,
                        'shape': list(feature.shape),
                        'phase': training_phase
                    })
        
        # Print analysis every 500 steps
        if self.analysis_count % 500 == 0:
            self.print_analysis()
    
    def print_analysis(self):
        """Print the analysis results."""
        if not self.layer_stats:
            return
        
        print("\n" + "="*60)
        print("REAL-TIME LAYER ANALYSIS")
        print("="*60)
        
        # Print layer analysis
        print(f"\nLayer Analysis (based on {self.analysis_count} samples):")
        print(f"{'Layer':<8} {'Samples':<8} {'Mean':<10} {'Std':<10} {'Range':<10} {'Var':<10}")
        print("-" * 60)
        
        layer_scores = {}
        
        # Group by layer index (before underscore)
        layer_groups = {}
        for key in self.layer_stats.keys():
            if '_' in key:
                layer_idx = int(key.split('_')[0])
            else:
                layer_idx = int(key)
            
            if layer_idx not in layer_groups:
                layer_groups[layer_idx] = []
            layer_groups[layer_idx].extend(self.layer_stats[key])
        
        for layer_idx in sorted(layer_groups.keys()):
            stats = layer_groups[layer_idx]
            
            # Calculate aggregate statistics
            means = [s['mean'] for s in stats]
            stds = [s['std'] for s in stats]
            mins = [s['min'] for s in stats]
            maxs = [s['max'] for s in stats]
            
            avg_mean = np.mean(means)
            avg_std = np.mean(stds)
            mean_range = np.mean(maxs) - np.mean(mins)
            std_range = np.std(means)  # How much the mean varies across samples
            
            # Calculate a simple score based on feature variability
            score = std_range * mean_range
            layer_scores[layer_idx] = score
            
            print(f"{layer_idx:<8} {len(stats):<8} "
                  f"{avg_mean:<10.4f} {avg_std:<10.4f} "
                  f"{mean_range:<10.4f} {std_range:<10.4f}")
        
        # Print recommendations
        if layer_scores:
            sorted_layers = sorted(layer_scores.items(), key=lambda x: x[1], reverse=True)
            
            print(f"\nLayer Ranking (by discriminative power):")
            for i, (layer_idx, score) in enumerate(sorted_layers):
                print(f"  {i+1}. Layer {layer_idx}: {score:.6f}")
            
            print(f"\nRecommended feature_extract_layers:")
            print(f"  Top 3: {[layer for layer, _ in sorted_layers[:3]]}")
            print(f"  Top 4: {[layer for layer, _ in sorted_layers[:4]]}")
            print(f"  All: {[layer for layer, _ in sorted_layers]}")
        
        print("="*60)
        
        return layer_scores


class DomainAdaptMRModule(PromptMrModule):
    """
    PyTorch Lightning module for PromptMR-plus with Domain Adaptation

    This extends the PromptMrModule to add domain adversarial training capabilities.
    """

    def __init__(
        self,
        num_cascades=6,
        chans=48,
        pools=4, # Keep pools here if DomainAdaptMRModule uses it directly
        domain_lambda=0.05,  # Further reduced from 0.1 to 0.05 for better stability
        feature_extract_layers=[1, 3, 5],
        grl_alpha_schedule='cosine',  # Changed to cosine schedule for smoother transitions
        enable_mask_visualization=False,  # Whether to enable mask visualization
        num_log_images=16,  # Number of images to log
        discriminator_pretrain_epochs=3,  # NEW: Number of epochs to pretrain discriminator
        discriminator_accuracy_threshold=0.7,  # NEW: Accuracy threshold to start adversarial training
        enable_layer_analysis=True,  # NEW: Enable real-time layer analysis
        **kwargs
    ):
        """
        Args:
            num_cascades: Number of cascades in the PromptMR-plus model
            chans: Number of channels in the PromptUnet model
            pools: Number of pooling layers in the PromptUnet model
            domain_lambda: Weight for domain adversarial loss
            feature_extract_layers: Which cascade blocks to extract features from
            grl_alpha_schedule: Schedule for GRL alpha parameter ('constant', 'linear', 'exp', 'cosine')
            enable_mask_visualization: Whether to enable mask visualization
            num_log_images: Number of images to log
            discriminator_pretrain_epochs: Number of epochs to pretrain discriminator without adversarial training
            discriminator_accuracy_threshold: Accuracy threshold to enable adversarial training
            enable_layer_analysis: Whether to enable real-time layer analysis
            **kwargs: Additional arguments for the parent class
        """
        # Pass 'chans' as 'n_feat0' to the parent class
        super().__init__(num_cascades=num_cascades, n_feat0=chans, **kwargs)

        # Initialize Domain Adaptation Module
        self.domain_lambda = domain_lambda
        self.feature_extract_layers = feature_extract_layers
        self.grl_alpha_schedule = grl_alpha_schedule
        self.current_alpha = 0.0  # Initial alpha value
        self.max_alpha = 0.1  # Reduced max alpha value to minimize gradient reversal impact

        # NEW: Two-stage training parameters
        self.discriminator_pretrain_epochs = discriminator_pretrain_epochs
        self.discriminator_accuracy_threshold = discriminator_accuracy_threshold
        self.discriminator_accuracy = 0.0  # Track current discriminator accuracy
        self.adversarial_training_enabled = False  # Flag to control adversarial training
        self.discriminator_learning_phase = True  # Flag for discriminator learning phase

        # NEW: Layer analysis parameters
        self.enable_layer_analysis = enable_layer_analysis
        self.layer_analysis_counter = 0
        self.layer_analysis_interval = 100  # Analyze every 100 steps
        self.layer_analysis_print_interval = 500  # Print analysis every 500 steps

        # Verification flag
        self.verification_done = False

        # Visualization parameters
        self.enable_mask_visualization = enable_mask_visualization
        self.num_log_images = num_log_images

        self.domain_adapt = DomainAdaptationModule(
            cascade_channels=chans,
            feature_extract_layers=feature_extract_layers
        )

        # Hook handlers for storing intermediate features
        self.feature_hooks = []
        self.cascade_features = []

        # Register hooks on cascade blocks
        self._register_feature_hooks()

        # Domain loss criterion
        self.domain_criterion = nn.MSELoss()

        # Track domain adaptation metrics
        self.domain_loss = None

        # NEW: Initialize layer analyzer (will be set in on_train_start)
        self.layer_analyzer = None

    def verify_domain_adaptation(self):
        """
        Verify the feature extraction and domain adaptation components.
        """
        print("\n--- Domain Adaptation Verification ---")
        # 1. Verify feature extraction layer indices
        print(f"Feature extract layers: {self.feature_extract_layers}")
        print(f"Number of cascade features available: {len(self.cascade_features)}")

        # 2. Verify features from each extraction layer
        print("\nVerifying feature extraction layers:")
        for i, layer_idx in enumerate(self.feature_extract_layers):
            if layer_idx < len(self.cascade_features):
                feature = self.cascade_features[layer_idx]
                if isinstance(feature, list):
                    print(f"  - Layer {layer_idx}: list with {len(feature)} tensors")
                    for j, feat in enumerate(feature):
                        if feat is not None:
                            print(f"    Tensor {j}: shape={feat.shape}, dtype={feat.dtype}, device={feat.device}")
                        else:
                            print(f"    Tensor {j}: None")
                else:
                    print(f"  - Layer {layer_idx}: shape={feature.shape}, dtype={feature.dtype}, device={feature.device}")
            else:
                print(f"  - Layer {layer_idx}: Index out of range for available features ({len(self.cascade_features)})!")

        # 3. Verify compressors
        print("\nVerifying compressors:")
        for i, compressor in enumerate(self.domain_adapt.compressors):
            # Assuming compressor is a Sequential model with conv1 as the first layer
            if hasattr(compressor, 'conv1') and hasattr(compressor.conv1, 'in_channels'):
                print(f"  - Compressor {i}: in_channels={compressor.conv1.in_channels}")
            else:
                print(f"  - Compressor {i}: Could not determine in_channels.")
        print("--- End Verification ---\n")

    def calculate_alpha(self):
        """
        Calculate the current alpha value based on the schedule and training progress
        Only used during adversarial training phase
        """
        if not self.adversarial_training_enabled:
            return 0.0
            
        if self.grl_alpha_schedule == 'constant':
            return self.max_alpha

        # Get current epoch and total epochs
        current_epoch = self.current_epoch
        max_epochs = self.trainer.max_epochs

        # Calculate progress since adversarial training started
        adversarial_start_epoch = max(self.discriminator_pretrain_epochs, 
                                     current_epoch - (current_epoch - self.discriminator_pretrain_epochs))
        adversarial_epochs = max_epochs - adversarial_start_epoch
        
        if adversarial_epochs <= 0:
            return 0.0
            
        progress = min((current_epoch - adversarial_start_epoch) / adversarial_epochs, 1.0)

        if self.grl_alpha_schedule == 'linear':
            # Linear increase from 0 to max_alpha over adversarial training period
            return self.max_alpha * progress

        elif self.grl_alpha_schedule == 'exp':
            # Exponential growth
            return self.max_alpha * (2.0 / (1.0 + np.exp(-5 * progress)) - 1.0)

        elif self.grl_alpha_schedule == 'cosine':
            # Cosine schedule: smoother transition
            return self.max_alpha * 0.5 * (1 - math.cos(math.pi * progress))

        # Default to constant schedule
        return self.max_alpha

    def visualize_kspace_mask(self, mask_tensor, mask_type=None, batch_idx=0):
        """
        Create a proper visualization of a k-space sampling mask

        Args:
            mask_tensor: The mask tensor to visualize
            mask_type: The type of mask (e.g., 'kt_uniform', 'kt_random', etc.)
            batch_idx: Batch index for logging purposes

        Returns:
            A tuple of (processed_mask_np, mask_fig) where processed_mask_np is the
            processed numpy array and mask_fig is a matplotlib figure
        """
        import matplotlib.pyplot as plt
        import numpy as np
        import os

        # Create directory for saving visualizations
        save_dir = os.path.join('/home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug')
        os.makedirs(save_dir, exist_ok=True)

        # Log mask information
        mask_shape_str = str(mask_tensor.shape)
        self.logger.experiment.log({f"val_images/mask_shape_{batch_idx}": mask_shape_str})
        print(f"MASK_DEBUG: Mask tensor shape: {mask_shape_str}")

        if mask_type is not None:
            self.logger.experiment.log({f"val_images/mask_type_{batch_idx}": mask_type})
            print(f"MASK_DEBUG: Mask type: {mask_type}")

        # Get the first mask in the batch
        mask_np = mask_tensor[0].cpu().numpy()

        # Log original mask shape
        original_shape_str = str(mask_np.shape)
        original_ndim = mask_np.ndim
        self.logger.experiment.log({f"val_images/original_mask_shape_{batch_idx}": original_shape_str})
        self.logger.experiment.log({f"val_images/original_mask_ndim_{batch_idx}": original_ndim})
        print(f"MASK_DEBUG: Original mask shape: {original_shape_str}, ndim: {original_ndim}")

        # Print some statistics about the mask
        mask_min = np.min(mask_np)
        mask_max = np.max(mask_np)
        mask_mean = np.mean(mask_np)
        mask_nonzero = np.count_nonzero(mask_np)
        mask_total = mask_np.size
        mask_sparsity = mask_nonzero / mask_total

        print(f"MASK_DEBUG: Mask stats - min: {mask_min}, max: {mask_max}, mean: {mask_mean:.4f}")
        print(f"MASK_DEBUG: Mask sparsity: {mask_nonzero}/{mask_total} = {mask_sparsity:.4f}")

        # Process mask for visualization
        # First, try to get a 2D representation that shows the sampling pattern
        print(f"MASK_DEBUG: Processing mask for visualization...")

        # If mask is 3D with last dimension of size 1, squeeze it
        if mask_np.ndim == 3 and mask_np.shape[2] == 1:
            mask_np = mask_np[:, :, 0]
            print(f"MASK_DEBUG: Squeezed 3D mask with last dim=1 to shape {mask_np.shape}")
        # Otherwise, try to squeeze any singleton dimensions
        else:
            old_shape = mask_np.shape
            mask_np = np.squeeze(mask_np)
            print(f"MASK_DEBUG: Squeezed mask from {old_shape} to {mask_np.shape}")

        # If still more than 2D, we need to take a slice
        if mask_np.ndim > 2:
            old_shape = mask_np.shape
            # For 3D mask, take the middle slice along the first dimension
            if mask_np.ndim == 3:
                slice_idx = mask_np.shape[0]//2
                mask_np = mask_np[slice_idx, :, :]
                print(f"MASK_DEBUG: Took middle slice {slice_idx} from 3D mask, new shape: {mask_np.shape}")
            # For 4D or higher, take middle slices along all dimensions beyond the first two
            else:
                slices = tuple(slice(None) for _ in range(2)) + \
                         tuple(mask_np.shape[i]//2 for i in range(2, mask_np.ndim))
                mask_np = mask_np[slices]
                print(f"MASK_DEBUG: Took middle slices from {old_shape} to get 2D mask with shape {mask_np.shape}")

        # Log the processed mask shape
        processed_shape = str(mask_np.shape)
        self.logger.experiment.log({f"val_images/processed_mask_shape_{batch_idx}": processed_shape})
        print(f"MASK_DEBUG: Final processed mask shape: {processed_shape}")

        # Print a small sample of the mask values
        if mask_np.size > 0:
            sample_size = min(5, mask_np.shape[0])
            sample_values = mask_np[:sample_size, :sample_size]
            print(f"MASK_DEBUG: Sample of mask values (top-left {sample_size}x{sample_size}):\n{sample_values}")

            # Print the number of non-zero elements in each row/column to understand the pattern
            row_nonzeros = np.count_nonzero(mask_np, axis=1)[:5]  # First 5 rows
            col_nonzeros = np.count_nonzero(mask_np, axis=0)[:5]  # First 5 columns
            print(f"MASK_DEBUG: Non-zeros in first 5 rows: {row_nonzeros}")
            print(f"MASK_DEBUG: Non-zeros in first 5 cols: {col_nonzeros}")

        # Create a figure for the mask
        mask_fig, mask_ax = plt.subplots(figsize=(10, 8))

        # For kt masks, create a special visualization
        if mask_type in ('kt_random', 'kt_uniform', 'kt_radial'):
            # Create a composite visualization that shows the mask pattern better
            print(f"MASK_DEBUG: Creating special visualization for kt mask type: {mask_type}")

            # Create a pattern that shows how the mask varies across time
            mask_pattern = np.zeros((mask_np.shape[1], min(50, mask_np.shape[0])))
            for i in range(min(50, mask_np.shape[0])):
                mask_pattern[:, i] = mask_np[i, :]

            # Plot the pattern with time on the x-axis
            mask_im = mask_ax.imshow(mask_pattern.T, cmap='viridis', aspect='auto')
            mask_title = 'K-t Sampling Pattern'
            mask_ax.set_xlabel('Frequency Encoding')
            mask_ax.set_ylabel('Time Points')
        else:
            # Standard visualization for regular masks
            mask_im = mask_ax.imshow(mask_np, cmap='viridis')
            mask_title = 'K-space Sampling Mask'

        if mask_type is not None:
            mask_title += f' (Type: {mask_type})'
        mask_ax.set_title(mask_title)
        plt.colorbar(mask_im, ax=mask_ax)

        # Also create a second visualization showing the mask sparsity pattern
        if mask_np.ndim == 2 and mask_np.shape[0] > 1:
            # Create a figure showing the number of samples per row/column
            sparsity_fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

            # Plot samples per row
            row_samples = np.sum(mask_np, axis=1)
            ax1.plot(row_samples)
            ax1.set_title('Samples per Row')
            ax1.set_xlabel('Row Index')
            ax1.set_ylabel('Number of Samples')

            # Plot samples per column
            col_samples = np.sum(mask_np, axis=0)
            ax2.plot(col_samples)
            ax2.set_title('Samples per Column')
            ax2.set_xlabel('Column Index')
            ax2.set_ylabel('Number of Samples')

            # Save the sparsity visualization
            sparsity_viz_file = os.path.join(save_dir, f'mask_sparsity_{batch_idx}_{mask_type}.png')
            sparsity_fig.tight_layout()
            sparsity_fig.savefig(sparsity_viz_file, dpi=300, bbox_inches='tight')
            print(f"MASK_DEBUG: Saved mask sparsity visualization to {sparsity_viz_file}")
            plt.close(sparsity_fig)

        # Save the mask visualization to a file for manual inspection
        import os
        save_dir = os.path.join('/home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug')
        os.makedirs(save_dir, exist_ok=True)

        # Save both the raw mask data and the visualization
        mask_file = os.path.join(save_dir, f'mask_{batch_idx}_{mask_type}.npy')
        np.save(mask_file, mask_np)
        print(f"MASK_DEBUG: Saved raw mask data to {mask_file}")

        # Save the visualization
        viz_file = os.path.join(save_dir, f'mask_viz_{batch_idx}_{mask_type}.png')
        mask_fig.savefig(viz_file, dpi=300, bbox_inches='tight')
        print(f"MASK_DEBUG: Saved mask visualization to {viz_file}")

        return mask_np, mask_fig

    def on_train_epoch_start(self):
        # Clear features at the start of each epoch
        self.cascade_features = []

        # Update alpha value based on training phase
        training_phase, effective_alpha = self._get_training_phase_info()
        self.current_alpha = effective_alpha
        
        # Log the training phase and alpha
        self.log("train_metrics/grl_alpha", self.current_alpha)
        print(f"🔄 Epoch {self.current_epoch} starting - Phase: {training_phase}, Alpha: {self.current_alpha:.4f}")

    def _register_feature_hooks(self):
        """Register forward hooks to capture intermediate features from cascade blocks"""

        def get_features_hook(cascade_idx):
            def hook(module, input, output):
                # For promptmr_v2, we need to get features from the internal NormPromptUnet
                # The output is a tuple: (img_pred, latent, history_feat)
                # We need to access the intermediate features from the model's forward pass
                pass  # This hook won't be used directly
            return hook

        def get_norm_unet_features_hook(cascade_idx):
            def hook(module, input, output):
                # The output from NormPromptUnet is: (x, latent, history_feat, intermediate_features)
                if isinstance(output, tuple) and len(output) >= 4:
                    intermediate_features = output[3]  # Get the intermediate features
                    
                    # Store the intermediate features for this cascade
                    if cascade_idx >= len(self.cascade_features):
                        self.cascade_features.extend([None] * (cascade_idx + 1 - len(self.cascade_features)))
                    
                    # Store the intermediate features (list of 3 tensors from different layers)
                    self.cascade_features[cascade_idx] = intermediate_features
                    
                    # Debug: print feature shapes
                    if intermediate_features:
                        print(f"DEBUG: Cascade {cascade_idx} features:")
                        for i, feat in enumerate(intermediate_features):
                            if feat is not None:
                                print(f"  Layer {i}: {feat.shape}")
                            else:
                                print(f"  Layer {i}: None")
            return hook

        # Clear any existing hooks
        for hook in self.feature_hooks:
            hook.remove()
        self.feature_hooks = []

        # Register new hooks for each cascade block
        for i, cascade in enumerate(self.promptmr.cascades):
            # For promptmr_v2, register hook on the internal NormPromptUnet model
            # cascade.model is the NormPromptUnet
            hook = cascade.model.register_forward_hook(get_norm_unet_features_hook(i))
            self.feature_hooks.append(hook)

    def on_train_start(self):
        """Called at the beginning of training"""
        super().on_train_start()
        # Ensure hooks are registered at the start of training
        if not self.feature_hooks:
            self._register_feature_hooks()

        # NEW: Initialize layer analyzer
        self.layer_analyzer = LayerAnalyzer()

    def on_validation_start(self):
        """Called at the beginning of validation"""
        super().on_validation_start()
        # Ensure hooks are registered at the start of validation
        if not self.feature_hooks:
            self._register_feature_hooks()

    def training_step(self, batch, batch_idx):
        """
        Training step with domain adaptation

        In addition to the regular reconstruction loss, adds domain adversarial loss.
        """
        # Clear features from previous step
        self.cascade_features = []

        # Regular forward pass for reconstruction (will fill cascade_features via hooks)
        output_dict = self(batch.masked_kspace, batch.mask, batch.num_low_frequencies, batch.mask_type,
                           use_checkpoint=self.use_checkpoint, compute_sens_per_coil=self.compute_sens_per_coil)
        
        # Verification step (runs only once at the beginning of training)
        if not self.verification_done and self.global_step == 0:
            print("Performing one-time verification of domain adaptation setup...")
            self.verify_domain_adaptation()
            self.verification_done = True
        
        output = output_dict['img_pred']

        # Get target and crop output to match
        target, output = transforms.center_crop_to_smallest(batch.target, output)

        # Calculate reconstruction loss
        recon_loss = self.loss(output.unsqueeze(1), target.unsqueeze(1), data_range=batch.max_value)

        # Calculate domain adaptation loss
        domain_loss = torch.tensor(0.0, device=recon_loss.device)

        # Get current training phase
        training_phase, effective_alpha = self._get_training_phase_info()
        self.log("train_metrics/training_phase", hash(training_phase) % 1000, on_step=True, logger=True)  # Simple numeric encoding for logging
        self.log("train_metrics/effective_alpha", effective_alpha, on_step=True, logger=True)

        # NEW: Layer analysis every 500 steps
        if self.enable_layer_analysis and self.layer_analyzer and self.global_step % 500 == 0:
            print(f"\n🔍 LAYER ANALYSIS at step {self.global_step}")
            self.layer_analyzer.analyze_features(self.cascade_features, training_phase)
            self.layer_analyzer.print_analysis()

        # Only calculate domain loss if we have features
        if self.cascade_features:
            try:
                # Debug feature shapes and types
                print(f"DEBUG: Training phase: {training_phase}, Epoch: {self.current_epoch}")
                print(f"DEBUG: Number of cascade features: {len(self.cascade_features)}")
                for i, feat in enumerate(self.cascade_features):
                    if feat is not None:
                        if isinstance(feat, list):
                            print(f"DEBUG: Feature {i} is a list with {len(feat)} tensors")
                            for j, tensor in enumerate(feat):
                                if tensor is not None:
                                    print(f"  - Tensor {j}: shape={tensor.shape}, dtype={tensor.dtype}, device={tensor.device}")
                                else:
                                    print(f"  - Tensor {j}: None")
                        else:
                            print(f"DEBUG: Feature {i} shape: {feat.shape}, dtype: {feat.dtype}, device: {feat.device}")
                    else:
                        print(f"DEBUG: Feature {i} is None")

                # Extract field strength labels from file paths in batch
                if hasattr(batch, 'fname'):
                    # Create field strength tensor from file paths
                    field_strength_labels = create_field_strength_batch_tensor(
                        batch.fname, device=recon_loss.device
                    )
                    print(f"DEBUG: Field strength labels shape: {field_strength_labels.shape}, values: {field_strength_labels}")
                else:
                    # Fallback: infer from data path structure
                    batch_size = batch.masked_kspace.shape[0]
                    if self.trainer.training:
                        default_strength = 0.0  # 1.5T (training)
                    else:
                        default_strength = 1.0  # 3.0T (validation)
                    field_strength_labels = torch.ones((batch_size, 1), device=recon_loss.device) * default_strength
                    print(f"DEBUG: Using default field strength: {default_strength}")

                # Validate field strength labels
                if not torch.all((field_strength_labels == 0.0) | (field_strength_labels == 1.0)):
                    raise ValueError(f"Invalid field strength labels: {field_strength_labels}")

                # PHASE 1: Discriminator learning (no adversarial training)
                if training_phase in ["discriminator_pretrain", "discriminator_learning"]:
                    print(f"🔍 PHASE 1: Training discriminator to learn field strength features")
                    
                    # Forward pass WITHOUT gradient reversal (alpha=0)
                    pred_field_strength = self.domain_adapt(self.cascade_features, alpha=0.0)
                    
                    # Validate logits
                    if not torch.isfinite(pred_field_strength).all():
                        raise ValueError(f"Invalid predictions (NaN/Inf): {pred_field_strength}")
                    
                    # Calculate only discriminator loss (teaching it to recognize field strength)
                    discriminator_loss = F.binary_cross_entropy_with_logits(
                        pred_field_strength,  # Now outputs logits
                        field_strength_labels,
                        reduction='mean'
                    )
                    
                    # Calculate accuracy for phase transition decision
                    with torch.no_grad():
                        # 对于单一field strength的batch，使用概率而不是准确率
                        batch_size = field_strength_labels.shape[0]
                        unique_labels = torch.unique(field_strength_labels)
                        
                        if len(unique_labels) == 1:
                            # 单一field strength的batch
                            current_label = unique_labels[0].item()
                            
                            if current_label == 0.0:  # 1.5T batch
                                # 对于1.5T，我们希望判别器输出负值（接近0）
                                # 使用sigmoid后的概率，越接近0越好
                                probs = torch.sigmoid(pred_field_strength)
                                accuracy = (1.0 - probs.mean()).item()  # 1.5T的"准确率"
                            else:  # 3.0T batch
                                # 对于3.0T，我们希望判别器输出正值（接近1）
                                # 使用sigmoid后的概率，越接近1越好
                                probs = torch.sigmoid(pred_field_strength)
                                accuracy = probs.mean().item()  # 3.0T的"准确率"
                        else:
                            # 混合field strength的batch（理论上不应该出现）
                            pred_binary = (pred_field_strength > 0.0).float()
                            accuracy = (pred_binary == field_strength_labels).float().mean().item()
                        
                        # 使用滑动平均来稳定accuracy
                        if not hasattr(self, 'accuracy_buffer'):
                            self.accuracy_buffer = []
                        self.accuracy_buffer.append(accuracy)
                        
                        # 保持最近100个accuracy值的滑动平均
                        if len(self.accuracy_buffer) > 100:
                            self.accuracy_buffer.pop(0)
                        
                        # 使用滑动平均作为稳定的accuracy指标
                        stable_accuracy = sum(self.accuracy_buffer) / len(self.accuracy_buffer)
                        self.discriminator_accuracy = stable_accuracy
                        
                        # 添加详细的调试信息
                        logit_mean = pred_field_strength.mean().item()
                        logit_std = pred_field_strength.std().item()
                        probs = torch.sigmoid(pred_field_strength)
                        prob_mean = probs.mean().item()
                        
                        print(f"DEBUG: Batch field strength: {current_label if len(unique_labels) == 1 else 'mixed'}")
                        print(f"DEBUG: Logits - mean: {logit_mean:.4f}, std: {logit_std:.4f}")
                        print(f"DEBUG: Probabilities - mean: {prob_mean:.4f}")
                        print(f"DEBUG: Current accuracy: {accuracy:.3f}, Stable accuracy: {stable_accuracy:.3f}")
                    
                    # Domain loss is just discriminator loss (no adversarial component)
                    domain_loss = discriminator_loss
                    
                    print(f"DEBUG: Discriminator loss: {discriminator_loss:.4f}, Accuracy: {self.discriminator_accuracy:.3f}")
                    
                    # Log discriminator learning metrics
                    self.log("train_metrics/discriminator_loss", discriminator_loss, prog_bar=True, on_step=True, logger=True)
                    self.log("train_metrics/discriminator_accuracy", self.discriminator_accuracy, prog_bar=True, on_step=True, logger=True)
                    self.log("train_metrics/domain_loss", domain_loss, prog_bar=True, on_step=True, logger=True)
                    
                    # Check if we should transition to adversarial training
                    self._should_enable_adversarial_training()

                # PHASE 2: Adversarial training (fool the discriminator)
                elif training_phase == "adversarial_training":
                    print(f"⚔️ PHASE 2: Adversarial training - fooling the discriminator")
                    
                    # Calculate current alpha for gradient reversal
                    alpha = self.calculate_alpha()
                    self.current_alpha = alpha
                    
                    # For adversarial training, we want to push features towards a neutral point
                    target_field_strength = torch.ones_like(field_strength_labels) * 0.5

                    # Apply gradient scaling to stabilize training
                    with torch.cuda.amp.autocast(enabled=True):
                        # Validate features before domain adaptation
                        valid_features = []
                        for f in self.cascade_features:
                            if f is not None:
                                if isinstance(f, list):
                                    # 如果是list，检查list中的每个tensor
                                    if all(isinstance(item, torch.Tensor) and torch.isfinite(item).all() for item in f if item is not None):
                                        valid_features.append(f)
                                elif isinstance(f, torch.Tensor) and torch.isfinite(f).all():
                                    valid_features.append(f)
                        
                        if not valid_features:
                            raise ValueError("No valid features available for domain adaptation")
                        
                        # Apply gradient clipping to domain adaptation features to prevent explosion
                        for feat in valid_features:
                            if isinstance(feat, list):
                                # 如果是list，对list中的每个tensor进行梯度裁剪
                                for tensor in feat:
                                    if tensor is not None and tensor.requires_grad:
                                        torch.nn.utils.clip_grad_norm_(tensor, max_norm=1.0)
                            elif feat.requires_grad:
                                torch.nn.utils.clip_grad_norm_(feat, max_norm=1.0)
                        
                        pred_field_strength = self.domain_adapt(self.cascade_features, alpha)
                        
                        # Validate logits range and statistics
                        if not torch.isfinite(pred_field_strength).all():
                            raise ValueError(f"Invalid predictions (NaN/Inf): {pred_field_strength}")
                        
                        # Check for extreme logit values that might cause numerical instability
                        logit_stats = {
                            'min': pred_field_strength.min().item(),
                            'max': pred_field_strength.max().item(),
                            'mean': pred_field_strength.mean().item(),
                            'std': pred_field_strength.std().item()
                        }
                        
                        # Log the statistics
                        for stat_name, stat_value in logit_stats.items():
                            self.log(f"train_metrics/logit_{stat_name}", stat_value, on_step=True, logger=True)
                        
                        # More aggressive clipping for stability - reduce from [-20, 20] to [-5, 5]
                        if abs(logit_stats['min']) > 5 or abs(logit_stats['max']) > 5:
                            print(f"WARNING: Extreme logit values detected: {logit_stats}")
                            # Clip extreme values to prevent numerical instability
                            pred_field_strength = torch.clamp(pred_field_strength, -5, 5)
                            print("Logits have been clipped to [-5, 5] range")
                        
                        if pred_field_strength.shape != field_strength_labels.shape:
                            raise ValueError(f"Shape mismatch: pred {pred_field_strength.shape} vs labels {field_strength_labels.shape}")
                        
                        # Add detailed debugging for field strength distribution
                        unique_labels = torch.unique(field_strength_labels)
                        print(f"DEBUG: Unique field strength labels in batch: {unique_labels}")
                        print(f"DEBUG: Field strength distribution - 1.5T: {(field_strength_labels == 0.0).sum()}, 3.0T: {(field_strength_labels == 1.0).sum()}")
                        print(f"DEBUG: Predictions shape: {pred_field_strength.shape}, stats: {logit_stats}")
                        
                        # 添加更详细的batch分析
                        batch_size = field_strength_labels.shape[0]
                        print(f"DEBUG: Batch size: {batch_size}")
                        print(f"DEBUG: 1.5T samples: {(field_strength_labels == 0.0).sum().item()}/{batch_size}")
                        print(f"DEBUG: 3.0T samples: {(field_strength_labels == 1.0).sum().item()}/{batch_size}")
                        
                        # 分析判别器对不同field strength的预测
                        pred_15t = pred_field_strength[field_strength_labels == 0.0]
                        pred_30t = pred_field_strength[field_strength_labels == 1.0]
                        
                        if len(pred_15t) > 0:
                            print(f"DEBUG: 1.5T predictions - mean: {pred_15t.mean().item():.4f}, std: {pred_15t.std().item():.4f}")
                        if len(pred_30t) > 0:
                            print(f"DEBUG: 3.0T predictions - mean: {pred_30t.mean().item():.4f}, std: {pred_30t.std().item():.4f}")

                        # Two components of domain loss with validation
                        try:
                            # Use binary_cross_entropy_with_logits since discriminator now outputs logits
                            discriminator_loss = F.binary_cross_entropy_with_logits(
                                pred_field_strength,  # Now outputs logits
                                field_strength_labels,
                                reduction='mean'
                            )
                            if not torch.isfinite(discriminator_loss):
                                raise ValueError(f"Invalid discriminator loss: {discriminator_loss}")
                            
                            adaptation_loss = F.binary_cross_entropy_with_logits(
                                pred_field_strength,  # Now outputs logits
                                target_field_strength,
                                reduction='mean'
                            )
                            if not torch.isfinite(adaptation_loss):
                                raise ValueError(f"Invalid adaptation loss: {adaptation_loss}")
                            
                            print(f"DEBUG: Losses - discriminator: {discriminator_loss:.4f}, adaptation: {adaptation_loss:.4f}")
                        except Exception as e:
                            raise ValueError(f"Error calculating losses: {str(e)}")

                    # Ramp up the domain loss weight based on current epoch
                    epoch_factor = min(self.current_epoch / (self.trainer.max_epochs * 0.5), 1.0)
                    print(f"DEBUG: Current epoch: {self.current_epoch}, max epochs: {self.trainer.max_epochs}, epoch factor: {epoch_factor:.3f}")

                    # Reduce domain loss weights for better stability
                    discriminator_weight = 0.5  # Reduced from 0.7
                    adaptation_weight = 0.5     # Increased from 0.3
                    
                    # Total domain loss with validation and reduced impact
                    domain_loss = epoch_factor * 0.5 * (discriminator_weight * discriminator_loss + adaptation_weight * adaptation_loss)  # Added 0.5 multiplier
                    if not torch.isfinite(domain_loss):
                        raise ValueError(f"Invalid domain loss: {domain_loss}")

                    # Log adversarial training metrics
                    self.log("train_metrics/current_alpha", alpha, on_step=True, logger=True, on_epoch=True)
                    self.log("train_metrics/domain_loss", domain_loss, prog_bar=True, on_step=True, logger=True, on_epoch=True)
                    self.log("train_metrics/discriminator_loss", discriminator_loss, on_step=True, logger=True, on_epoch=True)
                    self.log("train_metrics/adaptation_loss", adaptation_loss, on_step=True, logger=True, on_epoch=True)
                    self.log("train_metrics/pred_field_strength", pred_field_strength.mean(), on_step=True, logger=True, on_epoch=True)
                    self.log("train_metrics/actual_field_strength", field_strength_labels.mean(), on_step=True, logger=True, on_epoch=True)

                # Add gradient and loss value checks
                if not torch.isfinite(domain_loss):
                    print(f"Warning: Domain loss is not finite: {domain_loss}. Setting to 0.0")
                    domain_loss = torch.tensor(0.0, device=recon_loss.device)

                # More conservative loss clipping - reduce from 10.0 to 2.0
                domain_loss = torch.clamp(domain_loss, 0.0, 2.0)
                print(f"DEBUG: Final domain loss: {domain_loss:.4f}")

            except Exception as e:
                # Enhanced error reporting
                import traceback
                print(f"Error in domain adaptation: {str(e)}")
                print("Traceback:")
                traceback.print_exc()
                print(f"Current epoch: {self.current_epoch}, batch_idx: {batch_idx}")
                print(f"Training phase: {training_phase}")
                domain_loss = torch.tensor(0.0, device=recon_loss.device)

        # Combined loss
        # Only add domain loss if we're doing domain adaptation training
        if training_phase != "discriminator_pretrain":
            total_loss = recon_loss + self.domain_lambda * domain_loss
        else:
            # During pretraining, only use reconstruction loss for main model
            total_loss = recon_loss
            
        self.domain_loss = domain_loss

        # Log metrics with explicit logger=True to ensure they appear in wandb
        self.log("train_loss", total_loss, prog_bar=True, logger=True, on_step=True, on_epoch=True)
        self.log("train_metrics/recon_loss", recon_loss, prog_bar=True, logger=True, on_step=True, on_epoch=True)

        return total_loss

    def validation_step(self, batch, batch_idx):
        """
        Validation step with domain adaptation metrics
        """
        # Clear features from previous step
        self.cascade_features = []

        # Run forward pass to get reconstructed image
        output_dict = self(batch.masked_kspace, batch.mask, batch.num_low_frequencies, batch.mask_type,
                           use_checkpoint=self.use_checkpoint, compute_sens_per_coil=self.compute_sens_per_coil)
        output = output_dict['img_pred']

        # Get target and crop output to match
        target, output = transforms.center_crop_to_smallest(batch.target, output)

        # Calculate metrics
        loss = self.loss(output.unsqueeze(1), target.unsqueeze(1), data_range=batch.max_value)

        # Log validation loss with explicit logger=True to ensure it appears in wandb
        self.log("val_loss", loss, prog_bar=True, logger=True, on_epoch=True)

        # Visualize images in wandb (only for first few batches to avoid too many images)
        if batch_idx < 5:  # Limit to first 5 batches
            # Convert tensors to numpy for visualization
            target_np = target[0].cpu().numpy()
            output_np = output[0].cpu().numpy()

            # Get undersampled image (zero-filled reconstruction)
            zero_filled = output_dict.get('zero_filled_recon', None)
            if zero_filled is not None:
                zero_filled_cropped, _ = transforms.center_crop_to_smallest(zero_filled, output)
                zero_filled_np = zero_filled_cropped[0].cpu().numpy()
            else:
                zero_filled_np = None

            # Create a figure with subplots
            import matplotlib.pyplot as plt
            import numpy as np
            from matplotlib.colors import Normalize

            # Initialize mask_np as None
            mask_np = None

            # Use the specialized mask visualization function
            if self.enable_mask_visualization:
                mask_np, mask_fig = self.visualize_kspace_mask(batch.mask, batch.mask_type, batch_idx)
                if mask_fig is not None:
                    self.logger.experiment.log({f"val_images/detailed_mask_{batch_idx}": wandb.Image(mask_fig)})
                    plt.close(mask_fig)

            # Also try to visualize the actual k-space data to see the sampling pattern
            if hasattr(batch, 'masked_kspace'):
                # Get the magnitude of the k-space data
                print(f"KSPACE_DEBUG: masked_kspace shape: {batch.masked_kspace.shape}")

                # Extract the first slice, first coil
                kspace_mag = torch.sqrt(batch.masked_kspace[0, 0, :, :, 0]**2 + batch.masked_kspace[0, 0, :, :, 1]**2)
                kspace_mag_np = kspace_mag.cpu().numpy()
                print(f"KSPACE_DEBUG: kspace_mag_np shape: {kspace_mag_np.shape}")
                print(f"KSPACE_DEBUG: kspace_mag_np stats - min: {np.min(kspace_mag_np):.4f}, max: {np.max(kspace_mag_np):.4f}, mean: {np.mean(kspace_mag_np):.4f}")

                # Log k-space magnitude
                kspace_fig, kspace_ax = plt.subplots(figsize=(8, 8))
                # Use log scale for better visualization
                kspace_im = kspace_ax.imshow(np.log1p(kspace_mag_np), cmap='viridis')
                kspace_ax.set_title('K-space Magnitude (log scale)')
                plt.colorbar(kspace_im, ax=kspace_ax)
                self.logger.experiment.log({f"val_images/kspace_magnitude_{batch_idx}": wandb.Image(kspace_fig)})

                # Save the k-space visualization to a file
                import os
                save_dir = os.path.join('/home/<USER>/common/CMRrecon2025_task3/PromptMR-plus/mask_debug')
                os.makedirs(save_dir, exist_ok=True)

                # Save both the raw k-space data and the visualization
                kspace_file = os.path.join(save_dir, f'kspace_{batch_idx}.npy')
                np.save(kspace_file, kspace_mag_np)
                print(f"KSPACE_DEBUG: Saved raw k-space data to {kspace_file}")

                # Save the visualization
                kspace_viz_file = os.path.join(save_dir, f'kspace_viz_{batch_idx}.png')
                kspace_fig.savefig(kspace_viz_file, dpi=300, bbox_inches='tight')
                print(f"KSPACE_DEBUG: Saved k-space visualization to {kspace_viz_file}")

                plt.close(kspace_fig)

                # Also visualize the mask applied to k-space
                masked_kspace_fig, masked_kspace_ax = plt.subplots(figsize=(8, 8))

                # Log the shapes for debugging
                self.logger.experiment.log({f"val_images/kspace_mag_shape_{batch_idx}": str(kspace_mag_np.shape)})

                # Only log mask_np shape if it exists
                if mask_np is not None:
                    self.logger.experiment.log({f"val_images/mask_np_shape_{batch_idx}": str(mask_np.shape)})

                # Create a masked version of the k-space magnitude
                # If mask_np is None or shapes don't match, just use the original k-space
                if mask_np is None:
                    print(f"MASK_DEBUG: No mask available, using original k-space")
                    masked_kspace = kspace_mag_np
                elif kspace_mag_np.shape != mask_np.shape:
                    print(f"MASK_DEBUG: Shape mismatch between kspace_mag_np {kspace_mag_np.shape} and mask_np {mask_np.shape}")

                    # Option 1: Resize mask using interpolation
                    from scipy.ndimage import zoom
                    zoom_factors = (kspace_mag_np.shape[0] / mask_np.shape[0],
                                   kspace_mag_np.shape[1] / mask_np.shape[1])
                    print(f"MASK_DEBUG: Using zoom factors: {zoom_factors}")

                    try:
                        # Save original mask before resizing
                        orig_mask_file = os.path.join(save_dir, f'orig_mask_{batch_idx}.npy')
                        np.save(orig_mask_file, mask_np)
                        print(f"MASK_DEBUG: Saved original mask to {orig_mask_file}")

                        # Resize the mask
                        resized_mask = zoom(mask_np, zoom_factors, order=0)  # order=0 for nearest neighbor
                        print(f"MASK_DEBUG: Resized mask shape: {resized_mask.shape}")

                        # Save resized mask
                        resized_mask_file = os.path.join(save_dir, f'resized_mask_{batch_idx}.npy')
                        np.save(resized_mask_file, resized_mask)
                        print(f"MASK_DEBUG: Saved resized mask to {resized_mask_file}")

                        # Create visualization of resized mask
                        resized_mask_fig, resized_mask_ax = plt.subplots(figsize=(8, 8))
                        resized_mask_im = resized_mask_ax.imshow(resized_mask, cmap='viridis')
                        resized_mask_ax.set_title('Resized Mask')
                        plt.colorbar(resized_mask_im, ax=resized_mask_ax)
                        resized_mask_viz_file = os.path.join(save_dir, f'resized_mask_viz_{batch_idx}.png')
                        resized_mask_fig.savefig(resized_mask_viz_file, dpi=300, bbox_inches='tight')
                        print(f"MASK_DEBUG: Saved resized mask visualization to {resized_mask_viz_file}")
                        plt.close(resized_mask_fig)

                        # Apply the mask
                        masked_kspace = kspace_mag_np * resized_mask
                        self.logger.experiment.log({f"val_images/resized_mask_shape_{batch_idx}": str(resized_mask.shape)})
                    except Exception as e:
                        # If resizing fails, just show the original k-space
                        print(f"MASK_DEBUG: Error resizing mask: {e}")
                        self.logger.experiment.log({f"val_images/resize_error_{batch_idx}": str(e)})
                        masked_kspace = kspace_mag_np
                else:
                    print(f"MASK_DEBUG: Shapes match between kspace_mag_np and mask_np: {kspace_mag_np.shape}")
                    masked_kspace = kspace_mag_np * mask_np

                masked_kspace_im = masked_kspace_ax.imshow(np.log1p(masked_kspace), cmap='viridis')
                masked_kspace_ax.set_title('Masked K-space (log scale)')
                plt.colorbar(masked_kspace_im, ax=masked_kspace_ax)
                self.logger.experiment.log({f"val_images/masked_kspace_{batch_idx}": wandb.Image(masked_kspace_fig)})

                # Save the masked k-space visualization to a file
                masked_kspace_file = os.path.join(save_dir, f'masked_kspace_{batch_idx}.npy')
                np.save(masked_kspace_file, masked_kspace)
                print(f"MASK_DEBUG: Saved masked k-space data to {masked_kspace_file}")

                # Save the visualization
                masked_kspace_viz_file = os.path.join(save_dir, f'masked_kspace_viz_{batch_idx}.png')
                masked_kspace_fig.savefig(masked_kspace_viz_file, dpi=300, bbox_inches='tight')
                print(f"MASK_DEBUG: Saved masked k-space visualization to {masked_kspace_viz_file}")

                plt.close(masked_kspace_fig)

            fig, axes = plt.subplots(1, 4 if zero_filled_np is not None else 3, figsize=(16, 4))

            # Normalize all images to the same scale
            vmin = min(np.min(target_np), np.min(output_np))
            vmax = max(np.max(target_np), np.max(output_np))
            if zero_filled_np is not None:
                vmin = min(vmin, np.min(zero_filled_np))
                vmax = max(vmax, np.max(zero_filled_np))
            norm = Normalize(vmin=vmin, vmax=vmax)

            # Plot mask (k-space) - use a better visualization
            # For k-space mask, we want to show where the samples are taken
            # Use a different colormap to highlight the sampling pattern
            # For kt_random and kt_uniform masks, we need to show the pattern differently
            if mask_np is None:
                # If mask_np is None, show a placeholder
                axes[0].text(0.5, 0.5, 'Mask not available',
                            horizontalalignment='center', verticalalignment='center',
                            transform=axes[0].transAxes)
                axes[0].set_title(f'Sampling Mask ({batch.mask_type if hasattr(batch, "mask_type") else "unknown"})')
            elif batch.mask_type in ('kt_random', 'kt_uniform', 'kt_radial'):
                try:
                    # For kt masks, show a more informative visualization
                    # Create a composite visualization that shows the mask pattern better
                        # Check if mask_np is a numpy array with the right shape
                    if not isinstance(mask_np, np.ndarray) or len(mask_np.shape) < 2:
                        print(f"Invalid mask_np type or shape: {type(mask_np)}, shape: {getattr(mask_np, 'shape', 'unknown')}")
                        # Create a dummy pattern
                        mask_pattern = np.zeros((100, 50))
                    else:
                        mask_pattern = np.zeros((mask_np.shape[1], 50))
                        for i in range(min(50, mask_np.shape[0])):
                            mask_pattern[:, i] = mask_np[i, :]
                    axes[0].imshow(mask_pattern.T, cmap='viridis', aspect='auto')
                    axes[0].set_title(f'K-t Sampling Pattern ({batch.mask_type})')
                except Exception as e:
                    # If there's an error, show a placeholder
                    print(f"Error visualizing kt mask: {e}")
                    axes[0].text(0.5, 0.5, f'Error: {str(e)}',
                                horizontalalignment='center', verticalalignment='center',
                                transform=axes[0].transAxes)
                    axes[0].set_title(f'Sampling Mask ({batch.mask_type})')
            else:
                try:
                    # For regular masks, show the standard visualization
                    # Convert mask_np to float if it's not already
                    if not np.issubdtype(mask_np.dtype, np.number):
                        print(f"Converting mask_np from {mask_np.dtype} to float")
                        mask_np = mask_np.astype(np.float32)
                    axes[0].imshow(mask_np, cmap='viridis')
                    axes[0].set_title(f'Sampling Mask ({batch.mask_type})')
                except Exception as e:
                    # If there's an error, show a placeholder
                    print(f"Error visualizing mask: {e}")
                    axes[0].text(0.5, 0.5, f'Error: {str(e)}',
                                horizontalalignment='center', verticalalignment='center',
                                transform=axes[0].transAxes)
                    axes[0].set_title(f'Sampling Mask ({batch.mask_type})')
            axes[0].axis('off')

            # Plot target (fully sampled)
            im1 = axes[1].imshow(target_np, cmap='gray', norm=norm)
            axes[1].set_title('Fully Sampled (Target)')
            axes[1].axis('off')

            # Plot zero-filled reconstruction if available
            if zero_filled_np is not None:
                axes[2].imshow(zero_filled_np, cmap='gray', norm=norm)
                axes[2].set_title('Zero-filled Reconstruction')
                axes[2].axis('off')
                idx = 3
            else:
                idx = 2

            # Plot output (reconstructed)
            axes[idx].imshow(output_np, cmap='gray', norm=norm)
            axes[idx].set_title('Model Reconstruction')
            axes[idx].axis('off')

            # Add colorbar
            plt.colorbar(im1, ax=axes, orientation='horizontal', fraction=0.046, pad=0.04)

            # Log figure to wandb - ensure it appears in the media panel with steps slider
            # Use a more descriptive name and caption
            # Use global_step to ensure steps slider appears
            global_step = self.trainer.global_step
            caption = f"Validation Sample {batch_idx}"
            if hasattr(batch, 'mask_type'):
                caption += f" | Mask: {batch.mask_type}"
            if hasattr(batch, 'fname') and "30T" in batch.fname[0]:
                caption += f" | Field: 3.0T"
            else:
                caption += f" | Field: 1.5T"

            self.logger.experiment.log({
                f"val_images/sample_{batch_idx}": wandb.Image(
                    fig,
                    caption=caption
                )
            }, commit=True, step=global_step)
            plt.close(fig)

            # Also log the images separately for better organization
            # Use global_step to ensure steps slider appears
            self.logger.experiment.log({
                f"val_images/target_{batch_idx}": wandb.Image(
                    target_np,
                    caption="Fully Sampled (Target)"
                ),
                f"val_images/output_{batch_idx}": wandb.Image(
                    output_np,
                    caption="Model Reconstruction"
                )
            }, commit=True, step=global_step)

            # Log zero-filled reconstruction if available
            # Use global_step to ensure steps slider appears
            if zero_filled_np is not None:
                self.logger.experiment.log({
                    f"val_images/zero_filled_{batch_idx}": wandb.Image(
                        zero_filled_np,
                        caption="Zero-filled Reconstruction"
                    )
                }, commit=True, step=global_step)

            # Also log field strength information if available
            if hasattr(batch, 'fname'):
                field_strength = "3.0T" if "30T" in batch.fname[0] else "1.5T"
                self.logger.experiment.log({f"val_images/field_strength_{batch_idx}": field_strength})

        # Continue with regular validation step
        # Get zero-filled reconstruction
        img_zf = output_dict.get('img_zf', None)
        if img_zf is not None:
            _, img_zf = transforms.center_crop_to_smallest(batch.target, img_zf)

        # Get sensitivity maps if available
        sens_maps = output_dict.get('sens_maps', None)

        # Create centered coil visual
        cc = batch.masked_kspace.shape[1]
        centered_coil_visual = torch.log(1e-10+torch.view_as_complex(batch.masked_kspace[:,cc//2]).abs())

        output = {
            "batch_idx": batch_idx,
            "fname": batch.fname,
            "slice_num": batch.slice_num,
            "max_value": batch.max_value,
            "img_zf": img_zf,
            "mask": centered_coil_visual,
            "sens_maps": sens_maps[:,0].abs() if sens_maps is not None else None,
            "output": output,
            "target": target,
            "loss": loss,
        }

        # Calculate domain adaptation metrics if we have features
        if self.cascade_features:
            # Debug print to understand the structure of cascade_features
            print(f"Cascade features length: {len(self.cascade_features)}")
            for i, feat in enumerate(self.cascade_features):
                if feat is not None:
                    if isinstance(feat, list):
                        print(f"Feature {i} type: {type(feat)}, length: {len(feat)}")
                        for j, tensor in enumerate(feat):
                            if tensor is not None:
                                print(f"  - Tensor {j}: shape={tensor.shape}, dtype={tensor.dtype}, device={tensor.device}")
                            else:
                                print(f"  - Tensor {j}: None")
                    else:
                        print(f"Feature {i} type: {type(feat)}, shape: {feat.shape if hasattr(feat, 'shape') else 'no shape'}")
                else:
                    print(f"Feature {i}: None")

            # Extract field strength labels from file paths in batch
            if hasattr(batch, 'fname'):
                field_strength_labels = create_field_strength_batch_tensor(
                    batch.fname, device=self.device
                )
            else:
                # Fallback: assume validation data is 3.0T (normalized to 1.0)
                batch_size = batch.masked_kspace.shape[0]
                field_strength_labels = torch.ones((batch_size, 1), device=self.device)

            # Forward pass through domain adaptation module (alpha=0 to disable GRL during validation)
            pred_field_strength = self.domain_adapt(self.cascade_features, alpha=0.0)

            # Validate logits range and statistics during validation
            if not torch.isfinite(pred_field_strength).all():
                raise ValueError(f"Invalid validation predictions (NaN/Inf): {pred_field_strength}")
            
            # Check for extreme logit values
            logit_stats = {
                'min': pred_field_strength.min().item(),
                'max': pred_field_strength.max().item(),
                'mean': pred_field_strength.mean().item(),
                'std': pred_field_strength.std().item()
            }
            
            # Log the statistics
            for stat_name, stat_value in logit_stats.items():
                self.log(f"val_metrics/logit_{stat_name}", stat_value, on_step=False, on_epoch=True)
            
            # Check for extreme values
            if abs(logit_stats['min']) > 20 or abs(logit_stats['max']) > 20:
                print(f"WARNING: Extreme validation logit values detected: {logit_stats}")
                # Clip extreme values
                pred_field_strength = torch.clamp(pred_field_strength, -20, 20)
                print("Validation logits have been clipped to [-20, 20] range")

            # Calculate discriminator accuracy (how well it can distinguish field strengths)
            # Discriminator now outputs logits, use 0.0 as threshold
            pred_binary = (pred_field_strength > 0.0).float()
            accuracy = (pred_binary == field_strength_labels).float().mean()

            # Calculate field strength prediction error using logits
            field_strength_error = F.mse_loss(pred_field_strength, field_strength_labels)

            # Log metrics
            self.log("val_metrics/domain_accuracy", accuracy, on_step=False, on_epoch=True)
            self.log("val_metrics/field_strength_error", field_strength_error, on_step=False, on_epoch=True)
            self.log("val_metrics/pred_logit_mean", pred_field_strength.mean(), on_step=False, on_epoch=True)
            self.log("val_metrics/pred_logit_std", pred_field_strength.std(), on_step=False, on_epoch=True)

        return output

    def configure_optimizers(self):
        """
        Configure optimizers with different learning rates for main model and domain adaptation
        """
        # Separate parameters for different learning rates
        main_params = []
        domain_params = []
        
        for name, param in self.named_parameters():
            if 'domain_adapt' in name:
                domain_params.append(param)
            else:
                main_params.append(param)
        
        # Create optimizer with different parameter groups
        optimizer = torch.optim.AdamW([
            {'params': main_params, 'lr': self.lr, 'weight_decay': self.weight_decay},
            {'params': domain_params, 'lr': self.lr * 0.1, 'weight_decay': self.weight_decay * 0.5}  # 10x smaller LR for domain components
        ])

        # Step lr scheduler
        scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer, self.lr_step_size, self.lr_gamma
        )
        
        return [optimizer], [scheduler]

    def on_before_optimizer_step(self, optimizer):
        """
        Apply gradient clipping before optimizer step
        """
        # Apply gradient clipping to all parameters
        torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)

    def _should_enable_adversarial_training(self):
        """
        Determine if adversarial training should be enabled based on discriminator performance
        """
        # Enable adversarial training if:
        # 1. We've passed the pretrain epochs, AND
        # 2. Discriminator accuracy is above threshold
        pretrain_complete = self.current_epoch >= self.discriminator_pretrain_epochs
        accuracy_sufficient = self.discriminator_accuracy >= self.discriminator_accuracy_threshold
        
        should_enable = pretrain_complete and accuracy_sufficient
        
        if should_enable and not self.adversarial_training_enabled:
            print(f"🎯 ENABLING ADVERSARIAL TRAINING at epoch {self.current_epoch}")
            print(f"   Discriminator accuracy: {self.discriminator_accuracy:.3f} (threshold: {self.discriminator_accuracy_threshold})")
            self.adversarial_training_enabled = True
            self.discriminator_learning_phase = False
        
        return should_enable

    def _get_training_phase_info(self):
        """
        Get current training phase information for logging
        """
        if self.current_epoch < self.discriminator_pretrain_epochs:
            return "discriminator_pretrain", 0.0
        elif not self.adversarial_training_enabled:
            return "discriminator_learning", 0.0
        else:
            return "adversarial_training", self.current_alpha


class DiscriminatorCheckpointCallback(Callback):
    """
    Custom callback to save discriminator weights when it reaches the accuracy threshold.
    """
    
    def __init__(self, save_dir="discriminator_checkpoints", threshold=0.65):
        super().__init__()
        self.save_dir = save_dir
        self.threshold = threshold
        self.best_accuracy = 0.0
        self.best_epoch = -1
        
        # Create save directory
        os.makedirs(save_dir, exist_ok=True)
    
    def on_train_epoch_end(self, trainer, pl_module):
        """Called at the end of each training epoch."""
        # Get current discriminator accuracy
        current_accuracy = getattr(pl_module, 'discriminator_accuracy', 0.0)
        current_epoch = trainer.current_epoch
        
        # Check if we're in discriminator pretrain phase
        if hasattr(pl_module, 'discriminator_learning_phase') and pl_module.discriminator_learning_phase:
            # Save if accuracy is above threshold and better than previous best
            if current_accuracy >= self.threshold and current_accuracy > self.best_accuracy:
                self.best_accuracy = current_accuracy
                self.best_epoch = current_epoch
                
                # Extract discriminator state dict
                discriminator_state_dict = {}
                for name, param in pl_module.named_parameters():
                    if 'domain_adapt' in name and 'discriminator' in name:
                        discriminator_state_dict[name] = param.data.clone()
                
                # Save discriminator weights
                save_path = os.path.join(
                    self.save_dir, 
                    f"discriminator_epoch{current_epoch:02d}_acc{current_accuracy:.3f}.pt"
                )
                torch.save({
                    'epoch': current_epoch,
                    'accuracy': current_accuracy,
                    'threshold': self.threshold,
                    'state_dict': discriminator_state_dict,
                    'model_config': {
                        'feature_extract_layers': pl_module.feature_extract_layers,
                        'domain_lambda': pl_module.domain_lambda,
                    }
                }, save_path)
                
                print(f"🎯 DISCRIMINATOR CHECKPOINT SAVED!")
                print(f"   Epoch: {current_epoch}")
                print(f"   Accuracy: {current_accuracy:.3f} (threshold: {self.threshold})")
                print(f"   Saved to: {save_path}")
                
                # Also log to wandb
                if trainer.logger:
                    trainer.logger.experiment.log({
                        "discriminator_checkpoint/epoch": current_epoch,
                        "discriminator_checkpoint/accuracy": current_accuracy,
                        "discriminator_checkpoint/save_path": save_path
                    })
    
    def on_train_end(self, trainer, pl_module):
        """Called at the end of training."""
        if self.best_accuracy >= self.threshold:
            print(f"🏆 Best discriminator achieved {self.best_accuracy:.3f} accuracy at epoch {self.best_epoch}")
        else:
            print(f"⚠️  Discriminator never reached threshold {self.threshold}. Best: {self.best_accuracy:.3f}")
