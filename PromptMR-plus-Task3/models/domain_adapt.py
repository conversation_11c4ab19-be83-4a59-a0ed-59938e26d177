"""
Domain Adaptation Module for PromptMR-plus

This module implements domain adversarial training (DAT) components:
1. Feature extraction from cascade blocks
2. Gradient Reversal Layer (GRL)
3. Field strength discriminator
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class GradientReversalLayer(torch.autograd.Function):
    """
    Gradient Reversal Layer

    During forward pass: identity function
    During backward pass: multiply gradients by -alpha
    """

    @staticmethod
    def forward(ctx, x, alpha):
        ctx.alpha = alpha
        return x.view_as(x)

    @staticmethod
    def backward(ctx, grad_output):
        return -ctx.alpha * grad_output, None


class FeatureCompressor(nn.Module):
    """
    Compress features from a cascade block to a standardized size
    with added normalization for stability
    """

    def __init__(self, in_channels, out_channels=64):  # Reduced from 128 to 64
        super().__init__()
        # Add instance normalization for feature stability
        self.norm = nn.InstanceNorm2d(in_channels, affine=True)

        # Use two conv layers with smaller output dimension
        self.conv1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(in_channels // 2, out_channels, kernel_size=3, padding=1)

        # Smaller spatial dimensions (16x16 -> 8x8) to reduce parameter count
        self.pool = nn.AdaptiveAvgPool2d((8, 8))

    def forward(self, x):
        # Normalize input features
        x = self.norm(x)

        # Two-layer convolution with activation
        x = self.conv1(x)
        x = self.relu(x)
        x = self.conv2(x)

        # Standardize spatial dimensions
        x = self.pool(x)
        return x


class ResidualBlock(nn.Module):
    """
    Residual block for the discriminator to improve gradient flow
    """
    def __init__(self, input_dim, output_dim, dropout_rate=0.3):
        super().__init__()
        self.fc1 = nn.Linear(input_dim, output_dim)
        self.fc2 = nn.Linear(output_dim, output_dim)
        self.norm1 = nn.LayerNorm(output_dim)
        self.norm2 = nn.LayerNorm(output_dim)
        self.dropout = nn.Dropout(dropout_rate)
        self.relu = nn.ReLU(inplace=True)

        # Projection layer for residual connection if dimensions don't match
        self.proj = None
        if input_dim != output_dim:
            self.proj = nn.Linear(input_dim, output_dim)

    def forward(self, x):
        residual = x
        if self.proj is not None:
            residual = self.proj(residual)

        out = self.fc1(x)
        out = self.norm1(out)
        out = self.relu(out)
        out = self.dropout(out)

        out = self.fc2(out)
        out = self.norm2(out)

        # Add residual connection
        out += residual
        out = self.relu(out)

        return out


class FieldStrengthDiscriminator(nn.Module):
    """
    Discriminator that predicts the magnetic field strength from image features
    with increased capacity and proper output handling for binary classification
    """

    def __init__(self, in_channels, hidden_dim=256):  # Increased from 128 to 256
        super().__init__()
        self.flatten = nn.Flatten()

        # Calculate flattened size after pooling - adjusted for 8x8 pooling
        flattened_size = in_channels * 8 * 8  # from AdaptiveAvgPool2d(8, 8)

        # Increased network capacity with more layers
        # Input layer with stronger regularization
        self.input_layer = nn.Sequential(
            nn.Linear(flattened_size, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.LeakyReLU(0.2),  # Changed from ReLU to LeakyReLU for better gradients
            nn.Dropout(0.3)     # Reduced dropout for better learning
        )

        # Additional hidden layers for increased capacity
        self.hidden_layer1 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3)
        )

        self.hidden_layer2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3)
        )

        # Output layer
        self.output_layer = nn.Linear(hidden_dim // 2, 1)

        # Initialize weights with proper scale for better learning
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with proper scale for better learning"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                # Use proper initialization scale
                nn.init.normal_(m.weight, mean=0.0, std=0.1)  # Increased from 0.01 to 0.1
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.flatten(x)
        x = self.input_layer(x)
        x = self.hidden_layer1(x)
        x = self.hidden_layer2(x)
        x = self.output_layer(x)
        # 移除sigmoid，直接输出logits，使用BCE_with_logits
        return x


class DomainAdaptationModule(nn.Module):
    """
    Domain Adaptation Module for field strength invariance

    Takes features from different cascade blocks, applies GRL, and feeds to discriminator
    """

    def __init__(self, cascade_channels, feature_extract_layers=[1, 3, 5], feature_dim=64):
        """
        Args:
            cascade_channels: Base channel dimension for cascade blocks
            feature_extract_layers: Which cascade blocks to extract features from (indices)
            feature_dim: Dimension of compressed features per layer (reduced from 128 to 64)
        """
        super().__init__()
        self.feature_extract_layers = feature_extract_layers

        # Pre-define compressors for expected feature dimensions
        # This avoids creating them dynamically during forward passes
        self.compressors = nn.ModuleList()

        # Estimate channel dimensions for each cascade block
        # This is an approximation - the actual dimensions will be determined during forward pass
        estimated_channels = [cascade_channels * (2 ** min(i, 3)) for i in feature_extract_layers]

        # Create compressors for estimated dimensions
        for channels in estimated_channels:
            self.compressors.append(FeatureCompressor(channels, feature_dim))

        # 分层处理：为不同层级设计不同的处理策略
        # Layer 0: bottleneck - 全局信息，使用全局平均池化
        self.global_processor = nn.Sequential(
            nn.AdaptiveAvgPool2d(4),  # 更小的全局特征
            nn.Conv2d(feature_dim, feature_dim // 2, kernel_size=1),
            nn.InstanceNorm2d(feature_dim // 2, affine=True),
            nn.ReLU(inplace=True)
        )
        
        # Layer 1: dec_level3 - 中等语义，使用中等池化
        self.mid_processor = nn.Sequential(
            nn.AdaptiveAvgPool2d(8),  # 中等分辨率
            nn.Conv2d(feature_dim, feature_dim, kernel_size=1),
            nn.InstanceNorm2d(feature_dim, affine=True),
            nn.ReLU(inplace=True)
        )
        
        # Layer 2: dec_level2 - 局部细节，保持较高分辨率
        self.local_processor = nn.Sequential(
            nn.AdaptiveAvgPool2d(16),  # 较高分辨率
            nn.Conv2d(feature_dim, feature_dim * 2, kernel_size=1),
            nn.InstanceNorm2d(feature_dim * 2, affine=True),
            nn.ReLU(inplace=True)
        )

        # 特征融合：将不同层级的特征融合
        total_features = (feature_dim // 2) + feature_dim + (feature_dim * 2)  # 全局 + 中等 + 局部
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(total_features, feature_dim, kernel_size=1),
            nn.InstanceNorm2d(feature_dim, affine=True),
            nn.ReLU(inplace=True)
        )

        # Discriminator
        self.discriminator = FieldStrengthDiscriminator(feature_dim)

        # Initialize weights properly
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with smaller values to prevent gradient explosion"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, features, alpha=1.0):
        """
        Args:
            features: List of cascade features, where each cascade contains a list of 3 layer features
                     [cascade0_features, cascade1_features, ...]
                     where cascadeN_features = [layer0_feat, layer1_feat, layer2_feat]
            alpha: GRL intensity parameter
        """
        # 分层处理：为不同层级设计不同的处理策略
        global_features = []  # bottleneck特征 - 全局信息
        mid_features = []     # dec_level3特征 - 中等语义
        local_features = []   # dec_level2特征 - 局部细节

        for i, layer_idx in enumerate(self.feature_extract_layers):
            if layer_idx < len(features):
                # Get the cascade features (list of 3 layer features)
                cascade_features = features[layer_idx]
                
                if cascade_features is None or len(cascade_features) < 3:
                    continue
                    
                # 分层处理每个层级的特征
                for layer_feat_idx, layer_feature in enumerate(cascade_features):
                    if layer_feature is None:
                        continue
                        
                    # Get the number of channels in this feature
                    in_channels = layer_feature.shape[1]

                    # Check if we need to recreate the compressor due to channel mismatch
                    if i >= len(self.compressors) or (hasattr(self.compressors[i], 'norm') and self.compressors[i].norm.num_features != in_channels):
                        # If channel dimensions don't match, create a new compressor
                        if i >= len(self.compressors):
                            self.compressors.append(FeatureCompressor(in_channels, 64))
                        else:
                            self.compressors[i] = FeatureCompressor(in_channels, 64)
                        self.compressors[i] = self.compressors[i].to(layer_feature.device)

                    # Compress the feature to standard dimension
                    compressed = self.compressors[i](layer_feature)
                    
                    # 根据层级进行不同的处理
                    if layer_feat_idx == 0:  # bottleneck - 全局信息
                        processed = self.global_processor(compressed)
                        global_features.append(processed)
                    elif layer_feat_idx == 1:  # dec_level3 - 中等语义
                        processed = self.mid_processor(compressed)
                        mid_features.append(processed)
                    elif layer_feat_idx == 2:  # dec_level2 - 局部细节
                        processed = self.local_processor(compressed)
                        local_features.append(processed)

        if not global_features and not mid_features and not local_features:
            raise ValueError("No valid features extracted. Check feature_extract_layers indices.")

        # 融合不同层级的特征
        fused_features = []
        
        if global_features:
            # 全局特征：平均池化到统一大小
            global_avg = torch.stack(global_features).mean(dim=0)  # 平均所有cascade的全局特征
            # 确保全局特征的空间维度与其他特征一致
            global_avg = F.adaptive_avg_pool2d(global_avg, (8, 8))
            fused_features.append(global_avg)
        
        if mid_features:
            # 中等特征：平均池化到统一大小
            mid_avg = torch.stack(mid_features).mean(dim=0)  # 平均所有cascade的中等特征
            # 确保中等特征的空间维度与其他特征一致
            mid_avg = F.adaptive_avg_pool2d(mid_avg, (8, 8))
            fused_features.append(mid_avg)
            
        if local_features:
            # 局部特征：平均池化到统一大小
            local_avg = torch.stack(local_features).mean(dim=0)  # 平均所有cascade的局部特征
            # 确保局部特征的空间维度与其他特征一致
            local_avg = F.adaptive_avg_pool2d(local_avg, (8, 8))
            fused_features.append(local_avg)

        # 拼接不同层级的特征
        x = torch.cat(fused_features, dim=1)

        # Apply feature fusion with normalization
        x = self.feature_fusion(x)

        # Apply gradient reversal layer with the specified alpha
        x = GradientReversalLayer.apply(x, alpha)

        # Feed to discriminator
        pred_field_strength = self.discriminator(x)

        return pred_field_strength
