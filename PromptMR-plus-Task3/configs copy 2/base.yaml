# common settings for all experiments
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp
  gradient_clip_val: 0.01
  log_every_n_steps: 50 # log every n steps during training
  deterministic: false
  use_distributed_sampler: false # use customized distributed sampler defined in data module while validation
  logger: 
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      project: deep_recon
      mode: online
  callbacks:
    -
      class_path: lightning.pytorch.callbacks.ModelCheckpoint
      init_args:
        monitor: validation_loss
        mode: min 
        save_top_k: 0 # -1 to save all checkpoints, 5 to save the top 5 checkpoints
        save_last: true # always save the last checkpoint
        verbose: true # print checkpoint information
    # - 
    #   class_path: lightning.pytorch.callbacks.LearningRateMonitor
    #   init_args:
    #     logging_interval: 'epoch'
    #     log_momentum: true
    #     log_weight_decay: true

model:
  class_path: pl_modules.PromptMrModule
  init_args:
    num_log_images: 16 # number of images to log in validation step