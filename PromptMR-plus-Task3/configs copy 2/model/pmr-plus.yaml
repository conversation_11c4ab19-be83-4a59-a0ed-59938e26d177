# Description: default configuration for PromptMR+ model, may vary for different datasets
model:
  class_path: pl_modules.PromptMrModule
  init_args:
    num_cascades: 12
    num_adj_slices: 5
    n_feat0: 48
    feature_dim: [72,96,120]
    prompt_dim: [24,48,72]
    sens_n_feat0: 24
    sens_feature_dim: [36,48,60]
    sens_prompt_dim: [12,24,36]
    len_prompt: [5,5,5]
    prompt_size: [64,32,16]
    n_enc_cab: [2,3,3]
    n_dec_cab: [2,2,3]
    n_skip_cab: [1,1,1]
    n_bottleneck_cab: 3
    no_use_ca: false
    learnable_prompt: false
    adaptive_input: true
    n_buffer: 4
    n_history: 11
    use_sens_adj: true
    lr_gamma: 0.1
    weight_decay: 1e-2
    use_checkpoint: false

# add model tags to wandb
trainer:
  logger:
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      tags: [promptmr+]
  