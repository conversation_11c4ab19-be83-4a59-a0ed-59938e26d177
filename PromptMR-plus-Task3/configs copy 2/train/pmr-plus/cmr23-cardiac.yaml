data:
  class_path: pl_modules.DataModule
  init_args:
    slice_dataset: data.CmrxReconSliceDataset
    data_path: /gpfs/home/<USER>/mridatasets/cmrxrecon2023
    challenge: multicoil
    data_balancer: 
      class_path: data.BalanceSampler
      init_args:
        ratio_dict: {'T1map': 2, 'T2map': 5, 'cine_lax': 3, 'cine_sax': 1}
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.FixedLowEquiSpacedMaskFunc
          init_args:
            center_fractions:
            - 24
            accelerations: 
            - 4
            - 8
            - 10
            allow_any_combination: true
        uniform_resolution: null
        use_seed: false
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.FixedLowEquiSpacedMaskFunc
          init_args:
            center_fractions:
            - 24
            accelerations:
            - 4
            - 8
            - 10
            allow_any_combination: true
        uniform_resolution: null
        use_seed: true
    combine_train_val: true
    num_adj_slices: 5
    batch_size: 1
    distributed_sampler: true
    use_dataset_cache_file: false

# dataset specific settings
trainer:
  devices: 2
  num_nodes: 1
  max_epochs: 12
  logger: 
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      save_dir: exp/cmr23-cardiac/pmr-plus
      tags: [cmr23-cardiac]
      name: cmr23-cardiac-pmr-plus
      
model:
  class_path: pl_modules.PromptMrModule
  init_args:
    lr: 0.0002
    lr_step_size: 11
ckpt_path: null # add the path to the checkpoint if you want to resume training