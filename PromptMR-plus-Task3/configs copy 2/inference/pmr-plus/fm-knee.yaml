data:
  class_path: pl_modules.InferenceDataModule
  init_args:
    slice_dataset: data.FastmriSliceDataset
    data_path: /gpfs/scratch/bingyx01/dataset/fastMRI/knee_multicoil/multicoil_test
    challenge: multicoil
    test_transform:
      class_path: data.transforms.FastmriDataTransform
      init_args:
        mask_func: null
        uniform_resolution:
        - 384
        - 384
        use_seed: true
        mask_type: 'cartesian'
        test_num_low_frequencies: -1
    num_adj_slices: &n_adj_slc 5
    batch_size: 1
    distributed_sampler: false
    test_filter: 
      class_path: data.FuncFilterString
      init_args:
        filter_str: null #file1000000.h5


# dataset specific settings
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp
  devices: 4
  num_nodes: 1
  logger: false
  callbacks:
    -
      class_path: __main__.CustomWriter
      init_args:
        output_dir: _predict/fastmri-knee/test-plus
        write_interval: batch_and_epoch
model:
  class_path: pl_modules.PromptMrModule

ckpt_path: weights/fm-knee/promptmr-plus-epoch=44-step=781695.ckpt
