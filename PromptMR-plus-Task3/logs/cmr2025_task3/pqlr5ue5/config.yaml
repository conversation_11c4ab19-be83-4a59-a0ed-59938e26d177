# lightning.pytorch==2.3.3
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp_find_unused_parameters_true
  devices: '4'
  num_nodes: 1
  precision: null
  logger:
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      name: fixed_domain_adapt_1614
      save_dir: logs
      version: null
      offline: false
      dir: null
      id: null
      anonymous: null
      project: cmr2025_task3
      log_model: false
      experiment: null
      prefix: ''
      checkpoint_name: null
      entity: null
      notes: null
      tags:
      - promptmr_plus
      - layer_analysis
      - improved
      - 1.5T_and_3.0T
      - domain_adaptation
      config: null
      config_exclude_keys: null
      config_include_keys: null
      allow_val_change: null
      group: null
      job_type: null
      mode: null
      force: null
      reinit: null
      resume: null
      resume_from: null
      fork_from: null
      save_code: null
      tensorboard: null
      sync_tensorboard: null
      monitor_gym: null
      settings: null
  callbacks:
  - class_path: lightning.pytorch.callbacks.ModelCheckpoint
    init_args:
      dirpath: null
      filename: best-epoch{epoch:02d}-val{validation_loss:.4f}
      monitor: validation_loss
      verbose: true
      save_last: true
      save_top_k: 3
      save_weights_only: false
      mode: min
      auto_insert_metric_name: true
      every_n_train_steps: null
      train_time_interval: null
      every_n_epochs: null
      save_on_train_epoch_end: null
      enable_version_counter: true
  - class_path: pl_modules.domain_adapt_module.DiscriminatorCheckpointCallback
    init_args:
      save_dir: discriminator_checkpoints
      threshold: 0.65
  fast_dev_run: false
  max_epochs: 50
  min_epochs: null
  max_steps: -1
  min_steps: null
  max_time: null
  limit_train_batches: null
  limit_val_batches: null
  limit_test_batches: null
  limit_predict_batches: null
  overfit_batches: 0.0
  val_check_interval: null
  check_val_every_n_epoch: 1
  num_sanity_val_steps: null
  log_every_n_steps: 50
  enable_checkpointing: null
  enable_progress_bar: null
  enable_model_summary: null
  accumulate_grad_batches: 1
  gradient_clip_val: 0.01
  gradient_clip_algorithm: null
  deterministic: false
  benchmark: null
  inference_mode: true
  use_distributed_sampler: false
  profiler: null
  detect_anomaly: false
  barebones: false
  plugins: null
  sync_batchnorm: false
  reload_dataloaders_every_n_epochs: 0
  default_root_dir: null
model:
  class_path: pl_modules.DomainAdaptMRModule
  init_args:
    num_cascades: 6
    chans: 48
    pools: 4
    domain_lambda: 0.01
    feature_extract_layers:
    - 1
    - 3
    - 5
    grl_alpha_schedule: linear
    enable_mask_visualization: false
    num_log_images: 4
    discriminator_pretrain_epochs: 3
    discriminator_accuracy_threshold: 0.65
    enable_layer_analysis: true
    num_adj_slices: 5
    feature_dim:
    - 72
    - 96
    - 120
    prompt_dim:
    - 24
    - 48
    - 72
    sens_n_feat0: 24
    sens_feature_dim:
    - 36
    - 48
    - 60
    sens_prompt_dim:
    - 12
    - 24
    - 36
    len_prompt:
    - 5
    - 5
    - 5
    prompt_size:
    - 64
    - 32
    - 16
    n_enc_cab:
    - 2
    - 3
    - 3
    n_dec_cab:
    - 2
    - 2
    - 3
    n_skip_cab:
    - 1
    - 1
    - 1
    n_bottleneck_cab: 3
    no_use_ca: false
    learnable_prompt: false
    adaptive_input: true
    n_buffer: 4
    n_history: 0
    use_sens_adj: true
    model_version: promptmr_v2
    lr: 0.0003
    lr_step_size: 20
    lr_gamma: 0.5
    weight_decay: 0.001
    use_checkpoint: false
    compute_sens_per_coil: false
    pretrain: false
    pretrain_weights_path: null
data:
  class_path: pl_modules.DataModule
  init_args:
    slice_dataset: data.CmrxReconSliceDataset
    data_path: /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/train_15T_and_30T
    challenge: multicoil
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies:
            - 16
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
            seed: null
        uniform_resolution: null
        use_seed: false
        mask_type: null
        test_num_low_frequencies: null
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies:
            - 16
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
            seed: null
        uniform_resolution: null
        use_seed: true
        mask_type: null
        test_num_low_frequencies: null
    combine_train_val: false
    sample_rate: null
    val_sample_rate: null
    volume_sample_rate: null
    val_volume_sample_rate: null
    train_filter: null
    val_filter: null
    use_dataset_cache_file: false
    batch_size: 1
    num_workers: 4
    distributed_sampler: true
    num_adj_slices: 5
    data_balancer:
      class_path: data.BalanceSampler
      init_args:
        ratio_dict:
          T1map: 1
          T1rho: 2
          T1w: 2
          T2map: 1
          T2smap: 8
          T2w: 2
          cine_lax: 1
          lge_lax: 1
          cine_sax: 1
          lge_sax: 1
          cine_ot: 5
          cine_lvot: 8
          cine_rvot: 8
          perfusion: 2
          blackblood: 8
          flow2d: 4
    val_data_path: /common/lidxxlab/cmrchallenge/task3/val_15T
optimizer: null
lr_scheduler: null
ckpt_path: null
