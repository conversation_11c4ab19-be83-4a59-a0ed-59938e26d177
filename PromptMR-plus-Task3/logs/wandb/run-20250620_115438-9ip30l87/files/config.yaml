_instantiator:
    value: lightning.pytorch.cli.instantiate_module
_wandb:
    value:
        cli_version: 0.19.9
        m:
            - "1": val_images/field_strength_1
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": trainer/global_step
              "6":
                - 3
              "7": []
            - "1": val_images/kspace_mag_shape_3
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/field_strength_error
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/logit_mean
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/field_strength_2
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_mag_shape_4
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/field_strength_0
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": discriminator_checkpoint/accuracy
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/logit_max
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/logit_min
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/pred_prob_std
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_loss_step
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/nmse
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_mag_shape_0
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/discriminator_accuracy
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_mag_shape_2
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_mag_shape_1
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/discriminator_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": validation_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.caption
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/field_strength_4
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/domain_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/effective_alpha
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_loss_epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": discriminator_checkpoint/epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_1._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/training_phase
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/ssim
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/domain_accuracy
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.size
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1651.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_2.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_1.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/sample_0.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2.path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_2._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_3.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_941.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1862.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/grl_alpha
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_0.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/recon_loss_step
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_4.sha256
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_4._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1448.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/pred_prob_mean
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/kspace_magnitude_0.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/masked_kspace_3.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images/field_strength_3
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/logit_std
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/psnr
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_metrics/recon_loss_epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": discriminator_checkpoint/save_path
              "5": 2
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.8.20
        t:
            "1":
                - 1
                - 55
                - 106
            "2":
                - 1
                - 41
                - 55
                - 106
            "3":
                - 7
                - 13
                - 15
                - 18
                - 23
                - 55
                - 61
                - 66
            "4": 3.8.20
            "5": 0.19.9
            "8":
                - 5
            "12": 0.19.9
            "13": linux-x86_64
class_path:
    value: pl_modules.DomainAdaptMRModule
init_args:
    value:
        adaptive_input: true
        chans: 48
        compute_sens_per_coil: false
        discriminator_accuracy_threshold: 0.65
        discriminator_pretrain_epochs: 5
        domain_lambda: 0.01
        enable_mask_visualization: false
        feature_dim:
            - 72
            - 96
            - 120
        feature_extract_layers:
            - 2
            - 3
        grl_alpha_schedule: linear
        learnable_prompt: false
        len_prompt:
            - 5
            - 5
            - 5
        lr: 0.0002
        lr_gamma: 0.5
        lr_step_size: 20
        model_version: promptmr_v2
        n_bottleneck_cab: 3
        n_buffer: 4
        n_dec_cab:
            - 2
            - 2
            - 3
        n_enc_cab:
            - 2
            - 3
            - 3
        n_history: 0
        n_skip_cab:
            - 1
            - 1
            - 1
        no_use_ca: false
        num_adj_slices: 5
        num_cascades: 6
        num_log_images: 4
        pools: 4
        pretrain: false
        prompt_dim:
            - 24
            - 48
            - 72
        prompt_size:
            - 64
            - 32
            - 16
        sens_feature_dim:
            - 36
            - 48
            - 60
        sens_n_feat0: 24
        sens_prompt_dim:
            - 12
            - 24
            - 36
        use_checkpoint: false
        use_sens_adj: true
        weight_decay: 0.001
