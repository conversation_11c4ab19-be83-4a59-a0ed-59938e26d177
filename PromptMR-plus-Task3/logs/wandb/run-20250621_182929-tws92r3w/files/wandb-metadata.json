{"os": "Linux-4.18.0-348.el8.x86_64-x86_64-with-glibc2.17", "python": "CPython 3.8.20", "startedAt": "2025-06-22T01:29:29.913452Z", "args": ["fit", "--config", "/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml", "--trainer.max_epochs=50", "--data.init_args.batch_size=1", "--trainer.devices=4", "--trainer.logger.init_args.project=cmr2025_task3", "--trainer.logger.init_args.tags=[fixed_domain_adapt,hierarchical_features,BCE_with_logits,stable_accuracy]", "--trainer.logger.init_args.name=fixed_domain_adapt_1829", "--trainer.logger.init_args.offline=false", "--model.feature_extract_layers=[1, 3, 5]", "--model.enable_layer_analysis=true", "--model.lr=0.0003", "--model.discriminator_pretrain_epochs=3", "--model.discriminator_accuracy_threshold=0.65"], "program": "main.py", "codePath": "main.py", "email": "<EMAIL>", "root": "logs", "host": "esplhpc-cp075", "executable": "/home/<USER>/.conda/envs/promptmr/bin/python", "codePathLocal": "main.py", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA L40S", "gpu_count": 4, "disk": {"/": {"total": "462588203008", "used": "27842228224"}}, "memory": {"total": "1081925951488"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}], "slurm": {"cluster_name": "slurm-compbio", "conf": "/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf", "cpu_bind": "quiet,mask_cpu:0x000000000F00E100", "cpu_bind_list": "0x000000000F00E100", "cpu_bind_type": "mask_cpu:", "cpu_bind_verbose": "quiet", "cpus_on_node": "8", "cpus_per_task": "8", "distribution": "cyclic", "gpus_on_node": "4", "gtids": "0", "job_account": "user", "job_cpus_per_node": "8", "job_end_time": "**********", "job_gid": "23218", "job_gpus": "1,4,5,6", "job_id": "1811278", "job_name": "cmr_fixed_domain_adapt", "job_nodelist": "esplhpc-cp075", "job_num_nodes": "1", "job_partition": "gpu", "job_qos": "normal", "job_start_time": "**********", "job_uid": "64401", "job_user": "longz2", "jobid": "1811278", "launch_node_ipaddr": "************", "localid": "0", "mem_per_node": "102400", "nnodes": "1", "nodeid": "0", "nodelist": "esplhpc-cp075", "nprocs": "1", "ntasks": "1", "prio_process": "0", "procid": "0", "srun_comm_host": "************", "srun_comm_port": "44351", "step_gpus": "1,4,5,6", "step_id": "0", "step_launcher_port": "44351", "step_nodelist": "esplhpc-cp075", "step_num_nodes": "1", "step_num_tasks": "1", "step_tasks_per_node": "1", "stepid": "0", "submit_dir": "/common/lidxxlab/cmrchallenge/task3", "submit_host": "esplhpccompbio-lv01", "task_pid": "475397", "tasks_per_node": "1", "topology_addr": "esplhpc-cp075", "topology_addr_pattern": "node", "tres_per_task": "cpu:8", "umask": "0022"}, "cudaVersion": "12.4"}