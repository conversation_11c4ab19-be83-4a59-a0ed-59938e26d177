{"os": "Linux-4.18.0-348.el8.x86_64-x86_64-with-glibc2.17", "python": "CPython 3.8.20", "startedAt": "2025-04-23T21:13:05.925845Z", "args": ["fit", "--config", "/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/configs/train/domain-adapt/cmr25-cardiac-domain-adapt-fixed.yaml", "--trainer.max_epochs=15", "--data.init_args.batch_size=1", "--trainer.devices=4", "--trainer.logger.init_args.project=cmr2025_task3", "--trainer.logger.init_args.tags=[domain_adaptation,promptmr_plus,comparison]", "--trainer.logger.init_args.name=pmr_plus_domain_adapt_comparison-30to15", "--trainer.logger.init_args.log_model=all", "--trainer.logger.init_args.offline=false"], "program": "main.py", "codePath": "main.py", "email": "<EMAIL>", "root": "logs", "host": "esplhpc-cp075", "executable": "/home/<USER>/.conda/envs/promptmr/bin/python", "codePathLocal": "main.py", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA L40S", "gpu_count": 4, "disk": {"/": {"total": "462588203008", "used": "17172365312"}}, "memory": {"total": "1081925947392"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}, {"name": "NVIDIA L40S", "memoryTotal": "***********", "cudaCores": 18176, "architecture": "Ada"}], "slurm": {"cluster_name": "slurm-compbio", "conf": "/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf", "cpu_bind": "quiet,mask_cpu:0x0000000000000FFF", "cpu_bind_list": "0x0000000000000FFF", "cpu_bind_type": "mask_cpu:", "cpu_bind_verbose": "quiet", "cpus_on_node": "12", "cpus_per_task": "12", "distribution": "cyclic", "gpus_on_node": "4", "gtids": "0", "job_account": "user", "job_cpus_per_node": "12", "job_end_time": "**********", "job_gid": "23218", "job_gpus": "0,1,2,3", "job_id": "1464219", "job_name": "cmr_baseline", "job_nodelist": "esplhpc-cp075", "job_num_nodes": "1", "job_partition": "gpu", "job_qos": "normal", "job_start_time": "**********", "job_uid": "64401", "job_user": "longz2", "jobid": "1464219", "launch_node_ipaddr": "************", "localid": "0", "mem_per_node": "204800", "nnodes": "1", "nodeid": "0", "nodelist": "esplhpc-cp075", "nprocs": "1", "ntasks": "1", "prio_process": "0", "procid": "0", "srun_comm_host": "************", "srun_comm_port": "37163", "step_gpus": "0,1,2,3", "step_id": "0", "step_launcher_port": "37163", "step_nodelist": "esplhpc-cp075", "step_num_nodes": "1", "step_num_tasks": "1", "step_tasks_per_node": "1", "stepid": "0", "submit_dir": "/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3", "submit_host": "esplhpccompbio-lv01", "task_pid": "2282759", "tasks_per_node": "1", "topology_addr": "esplhpc-cp075", "topology_addr_pattern": "node", "tres_per_task": "cpu:12", "umask": "0022"}, "cudaVersion": "12.4"}