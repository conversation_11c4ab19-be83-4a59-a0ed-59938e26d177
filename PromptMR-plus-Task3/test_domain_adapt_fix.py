#!/usr/bin/env python3
"""
Test script to verify the domain adaptation bug fix
"""

import torch
import sys
import os

# Add the project root to the path
sys.path.append('/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3')

def test_feature_validation():
    """Test the fixed feature validation logic"""
    
    # Simulate the cascade_features structure
    # Each element is a list of 3 tensors (from different layers)
    cascade_features = []
    
    # Create 6 cascades, each with 3 feature tensors
    for i in range(6):
        cascade_feat = [
            torch.randn(1, 120, 52, 15),  # Layer 0
            torch.randn(1, 96, 104, 30),  # Layer 1  
            torch.randn(1, 72, 208, 60),  # Layer 2
        ]
        cascade_features.append(cascade_feat)
    
    print(f"Created {len(cascade_features)} cascade features")
    for i, feat in enumerate(cascade_features):
        print(f"Cascade {i}: {len(feat)} tensors")
        for j, tensor in enumerate(feat):
            print(f"  Tensor {j}: {tensor.shape}")
    
    # Test the fixed validation logic
    print("\n🔍 Testing fixed validation logic...")
    
    valid_features = []
    for f in cascade_features:
        if f is not None and isinstance(f, list):
            # Check if all tensors in the list are valid
            all_valid = True
            for tensor in f:
                if tensor is None or not torch.isfinite(tensor).all():
                    all_valid = False
                    break
            if all_valid:
                valid_features.append(f)
    
    print(f"✅ Valid features: {len(valid_features)}/{len(cascade_features)}")
    
    # Test gradient clipping logic
    print("\n🔍 Testing gradient clipping logic...")
    
    # Make tensors require gradients
    for feat_list in valid_features:
        for tensor in feat_list:
            if tensor is not None:
                tensor.requires_grad_(True)
    
    # Test gradient clipping (this should not crash)
    try:
        for feat_list in valid_features:
            for tensor in feat_list:
                if tensor is not None and tensor.requires_grad:
                    # Create some dummy gradients
                    tensor.grad = torch.randn_like(tensor)
                    torch.nn.utils.clip_grad_norm_(tensor, max_norm=1.0)
        print("✅ Gradient clipping test passed")
    except Exception as e:
        print(f"❌ Gradient clipping test failed: {e}")
        return False
    
    # Test with invalid features (NaN/Inf)
    print("\n🔍 Testing with invalid features...")
    
    # Create a cascade with NaN values
    invalid_cascade = [
        torch.randn(1, 120, 52, 15),
        torch.full((1, 96, 104, 30), float('nan')),  # NaN tensor
        torch.randn(1, 72, 208, 60),
    ]
    cascade_features_with_invalid = cascade_features + [invalid_cascade]
    
    valid_features_filtered = []
    for f in cascade_features_with_invalid:
        if f is not None and isinstance(f, list):
            all_valid = True
            for tensor in f:
                if tensor is None or not torch.isfinite(tensor).all():
                    all_valid = False
                    break
            if all_valid:
                valid_features_filtered.append(f)
    
    print(f"✅ Filtered out invalid features: {len(valid_features_filtered)}/{len(cascade_features_with_invalid)}")
    
    return True

def test_domain_adapt_import():
    """Test that we can import the domain adaptation module"""
    try:
        from models.domain_adapt import DomainAdaptationModule
        print("✅ Successfully imported DomainAdaptationModule")
        return True
    except Exception as e:
        print(f"❌ Failed to import DomainAdaptationModule: {e}")
        return False

def test_pl_module_import():
    """Test that we can import the PyTorch Lightning module"""
    try:
        from pl_modules.domain_adapt_module import PromptMRDomainAdaptModule
        print("✅ Successfully imported PromptMRDomainAdaptModule")
        return True
    except Exception as e:
        print(f"❌ Failed to import PromptMRDomainAdaptModule: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Domain Adaptation Bug Fix")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Feature validation logic
    print("\n📋 Test 1: Feature validation logic")
    if not test_feature_validation():
        all_tests_passed = False
    
    # Test 2: Import domain adaptation module
    print("\n📋 Test 2: Domain adaptation module import")
    if not test_domain_adapt_import():
        all_tests_passed = False
    
    # Test 3: Import PyTorch Lightning module
    print("\n📋 Test 3: PyTorch Lightning module import")
    if not test_pl_module_import():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! The bug fix should work correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    print("=" * 50)
