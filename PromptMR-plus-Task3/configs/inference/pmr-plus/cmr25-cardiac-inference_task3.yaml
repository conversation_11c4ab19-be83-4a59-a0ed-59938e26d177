
data:
  class_path: pl_modules.DataModule
  init_args:
    batch_size: 1
    challenge: multicoil
    combine_train_val: false
    data_balancer:
      class_path: data.BalanceSampler
      init_args:
        ratio_dict:
          cine_sax: 44,
          cine_lax_2ch: 44,
          cine_lax_3ch: 40,
          cine_lax_4ch: 48,
          T1w: 8,
          T2w: 8,
          T2w_lax_2ch: 10,
          T2w_lax_4ch: 9,
          perfusion: 9,
          lge_sax: 6,
          lge_lax_2ch: 8,
          lge_lax_4ch: 7  

    data_path: /common/lidxxlab/cmrchallenge/task3/inference_50T
    distributed_sampler: true
    num_adj_slices: 5
    slice_dataset: data.CmrxReconSliceDataset
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.subsample.CmrxRecon25MaskFunc
          init_args:
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
            num_adj_slices: 5
            num_low_frequencies:
            - 16
        uniform_resolution: null
        use_seed: true
    use_dataset_cache_file: false
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.subsample.CmrxRecon25MaskFunc
          init_args:
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
            num_adj_slices: 5
            num_low_frequencies:
            - 16
        uniform_resolution: null
        use_seed: true
        
# dataset specific settings
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp
  devices: auto
  num_nodes: 1
  logger: False
  callbacks:
    -
      class_path: __main__.CustomWriter
      init_args:
        output_dir: /common/lidxxlab/cmrchallenge/data/CMR2025/DirectInference_model2025
        write_interval: batch_and_epoch
model:
  class_path: pl_modules.PromptMrModule
  init_args:
    num_cascades: 12
    num_adj_slices: 5
    n_feat0: 48
    feature_dim:
    - 72
    - 96
    - 120
    prompt_dim:
    - 24
    - 48
    - 72
    sens_n_feat0: 24
    sens_feature_dim:
    - 36
    - 48
    - 60
    sens_prompt_dim:
    - 12
    - 24
    - 36
    len_prompt:
    - 5
    - 5
    - 5
    prompt_size:
    - 64
    - 32
    - 16
    n_enc_cab:
    - 2
    - 3
    - 3
    n_dec_cab:
    - 2
    - 2
    - 3
    n_skip_cab:
    - 1
    - 1
    - 1
    n_bottleneck_cab: 3
    no_use_ca: false
    learnable_prompt: false
    adaptive_input: true
    n_buffer: 4
    n_history: 11
    use_sens_adj: true
    model_version: promptmr_v2
    lr: 0.0002
    lr_step_size: 11
    lr_gamma: 0.1
    weight_decay: 0.01
    use_checkpoint: false
    compute_sens_per_coil: false
    num_log_images: 16

ckpt_path: /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt