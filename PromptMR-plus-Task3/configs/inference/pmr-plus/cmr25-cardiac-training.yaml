# data:
#   class_path: pl_modules.InferenceDataModule
#   init_args:
#     slice_dataset: data.CmrxReconInferenceSliceDataset
#     #data_path: /gpfs/home/<USER>/mridatasets/cmrxrecon2024/validationset/undersample
#     data_path: /common/lidxxlab/cmrchallenge/data/CMR2025/ChallengeData/MultiCoil
#     challenge: multicoil
#     test_transform:
#       class_path: data.CmrxReconDataTransform
#       init_args:
#         mask_func:
#           class_path: data.subsample.CmrxRecon25MaskFunc
#           init_args:
#             mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
#             num_adj_slices: 5
#             num_low_frequencies:
#             - 16
#         uniform_resolution: null
#         use_seed: true
#     num_adj_slices: 5
#     batch_size: 1
#     distributed_sampler: true 
#     test_filter: 
#       class_path: data.FuncFilterString
#       init_args:
#         # filter_str: Mapping/ValidationSet/UnderSample_Task1/
#         filter_str: Cine/TrainingSet/FullSample/

data:
  class_path: pl_modules.DataModule
  init_args:
    batch_size: 1
    challenge: multicoil
    combine_train_val: false
    data_balancer:
      class_path: data.BalanceSampler
      init_args:
        ratio_dict:
          T1map: 1
          T1rho: 2
          T1w: 2
          T2map: 1
          T2smap: 8
          T2w: 2
          cine_lax: 1
          lge_lax: 1
          cine_sax: 1
          lge_sax: 1
          cine_ot: 5
          cine_lvot: 8
          cine_rvot: 8
          perfusion: 2
          blackblood: 8
          flow2d: 4

    data_path: /common/lidxxlab/cmrchallenge/data/CMR2025/ProcessedDirectInference
    distributed_sampler: true
    num_adj_slices: 5
    slice_dataset: data.CmrxReconSliceDataset
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.subsample.CmrxRecon25MaskFunc
          init_args:
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Validation/Task3-S1/summary.h5
            num_adj_slices: 5
            num_low_frequencies:
            - 16
        uniform_resolution: null
        use_seed: true
    use_dataset_cache_file: false
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.subsample.CmrxRecon25MaskFunc
          init_args:
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
            num_adj_slices: 5
            num_low_frequencies:
            - 16
        uniform_resolution: null
        use_seed: true
        
# dataset specific settings
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp
  devices: auto
  num_nodes: 1
  logger: False
  callbacks:
    -
      class_path: __main__.CustomWriter
      init_args:
        output_dir: /common/lidxxlab/cmrchallenge/data/CMR2025/DirectInference_model2025
        write_interval: batch_and_epoch
model:
  class_path: pl_modules.PromptMrModule
  init_args:
    num_cascades: 12
    num_adj_slices: 5
    n_feat0: 48
    feature_dim:
    - 72
    - 96
    - 120
    prompt_dim:
    - 24
    - 48
    - 72
    sens_n_feat0: 24
    sens_feature_dim:
    - 36
    - 48
    - 60
    sens_prompt_dim:
    - 12
    - 24
    - 36
    len_prompt:
    - 5
    - 5
    - 5
    prompt_size:
    - 64
    - 32
    - 16
    n_enc_cab:
    - 2
    - 3
    - 3
    n_dec_cab:
    - 2
    - 2
    - 3
    n_skip_cab:
    - 1
    - 1
    - 1
    n_bottleneck_cab: 3
    no_use_ca: false
    learnable_prompt: false
    adaptive_input: true
    n_buffer: 4
    n_history: 11
    use_sens_adj: true
    model_version: promptmr_v2
    lr: 0.0002
    lr_step_size: 11
    lr_gamma: 0.1
    weight_decay: 0.01
    use_checkpoint: false
    compute_sens_per_coil: false
    num_log_images: 16

ckpt_path: /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt