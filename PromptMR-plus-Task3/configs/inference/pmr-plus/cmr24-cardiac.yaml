data:
  class_path: pl_modules.InferenceDataModule
  init_args:
    slice_dataset: data.CmrxReconInferenceSliceDataset
    data_path: /gpfs/home/<USER>/mridatasets/cmrxrecon2024/validationset/undersample
    challenge: multicoil
    test_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func: null
        uniform_resolution: null
        use_seed: true
        mask_type: 'cartesian_or_radial'
        test_num_low_frequencies: 16
    num_adj_slices: 5
    batch_size: 1
    distributed_sampler: true 
    test_filter: 
      class_path: data.FuncFilterString
      init_args:
        filter_str: null #Mapping/ValidationSet/UnderSample_Task1/P001/

# dataset specific settings
seed_everything: 42
trainer:
  accelerator: gpu
  strategy: ddp
  devices: 4
  num_nodes: 1
  logger: false
  callbacks:
    -
      class_path: __main__.CustomWriter
      init_args:
        output_dir: _predict/cmr24-cardiac/test_plus
        write_interval: batch_and_epoch
model:
  class_path: pl_modules.PromptMrModule

ckpt_path: weights/cmr24-cardiac/promptmr-plus-epoch=11-step=337764.ckpt