data:
  class_path: pl_modules.DataModule
  init_args:
    slice_dataset: data.CmrxReconSliceDataset
    data_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed
    challenge: multicoil
    data_balancer:
      class_path: data.BalanceSampler
      init_args: 
        ratio_dict: {
          # Adjust these categories based on CMRxRecon2025 sequence types
          'T1map': 2, 'T2map': 6, 'cine_lax': 2, 'cine_sax': 1, 
          'cine_lvot': 6, 'aorta_sag': 1, 'aorta_tra': 1, 'tagging': 1 
        }
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc  # You may need to create this class
          init_args:
            num_low_frequencies: 
            - 16
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/code/PromptMR/mask_files_required_for_training/mask_radial.h5
        uniform_resolution: null
        use_seed: false
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc  # Same mask class as above
          init_args:
            num_low_frequencies: 
            - 16
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/code/PromptMR/mask_files_required_for_training/mask_radial.h5
        uniform_resolution: null
        use_seed: True
    combine_train_val: false
    num_adj_slices: 5
    batch_size: 1
    distributed_sampler: true
    use_dataset_cache_file: false

trainer:
  strategy: ddp
  devices: auto
  num_nodes: 1
  max_epochs: 12
  logger: 
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      save_dir: exp/cmr25-cardiac/pmr-plus
      tags: [cmr25-cardiac]
      name: cmr25-cardiac-pmr-plus

model:
  class_path: pl_modules.PromptMrModule
  init_args:
    lr: 0.0002
    lr_step_size: 11

ckpt_path: null