data:
  class_path: pl_modules.DataModule
  init_args:
    # Dataset settings
    slice_dataset: data.CmrxReconSliceDataset
    data_path: /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/train_15T_and_30T
    val_data_path: /common/lidxxlab/cmrchallenge/task3/val_15T
    challenge: multicoil

    # NEW: Subset ratio for faster training
    subset_ratio: 0.33  # Use 1/3 of training data

    # Data balancing
    data_balancer:
      class_path: data.BalanceSampler
      init_args:
        ratio_dict: {
          # Adjust these categories based on CMRxRecon2025 sequence types
          'T1map': 1,
          'T1rho': 2,
          'T1w': 2,
          'T2map': 1,
          'T2smap': 8,
          'T2w': 2,
          'cine_lax': 1,
          'lge_lax': 1,
          'cine_sax': 1,
          'lge_sax': 1,
          'cine_ot': 5,
          'cine_lvot': 8,
          'cine_rvot': 8,
          'perfusion': 2,
          'blackblood': 8,
          'flow2d': 4
        }
    # Training data transformation
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies: [16]
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
        uniform_resolution: null
        use_seed: false

    # Validation data transformation
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies: [16]
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
        uniform_resolution: null
        use_seed: true

    # Other data parameters
    combine_train_val: false
    num_adj_slices: 5
    batch_size: 1
    distributed_sampler: true
    use_dataset_cache_file: false

seed_everything: 42
trainer:
  # Basic settings
  accelerator: gpu                 # Use GPU acceleration
  strategy: ddp_find_unused_parameters_true  # DDP with unused parameter detection
  devices: 4                       # Use 4 GPUs
  num_nodes: 1                     # Use 1 node
  max_epochs: 25                   # Increased for 6 epochs reconstruction + discriminator training

  # Optimization settings
  gradient_clip_val: 0.01          # Gradient clipping value
  log_every_n_steps: 50            # Log every 50 steps
  deterministic: false             # Non-deterministic training
  use_distributed_sampler: false   # Do not use distributed sampler

  # Logging settings
  logger:
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      project: cmr2025_phased      # New project name for phased training
      save_dir: logs               # Log directory
      tags: ['phased_training', 'domain_adaptation', 'promptmr_plus', 'subset_ratio', 'medium_window', '6epoch_recon']  # Updated tags
      name: pmr_plus_phased_6epoch_recon  # Updated run name

  # Callback settings
  callbacks:
    - class_path: lightning.pytorch.callbacks.ModelCheckpoint
      init_args:
        monitor: validation_loss   # Monitor validation loss
        mode: min                  # Minimize validation loss
        save_top_k: 3              # Save top 3 best models based on validation loss
        save_last: true            # Also save last checkpoint
        verbose: true              # Print checkpoint information
        filename: 'best-epoch{epoch:02d}-val{validation_loss:.4f}'  # Better filename format
    
    - class_path: pl_modules.domain_adapt_module.DiscriminatorCheckpointCallback
      init_args:
        save_dir: discriminator_checkpoints  # Directory to save discriminator weights
        threshold: 0.75  # Updated threshold for phased training

# Domain adaptation specific settings
model:
  class_path: pl_modules.DomainAdaptMRModule
  init_args:
    # Model architecture
    num_cascades: 6                # Number of cascade blocks
    chans: 48                      # Base feature channels
    pools: 4                       # Number of pooling layers
    model_version: "promptmr_v2"   # Use promptmr_v2 model for consistency with other configs

    # NEW: Phased training parameters - 6 EPOCHS RECONSTRUCTION
    pure_reconstruction_epochs: 6  # 6 epochs for pure reconstruction training
    medium_window_size: 50         # Medium window size for accuracy statistics
    stability_epochs: 2            # 2 consecutive epochs required for stability
    discriminator_accuracy_threshold: 0.75  # Standard threshold for discriminator training

    # Domain adaptation parameters
    domain_lambda: 0.02            # Start with small value for stability
    feature_extract_layers: [1, 3, 5]  # Use ALL layers for analysis
    grl_alpha_schedule: 'cosine'   # Cosine schedule for smoother transitions

    # Layer analysis parameters
    enable_layer_analysis: true    # Enable real-time layer analysis

    # Other parameters
    enable_mask_visualization: false  # Disable mask visualization
    num_log_images: 4              # Reduced for faster logging

    # Learning rate parameters
    lr: 0.0003                     # Learning rate
    lr_step_size: 20               # Learning rate step size
    lr_gamma: 0.5                  # Learning rate gamma
    weight_decay: 0.001            # Weight decay

ckpt_path: null 