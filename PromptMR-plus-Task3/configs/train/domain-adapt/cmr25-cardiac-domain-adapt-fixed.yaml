data:
  class_path: pl_modules.DataModule
  init_args:
    # Dataset settings
    slice_dataset: data.CmrxReconSliceDataset
    data_path: /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/train_15T_and_30T
    val_data_path: /common/lidxxlab/cmrchallenge/task3/val_15T
    challenge: multicoil

    # Data balancing
    data_balancer:
      class_path: data.BalanceSampler
      init_args:
        ratio_dict: {
          # Adjust these categories based on CMRxRecon2025 sequence types
          'T1map': 1,
          'T1rho': 2,
          'T1w': 2,
          'T2map': 1,
          'T2smap': 8,
          'T2w': 2,
          'cine_lax': 1,
          'lge_lax': 1,
          'cine_sax': 1,
          'lge_sax': 1,
          'cine_ot': 5,
          'cine_lvot': 8,
          'cine_rvot': 8,
          'perfusion': 2,
          'blackblood': 8,
          'flow2d': 4
        }
    # Training data transformation
    train_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies: [16]
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
        uniform_resolution: null
        use_seed: false

    # Validation data transformation
    val_transform:
      class_path: data.CmrxReconDataTransform
      init_args:
        mask_func:
          class_path: data.CmrxRecon25MaskFunc
          init_args:
            num_low_frequencies: [16]
            num_adj_slices: 5
            mask_path: /common/lidxxlab/cmrchallenge/data/CMR2025/Processed/Mask/summary.h5
        uniform_resolution: null
        use_seed: true

    # Other data parameters
    combine_train_val: false
    num_adj_slices: 5
    batch_size: 1
    distributed_sampler: true
    use_dataset_cache_file: false

seed_everything: 42
trainer:
  # Basic settings
  accelerator: gpu                 # Use GPU acceleration
  strategy: ddp_find_unused_parameters_true  # DDP with unused parameter detection
  devices: 4                       # Use 4 GPUs
  num_nodes: 1                     # Use 1 node
  max_epochs: 15                   # Increased from 10 to 15 epochs for longer pretrain (5 epochs) + adversarial training

  # Optimization settings
  gradient_clip_val: 0.01          # Gradient clipping value
  log_every_n_steps: 50            # Log every 50 steps
  deterministic: false             # Non-deterministic training
  use_distributed_sampler: false   # Do not use distributed sampler

  # Logging settings
  logger:
    class_path: lightning.pytorch.loggers.WandbLogger
    init_args:
      project: cmr2025_extended     # Changed project name for new run
      save_dir: logs               # Log directory
      tags: ['domain_adaptation', 'promptmr_plus', '1.5T_and_3.0T', 'layer_analysis', 'improved']  # Updated tags
      name: pmr_plus_domain_adapt_layer_analysis  # Updated run name

  # Callback settings
  callbacks:
    - class_path: lightning.pytorch.callbacks.ModelCheckpoint
      init_args:
        monitor: validation_loss   # Monitor validation loss
        mode: min                  # Minimize validation loss
        save_top_k: 3              # Save top 3 best models based on validation loss
        save_last: true            # Also save last checkpoint
        verbose: true              # Print checkpoint information
        filename: 'best-epoch{epoch:02d}-val{validation_loss:.4f}'  # Better filename format
    
    - class_path: pl_modules.domain_adapt_module.DiscriminatorCheckpointCallback
      init_args:
        save_dir: discriminator_checkpoints  # Directory to save discriminator weights
        threshold: 0.65  # Accuracy threshold for saving discriminator

# Domain adaptation specific settings
model:
  class_path: pl_modules.DomainAdaptMRModule
  init_args:
    # Model architecture
    num_cascades: 6                # Number of cascade blocks
    chans: 48                      # Base feature channels
    pools: 4                       # Number of pooling layers
    model_version: "promptmr"      # Use original promptmr model that returns intermediate features

    # Domain adaptation parameters - IMPROVED SETTINGS
    domain_lambda: 0.01            # Keep reduced for stability
    feature_extract_layers: [0, 1, 2, 3, 4, 5]  # Use ALL layers for analysis
    grl_alpha_schedule: 'linear'     # Keep linear for predictable behavior

    # Two-stage training parameters - IMPROVED SETTINGS
    discriminator_pretrain_epochs: 5    # Keep 5 epochs for better discriminator learning
    discriminator_accuracy_threshold: 0.65  # Keep threshold at 0.65 for strong discriminator

    # NEW: Layer analysis parameters
    enable_layer_analysis: true    # Enable real-time layer analysis

    # Other parameters
    enable_mask_visualization: false  # Disable mask visualization
    num_log_images: 4              # Reduced from 16 to 4 for faster logging

    # Learning rate parameters - IMPROVED FOR BETTER LEARNING
    lr: 0.0003                     # Increased from 0.0002 to 0.0003 for faster learning
    lr_step_size: 20               # Keep the same
    lr_gamma: 0.5                  # Keep the same
    weight_decay: 0.001            # Keep the same

ckpt_path: null
